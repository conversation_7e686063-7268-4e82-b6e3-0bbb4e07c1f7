# Environment Variables Example
# Copy this file to .env.local and fill in your actual values

# Site Configuration
NEXT_PUBLIC_SITE_URL=https://setmee.com
NEXT_PUBLIC_SITE_NAME=Setmee

# Make.com Webhook URLs for different form types
NEXT_PUBLIC_MAKE_CONTACT_WEBHOOK=https://hook.eu1.make.com/your-contact-webhook-id
NEXT_PUBLIC_MAKE_NEWSLETTER_WEBHOOK=https://hook.eu1.make.com/your-newsletter-webhook-id
NEXT_PUBLIC_MAKE_DEMO_WEBHOOK=https://hook.eu1.make.com/your-demo-webhook-id
NEXT_PUBLIC_MAKE_AUDIT_WEBHOOK=https://hook.eu1.make.com/your-audit-webhook-id
NEXT_PUBLIC_MAKE_CONSULTATION_WEBHOOK=https://hook.eu1.make.com/your-consultation-webhook-id

# Legacy Form Services (optional, for backup)
# Formspree
NEXT_PUBLIC_FORMSPREE_FORM_ID=your_formspree_form_id

# EmailJS
NEXT_PUBLIC_EMAILJS_SERVICE_ID=your_emailjs_service_id
NEXT_PUBLIC_EMAILJS_TEMPLATE_ID=your_emailjs_template_id
NEXT_PUBLIC_EMAILJS_PUBLIC_KEY=your_emailjs_public_key

# Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
NEXT_PUBLIC_GTM_ID=GTM-XXXXXXX

# SEO
NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION=your_google_verification_code
NEXT_PUBLIC_YANDEX_VERIFICATION=your_yandex_verification_code

# Social Media
NEXT_PUBLIC_TWITTER_HANDLE=@setmee
NEXT_PUBLIC_LINKEDIN_URL=https://www.linkedin.com/company/setmee

# Contact Information
NEXT_PUBLIC_CONTACT_EMAIL=<EMAIL>
NEXT_PUBLIC_CONTACT_PHONE=******-123-4567

# Development
NODE_ENV=development
