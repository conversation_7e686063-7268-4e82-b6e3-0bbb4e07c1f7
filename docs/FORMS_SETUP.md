# Настройка форм с Make.com

## 🎯 Обзор системы

Система поддерживает 5 типов форм, каждая отправляет данные на свой webhook в Make.com:

1. **Contact** - основная контактная форма
2. **Newsletter** - подписка на новости/презентацию  
3. **Demo** - запрос демонстрации
4. **Audit** - запрос аудита CRM
5. **Consultation** - запрос консультации эксперта

## 🔧 Настройка Make.com

### Шаг 1: Создание webhook'ов в Make

1. Зайдите в [Make.com](https://make.com)
2. Создайте новый сценарий
3. Добавьте модуль **"Webhooks" → "Custom webhook"**
4. Скопируйте URL webhook'а
5. Повторите для каждого типа формы

### Шаг 2: Настройка переменных окружения

Скопируйте `.env.example` в `.env.local` и заполните URL'ы:

```bash
# Основные webhook'и
NEXT_PUBLIC_MAKE_CONTACT_WEBHOOK=https://hook.eu1.make.com/ваш-contact-webhook-id
NEXT_PUBLIC_MAKE_NEWSLETTER_WEBHOOK=https://hook.eu1.make.com/ваш-newsletter-webhook-id
NEXT_PUBLIC_MAKE_DEMO_WEBHOOK=https://hook.eu1.make.com/ваш-demo-webhook-id
NEXT_PUBLIC_MAKE_AUDIT_WEBHOOK=https://hook.eu1.make.com/ваш-audit-webhook-id
NEXT_PUBLIC_MAKE_CONSULTATION_WEBHOOK=https://hook.eu1.make.com/ваш-consultation-webhook-id
```

## 📋 Структура данных

### Contact Form
```json
{
  "formType": "contact",
  "name": "Иван Иванов",
  "email": "<EMAIL>",
  "phone": "****** 123-45-67",
  "company": "ООО Компания",
  "message": "Интересует внедрение Kommo",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "source": "setmee-website"
}
```

### Newsletter Form
```json
{
  "formType": "newsletter",
  "email": "<EMAIL>",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "source": "setmee-website"
}
```

### Demo Request
```json
{
  "formType": "demo",
  "name": "Петр Петров",
  "email": "<EMAIL>",
  "company": "ИП Петров",
  "phone": "****** 987-65-43",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "source": "setmee-website"
}
```

## 🚀 Использование в коде

### Отправка формы
```typescript
import { submitForm } from '@/lib/form-service';

// Универсальная функция
const result = await submitForm('contact', {
  name: 'Иван',
  email: '<EMAIL>',
  message: 'Привет!'
});

// Специализированные функции
import { 
  submitContactForm, 
  submitEmailSubscription,
  submitDemoRequest 
} from '@/lib/form-service';

const contactResult = await submitContactForm(contactData);
const newsletterResult = await submitEmailSubscription({ email });
const demoResult = await submitDemoRequest(demoData);
```

### Добавление новой формы

1. **Добавьте конфигурацию** в `src/config/forms.ts`:
```typescript
newFormType: {
  webhookUrl: process.env.NEXT_PUBLIC_MAKE_NEW_WEBHOOK,
  successMessage: 'Спасибо! Мы свяжемся с вами.',
  errorMessage: 'Ошибка отправки. Попробуйте еще раз.',
  fields: ['name', 'email', 'customField'],
  type: 'newFormType'
}
```

2. **Добавьте переменную** в `.env.local`:
```bash
NEXT_PUBLIC_MAKE_NEW_WEBHOOK=https://hook.eu1.make.com/ваш-новый-webhook
```

3. **Используйте в компоненте**:
```typescript
const result = await submitForm('newFormType', formData);
```

## 🔍 Отладка

- Если webhook не настроен, данные логируются в консоль
- Проверьте Network tab в DevTools для отладки запросов
- Убедитесь, что переменные окружения загружены правильно

## 📈 Мониторинг

В Make.com вы можете:
- Просматривать историю выполнения
- Настроить уведомления об ошибках
- Добавить интеграции с CRM, email, Slack и т.д.
