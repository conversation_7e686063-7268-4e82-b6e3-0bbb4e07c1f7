[{"/Users/<USER>/Documents/SetMee/src/app/contact/layout.tsx": "1", "/Users/<USER>/Documents/SetMee/src/app/contact/page.tsx": "2", "/Users/<USER>/Documents/SetMee/src/app/layout.tsx": "3", "/Users/<USER>/Documents/SetMee/src/app/page.tsx": "4", "/Users/<USER>/Documents/SetMee/src/app/sitemap.ts": "5", "/Users/<USER>/Documents/SetMee/src/components/StructuredData.tsx": "6", "/Users/<USER>/Documents/SetMee/src/components/layout/Footer.tsx": "7", "/Users/<USER>/Documents/SetMee/src/components/layout/Header.tsx": "8", "/Users/<USER>/Documents/SetMee/src/components/layout/index.ts": "9", "/Users/<USER>/Documents/SetMee/src/components/sections/AboutSection.tsx": "10", "/Users/<USER>/Documents/SetMee/src/components/sections/AuditSection.tsx": "11", "/Users/<USER>/Documents/SetMee/src/components/sections/BusinessVerticalsSection.tsx": "12", "/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx": "13", "/Users/<USER>/Documents/SetMee/src/components/sections/ContactSection.tsx": "14", "/Users/<USER>/Documents/SetMee/src/components/sections/CustomIntegrationsSection.tsx": "15", "/Users/<USER>/Documents/SetMee/src/components/sections/FeaturesSection.tsx": "16", "/Users/<USER>/Documents/SetMee/src/components/sections/HeroSection.tsx": "17", "/Users/<USER>/Documents/SetMee/src/components/sections/ImplementationProcessSection.tsx": "18", "/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx": "19", "/Users/<USER>/Documents/SetMee/src/components/sections/OurServicesSection.tsx": "20", "/Users/<USER>/Documents/SetMee/src/components/sections/WhyKommoSection.tsx": "21", "/Users/<USER>/Documents/SetMee/src/components/ui/Button.tsx": "22", "/Users/<USER>/Documents/SetMee/src/components/ui/Card.tsx": "23", "/Users/<USER>/Documents/SetMee/src/components/ui/Container.tsx": "24", "/Users/<USER>/Documents/SetMee/src/components/ui/Section.tsx": "25", "/Users/<USER>/Documents/SetMee/src/components/ui/SectionHeader.tsx": "26", "/Users/<USER>/Documents/SetMee/src/components/ui/index.ts": "27", "/Users/<USER>/Documents/SetMee/src/data/business-verticals.ts": "28", "/Users/<USER>/Documents/SetMee/src/data/content.ts": "29", "/Users/<USER>/Documents/SetMee/src/data/design-tokens.ts": "30", "/Users/<USER>/Documents/SetMee/src/data/navigation.ts": "31", "/Users/<USER>/Documents/SetMee/src/data/services.ts": "32", "/Users/<USER>/Documents/SetMee/src/lib/form-service.ts": "33", "/Users/<USER>/Documents/SetMee/src/lib/utils.ts": "34", "/Users/<USER>/Documents/SetMee/src/lib/validations.ts": "35", "/Users/<USER>/Documents/SetMee/src/types/index.ts": "36"}, {"size": 783, "mtime": 1751981642643, "results": "37", "hashOfConfig": "38"}, {"size": 13260, "mtime": 1751981722348, "results": "39", "hashOfConfig": "38"}, {"size": 2496, "mtime": 1751991791280, "results": "40", "hashOfConfig": "38"}, {"size": 1297, "mtime": 1751990907104, "results": "41", "hashOfConfig": "38"}, {"size": 410, "mtime": 1751981226549, "results": "42", "hashOfConfig": "38"}, {"size": 3207, "mtime": 1751981289619, "results": "43", "hashOfConfig": "38"}, {"size": 1103, "mtime": 1751980187696, "results": "44", "hashOfConfig": "38"}, {"size": 3738, "mtime": 1751991223930, "results": "45", "hashOfConfig": "38"}, {"size": 92, "mtime": 1751980204586, "results": "46", "hashOfConfig": "38"}, {"size": 4800, "mtime": 1751981809464, "results": "47", "hashOfConfig": "38"}, {"size": 3855, "mtime": 1751980531764, "results": "48", "hashOfConfig": "38"}, {"size": 7493, "mtime": 1751981679698, "results": "49", "hashOfConfig": "38"}, {"size": 12948, "mtime": 1751991791294, "results": "50", "hashOfConfig": "38"}, {"size": 5169, "mtime": 1751980879732, "results": "51", "hashOfConfig": "38"}, {"size": 9058, "mtime": 1751981822613, "results": "52", "hashOfConfig": "38"}, {"size": 5720, "mtime": 1751980709715, "results": "53", "hashOfConfig": "38"}, {"size": 5309, "mtime": 1751985340324, "results": "54", "hashOfConfig": "38"}, {"size": 3434, "mtime": 1751981836486, "results": "55", "hashOfConfig": "38"}, {"size": 5939, "mtime": 1751989578953, "results": "56", "hashOfConfig": "38"}, {"size": 1653, "mtime": 1751983104860, "results": "57", "hashOfConfig": "38"}, {"size": 2751, "mtime": 1751989403920, "results": "58", "hashOfConfig": "38"}, {"size": 2430, "mtime": 1751991252330, "results": "59", "hashOfConfig": "38"}, {"size": 1097, "mtime": 1751980087380, "results": "60", "hashOfConfig": "38"}, {"size": 627, "mtime": 1751980067187, "results": "61", "hashOfConfig": "38"}, {"size": 1089, "mtime": 1751980076140, "results": "62", "hashOfConfig": "38"}, {"size": 991, "mtime": 1751980096087, "results": "63", "hashOfConfig": "38"}, {"size": 248, "mtime": 1751980102041, "results": "64", "hashOfConfig": "38"}, {"size": 1671, "mtime": 1751980018666, "results": "65", "hashOfConfig": "38"}, {"size": 5547, "mtime": 1751981864425, "results": "66", "hashOfConfig": "38"}, {"size": 3381, "mtime": 1751979973097, "results": "67", "hashOfConfig": "38"}, {"size": 373, "mtime": 1751991276385, "results": "68", "hashOfConfig": "38"}, {"size": 638, "mtime": 1751979827239, "results": "69", "hashOfConfig": "38"}, {"size": 2513, "mtime": 1751981055113, "results": "70", "hashOfConfig": "38"}, {"size": 169, "mtime": 1751980052785, "results": "71", "hashOfConfig": "38"}, {"size": 893, "mtime": 1751981030050, "results": "72", "hashOfConfig": "38"}, {"size": 814, "mtime": 1751979813728, "results": "73", "hashOfConfig": "38"}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "959dgp", {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/SetMee/src/app/contact/layout.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/app/contact/page.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/app/layout.tsx", ["182"], [], "/Users/<USER>/Documents/SetMee/src/app/page.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/app/sitemap.ts", [], [], "/Users/<USER>/Documents/SetMee/src/components/StructuredData.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/layout/Footer.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/layout/index.ts", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/AboutSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/AuditSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/BusinessVerticalsSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx", ["183", "184", "185", "186"], [], "/Users/<USER>/Documents/SetMee/src/components/sections/ContactSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/CustomIntegrationsSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/FeaturesSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/HeroSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/ImplementationProcessSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/OurServicesSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/WhyKommoSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/ui/Button.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/ui/Card.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/ui/Container.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/ui/Section.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/ui/SectionHeader.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/ui/index.ts", [], [], "/Users/<USER>/Documents/SetMee/src/data/business-verticals.ts", [], [], "/Users/<USER>/Documents/SetMee/src/data/content.ts", [], [], "/Users/<USER>/Documents/SetMee/src/data/design-tokens.ts", [], [], "/Users/<USER>/Documents/SetMee/src/data/navigation.ts", [], [], "/Users/<USER>/Documents/SetMee/src/data/services.ts", [], [], "/Users/<USER>/Documents/SetMee/src/lib/form-service.ts", [], [], "/Users/<USER>/Documents/SetMee/src/lib/utils.ts", [], [], "/Users/<USER>/Documents/SetMee/src/lib/validations.ts", [], [], "/Users/<USER>/Documents/SetMee/src/types/index.ts", [], [], {"ruleId": "187", "severity": 2, "message": "188", "line": 1, "column": 25, "nodeType": null, "messageId": "189", "endLine": 1, "endColumn": 33}, {"ruleId": "187", "severity": 2, "message": "190", "line": 6, "column": 8, "nodeType": null, "messageId": "189", "endLine": 6, "endColumn": 13}, {"ruleId": "187", "severity": 2, "message": "191", "line": 15, "column": 24, "nodeType": null, "messageId": "189", "endLine": 15, "endColumn": 39}, {"ruleId": "192", "severity": 1, "message": "193", "line": 148, "column": 17, "nodeType": "194", "endLine": 152, "endColumn": 19}, {"ruleId": "192", "severity": 1, "message": "193", "line": 156, "column": 15, "nodeType": "194", "endLine": 162, "endColumn": 17}, "@typescript-eslint/no-unused-vars", "'Viewport' is defined but never used.", "unusedVar", "'Image' is defined but never used.", "'setPreviewImage' is assigned a value but never used.", "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement"]