[{"/Users/<USER>/Documents/SetMee/src/app/contact/layout.tsx": "1", "/Users/<USER>/Documents/SetMee/src/app/contact/page.tsx": "2", "/Users/<USER>/Documents/SetMee/src/app/layout.tsx": "3", "/Users/<USER>/Documents/SetMee/src/app/page.tsx": "4", "/Users/<USER>/Documents/SetMee/src/app/sitemap.ts": "5", "/Users/<USER>/Documents/SetMee/src/components/StructuredData.tsx": "6", "/Users/<USER>/Documents/SetMee/src/components/layout/Footer.tsx": "7", "/Users/<USER>/Documents/SetMee/src/components/layout/Header.tsx": "8", "/Users/<USER>/Documents/SetMee/src/components/layout/index.ts": "9", "/Users/<USER>/Documents/SetMee/src/components/sections/AboutSection.tsx": "10", "/Users/<USER>/Documents/SetMee/src/components/sections/AuditSection.tsx": "11", "/Users/<USER>/Documents/SetMee/src/components/sections/BusinessVerticalsSection.tsx": "12", "/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx": "13", "/Users/<USER>/Documents/SetMee/src/components/sections/ContactSection.tsx": "14", "/Users/<USER>/Documents/SetMee/src/components/sections/CustomIntegrationsSection.tsx": "15", "/Users/<USER>/Documents/SetMee/src/components/sections/FeaturesSection.tsx": "16", "/Users/<USER>/Documents/SetMee/src/components/sections/HeroSection.tsx": "17", "/Users/<USER>/Documents/SetMee/src/components/sections/ImplementationProcessSection.tsx": "18", "/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx": "19", "/Users/<USER>/Documents/SetMee/src/components/sections/OurServicesSection.tsx": "20", "/Users/<USER>/Documents/SetMee/src/components/sections/WhyKommoSection.tsx": "21", "/Users/<USER>/Documents/SetMee/src/components/ui/Button.tsx": "22", "/Users/<USER>/Documents/SetMee/src/components/ui/Card.tsx": "23", "/Users/<USER>/Documents/SetMee/src/components/ui/Container.tsx": "24", "/Users/<USER>/Documents/SetMee/src/components/ui/Section.tsx": "25", "/Users/<USER>/Documents/SetMee/src/components/ui/SectionHeader.tsx": "26", "/Users/<USER>/Documents/SetMee/src/components/ui/index.ts": "27", "/Users/<USER>/Documents/SetMee/src/data/business-verticals.ts": "28", "/Users/<USER>/Documents/SetMee/src/data/content.ts": "29", "/Users/<USER>/Documents/SetMee/src/data/design-tokens.ts": "30", "/Users/<USER>/Documents/SetMee/src/data/navigation.ts": "31", "/Users/<USER>/Documents/SetMee/src/data/services.ts": "32", "/Users/<USER>/Documents/SetMee/src/lib/form-service.ts": "33", "/Users/<USER>/Documents/SetMee/src/lib/utils.ts": "34", "/Users/<USER>/Documents/SetMee/src/lib/validations.ts": "35", "/Users/<USER>/Documents/SetMee/src/types/index.ts": "36", "/Users/<USER>/Documents/SetMee/src/components/ui/ScrollToTop.tsx": "37"}, {"size": 783, "mtime": 1751981642643, "results": "38", "hashOfConfig": "39"}, {"size": 13261, "mtime": 1751994851147, "results": "40", "hashOfConfig": "39"}, {"size": 2558, "mtime": 1751994863461, "results": "41", "hashOfConfig": "39"}, {"size": 1297, "mtime": 1751990907104, "results": "42", "hashOfConfig": "39"}, {"size": 410, "mtime": 1751981226549, "results": "43", "hashOfConfig": "39"}, {"size": 3207, "mtime": 1751981289619, "results": "44", "hashOfConfig": "39"}, {"size": 1469, "mtime": 1751992666234, "results": "45", "hashOfConfig": "39"}, {"size": 3728, "mtime": 1751993815798, "results": "46", "hashOfConfig": "39"}, {"size": 92, "mtime": 1751980204586, "results": "47", "hashOfConfig": "39"}, {"size": 4801, "mtime": 1751994934189, "results": "48", "hashOfConfig": "39"}, {"size": 19717, "mtime": 1751995451425, "results": "49", "hashOfConfig": "39"}, {"size": 7493, "mtime": 1751981679698, "results": "50", "hashOfConfig": "39"}, {"size": 12530, "mtime": 1751995646265, "results": "51", "hashOfConfig": "39"}, {"size": 5170, "mtime": 1751994840530, "results": "52", "hashOfConfig": "39"}, {"size": 9058, "mtime": 1751981822613, "results": "53", "hashOfConfig": "39"}, {"size": 5720, "mtime": 1751980709715, "results": "54", "hashOfConfig": "39"}, {"size": 5663, "mtime": 1751994830187, "results": "55", "hashOfConfig": "39"}, {"size": 11043, "mtime": 1751995473604, "results": "56", "hashOfConfig": "39"}, {"size": 18193, "mtime": 1751995667644, "results": "57", "hashOfConfig": "39"}, {"size": 1653, "mtime": 1751983104860, "results": "58", "hashOfConfig": "39"}, {"size": 2743, "mtime": 1751995677976, "results": "59", "hashOfConfig": "39"}, {"size": 2430, "mtime": 1751991252330, "results": "60", "hashOfConfig": "39"}, {"size": 1097, "mtime": 1751980087380, "results": "61", "hashOfConfig": "39"}, {"size": 627, "mtime": 1751980067187, "results": "62", "hashOfConfig": "39"}, {"size": 1089, "mtime": 1751980076140, "results": "63", "hashOfConfig": "39"}, {"size": 991, "mtime": 1751980096087, "results": "64", "hashOfConfig": "39"}, {"size": 304, "mtime": 1751993088077, "results": "65", "hashOfConfig": "39"}, {"size": 1671, "mtime": 1751980018666, "results": "66", "hashOfConfig": "39"}, {"size": 5554, "mtime": 1751995075871, "results": "67", "hashOfConfig": "39"}, {"size": 3381, "mtime": 1751979973097, "results": "68", "hashOfConfig": "39"}, {"size": 373, "mtime": 1751991276385, "results": "69", "hashOfConfig": "39"}, {"size": 638, "mtime": 1751979827239, "results": "70", "hashOfConfig": "39"}, {"size": 2513, "mtime": 1751981055113, "results": "71", "hashOfConfig": "39"}, {"size": 169, "mtime": 1751980052785, "results": "72", "hashOfConfig": "39"}, {"size": 893, "mtime": 1751981030050, "results": "73", "hashOfConfig": "39"}, {"size": 814, "mtime": 1751979813728, "results": "74", "hashOfConfig": "39"}, {"size": 1869, "mtime": 1751993074458, "results": "75", "hashOfConfig": "39"}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "959dgp", {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/SetMee/src/app/contact/layout.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/app/contact/page.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/app/page.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/app/sitemap.ts", [], [], "/Users/<USER>/Documents/SetMee/src/components/StructuredData.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/layout/Footer.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/layout/index.ts", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/AboutSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/AuditSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/BusinessVerticalsSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/ContactSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/CustomIntegrationsSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/FeaturesSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/HeroSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/ImplementationProcessSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/OurServicesSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/WhyKommoSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/ui/Button.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/ui/Card.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/ui/Container.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/ui/Section.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/ui/SectionHeader.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/ui/index.ts", [], [], "/Users/<USER>/Documents/SetMee/src/data/business-verticals.ts", [], [], "/Users/<USER>/Documents/SetMee/src/data/content.ts", [], [], "/Users/<USER>/Documents/SetMee/src/data/design-tokens.ts", [], [], "/Users/<USER>/Documents/SetMee/src/data/navigation.ts", [], [], "/Users/<USER>/Documents/SetMee/src/data/services.ts", [], [], "/Users/<USER>/Documents/SetMee/src/lib/form-service.ts", [], [], "/Users/<USER>/Documents/SetMee/src/lib/utils.ts", [], [], "/Users/<USER>/Documents/SetMee/src/lib/validations.ts", [], [], "/Users/<USER>/Documents/SetMee/src/types/index.ts", [], [], "/Users/<USER>/Documents/SetMee/src/components/ui/ScrollToTop.tsx", [], []]