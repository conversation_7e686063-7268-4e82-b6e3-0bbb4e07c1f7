[{"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx": "1", "/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx": "2", "/Users/<USER>/Documents/SetMee/src/app/admin/blog/page.tsx": "3", "/Users/<USER>/Documents/SetMee/src/app/admin/login/page.tsx": "4", "/Users/<USER>/Documents/SetMee/src/app/admin/media/page.tsx": "5", "/Users/<USER>/Documents/SetMee/src/app/admin/page.tsx": "6", "/Users/<USER>/Documents/SetMee/src/app/admin/pages/page.tsx": "7", "/Users/<USER>/Documents/SetMee/src/app/admin/settings/page.tsx": "8", "/Users/<USER>/Documents/SetMee/src/app/contact/layout.tsx": "9", "/Users/<USER>/Documents/SetMee/src/app/contact/page.tsx": "10", "/Users/<USER>/Documents/SetMee/src/app/layout.tsx": "11", "/Users/<USER>/Documents/SetMee/src/app/page.tsx": "12", "/Users/<USER>/Documents/SetMee/src/app/sitemap.ts": "13", "/Users/<USER>/Documents/SetMee/src/app/test-forms/page.tsx": "14", "/Users/<USER>/Documents/SetMee/src/components/StructuredData.tsx": "15", "/Users/<USER>/Documents/SetMee/src/components/admin/AdminLayout.tsx": "16", "/Users/<USER>/Documents/SetMee/src/components/admin/AuthGuard.tsx": "17", "/Users/<USER>/Documents/SetMee/src/components/layout/Footer.tsx": "18", "/Users/<USER>/Documents/SetMee/src/components/layout/Header.tsx": "19", "/Users/<USER>/Documents/SetMee/src/components/layout/index.ts": "20", "/Users/<USER>/Documents/SetMee/src/components/sections/AboutSection.tsx": "21", "/Users/<USER>/Documents/SetMee/src/components/sections/AuditSection.tsx": "22", "/Users/<USER>/Documents/SetMee/src/components/sections/BusinessVerticalsSection.tsx": "23", "/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx": "24", "/Users/<USER>/Documents/SetMee/src/components/sections/ContactSection.tsx": "25", "/Users/<USER>/Documents/SetMee/src/components/sections/CustomIntegrationsSection.tsx": "26", "/Users/<USER>/Documents/SetMee/src/components/sections/FeaturesSection.tsx": "27", "/Users/<USER>/Documents/SetMee/src/components/sections/HeroSection.tsx": "28", "/Users/<USER>/Documents/SetMee/src/components/sections/ImplementationProcessSection.tsx": "29", "/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx": "30", "/Users/<USER>/Documents/SetMee/src/components/sections/OurServicesSection.tsx": "31", "/Users/<USER>/Documents/SetMee/src/components/sections/WhyKommoSection.tsx": "32", "/Users/<USER>/Documents/SetMee/src/components/ui/Button.tsx": "33", "/Users/<USER>/Documents/SetMee/src/components/ui/Card.tsx": "34", "/Users/<USER>/Documents/SetMee/src/components/ui/Container.tsx": "35", "/Users/<USER>/Documents/SetMee/src/components/ui/ScrollToTop.tsx": "36", "/Users/<USER>/Documents/SetMee/src/components/ui/Section.tsx": "37", "/Users/<USER>/Documents/SetMee/src/components/ui/SectionHeader.tsx": "38", "/Users/<USER>/Documents/SetMee/src/components/ui/index.ts": "39", "/Users/<USER>/Documents/SetMee/src/config/forms.ts": "40", "/Users/<USER>/Documents/SetMee/src/data/business-verticals.ts": "41", "/Users/<USER>/Documents/SetMee/src/data/content.ts": "42", "/Users/<USER>/Documents/SetMee/src/data/design-tokens.ts": "43", "/Users/<USER>/Documents/SetMee/src/data/navigation.ts": "44", "/Users/<USER>/Documents/SetMee/src/data/services.ts": "45", "/Users/<USER>/Documents/SetMee/src/lib/cms-utils.ts": "46", "/Users/<USER>/Documents/SetMee/src/lib/form-service.ts": "47", "/Users/<USER>/Documents/SetMee/src/lib/utils.ts": "48", "/Users/<USER>/Documents/SetMee/src/lib/validations.ts": "49", "/Users/<USER>/Documents/SetMee/src/types/cms.ts": "50", "/Users/<USER>/Documents/SetMee/src/types/index.ts": "51"}, {"size": 13139, "mtime": 1752004309419, "results": "52", "hashOfConfig": "53"}, {"size": 11802, "mtime": 1752004355775, "results": "54", "hashOfConfig": "53"}, {"size": 9865, "mtime": 1752004039816, "results": "55", "hashOfConfig": "53"}, {"size": 5731, "mtime": 1752000834694, "results": "56", "hashOfConfig": "53"}, {"size": 9114, "mtime": 1752004102652, "results": "57", "hashOfConfig": "53"}, {"size": 12542, "mtime": 1752003809067, "results": "58", "hashOfConfig": "53"}, {"size": 13893, "mtime": 1752004407003, "results": "59", "hashOfConfig": "53"}, {"size": 11412, "mtime": 1752003074019, "results": "60", "hashOfConfig": "53"}, {"size": 783, "mtime": 1751981642643, "results": "61", "hashOfConfig": "53"}, {"size": 13261, "mtime": 1751994851147, "results": "62", "hashOfConfig": "53"}, {"size": 2558, "mtime": 1751994863461, "results": "63", "hashOfConfig": "53"}, {"size": 1297, "mtime": 1751990907104, "results": "64", "hashOfConfig": "53"}, {"size": 410, "mtime": 1751981226549, "results": "65", "hashOfConfig": "53"}, {"size": 11302, "mtime": 1751998772173, "results": "66", "hashOfConfig": "53"}, {"size": 3207, "mtime": 1751981289619, "results": "67", "hashOfConfig": "53"}, {"size": 8237, "mtime": 1752002734497, "results": "68", "hashOfConfig": "53"}, {"size": 1645, "mtime": 1752000847024, "results": "69", "hashOfConfig": "53"}, {"size": 1469, "mtime": 1751992666234, "results": "70", "hashOfConfig": "53"}, {"size": 4386, "mtime": 1751997445409, "results": "71", "hashOfConfig": "53"}, {"size": 92, "mtime": 1751980204586, "results": "72", "hashOfConfig": "53"}, {"size": 4801, "mtime": 1751994934189, "results": "73", "hashOfConfig": "53"}, {"size": 19717, "mtime": 1751995451425, "results": "74", "hashOfConfig": "53"}, {"size": 7493, "mtime": 1751981679698, "results": "75", "hashOfConfig": "53"}, {"size": 12530, "mtime": 1751995646265, "results": "76", "hashOfConfig": "53"}, {"size": 5170, "mtime": 1751994840530, "results": "77", "hashOfConfig": "53"}, {"size": 9058, "mtime": 1751981822613, "results": "78", "hashOfConfig": "53"}, {"size": 5720, "mtime": 1751980709715, "results": "79", "hashOfConfig": "53"}, {"size": 5663, "mtime": 1751994830187, "results": "80", "hashOfConfig": "53"}, {"size": 11043, "mtime": 1751995473604, "results": "81", "hashOfConfig": "53"}, {"size": 18193, "mtime": 1751995667644, "results": "82", "hashOfConfig": "53"}, {"size": 1653, "mtime": 1751983104860, "results": "83", "hashOfConfig": "53"}, {"size": 2743, "mtime": 1751995677976, "results": "84", "hashOfConfig": "53"}, {"size": 2430, "mtime": 1751991252330, "results": "85", "hashOfConfig": "53"}, {"size": 1097, "mtime": 1751980087380, "results": "86", "hashOfConfig": "53"}, {"size": 627, "mtime": 1751980067187, "results": "87", "hashOfConfig": "53"}, {"size": 1869, "mtime": 1751993074458, "results": "88", "hashOfConfig": "53"}, {"size": 1089, "mtime": 1751980076140, "results": "89", "hashOfConfig": "53"}, {"size": 991, "mtime": 1751980096087, "results": "90", "hashOfConfig": "53"}, {"size": 304, "mtime": 1751993088077, "results": "91", "hashOfConfig": "53"}, {"size": 2799, "mtime": 1751998396958, "results": "92", "hashOfConfig": "53"}, {"size": 1671, "mtime": 1751980018666, "results": "93", "hashOfConfig": "53"}, {"size": 5548, "mtime": 1751998185929, "results": "94", "hashOfConfig": "53"}, {"size": 3381, "mtime": 1751979973097, "results": "95", "hashOfConfig": "53"}, {"size": 371, "mtime": 1751997314159, "results": "96", "hashOfConfig": "53"}, {"size": 638, "mtime": 1751979827239, "results": "97", "hashOfConfig": "53"}, {"size": 7728, "mtime": 1752004018722, "results": "98", "hashOfConfig": "53"}, {"size": 3975, "mtime": 1751999594452, "results": "99", "hashOfConfig": "53"}, {"size": 169, "mtime": 1751980052785, "results": "100", "hashOfConfig": "53"}, {"size": 3030, "mtime": 1751998607719, "results": "101", "hashOfConfig": "53"}, {"size": 3805, "mtime": 1752004073410, "results": "102", "hashOfConfig": "53"}, {"size": 814, "mtime": 1751979813728, "results": "103", "hashOfConfig": "53"}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "959dgp", {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/app/admin/blog/page.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/app/admin/login/page.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/app/admin/media/page.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/app/admin/page.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/app/admin/pages/page.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/app/admin/settings/page.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/app/contact/layout.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/app/contact/page.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/app/page.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/app/sitemap.ts", [], [], "/Users/<USER>/Documents/SetMee/src/app/test-forms/page.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/StructuredData.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/admin/AdminLayout.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/admin/AuthGuard.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/layout/Footer.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/layout/index.ts", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/AboutSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/AuditSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/BusinessVerticalsSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/ContactSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/CustomIntegrationsSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/FeaturesSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/HeroSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/ImplementationProcessSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/OurServicesSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/sections/WhyKommoSection.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/ui/Button.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/ui/Card.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/ui/Container.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/ui/ScrollToTop.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/ui/Section.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/ui/SectionHeader.tsx", [], [], "/Users/<USER>/Documents/SetMee/src/components/ui/index.ts", [], [], "/Users/<USER>/Documents/SetMee/src/config/forms.ts", [], [], "/Users/<USER>/Documents/SetMee/src/data/business-verticals.ts", [], [], "/Users/<USER>/Documents/SetMee/src/data/content.ts", [], [], "/Users/<USER>/Documents/SetMee/src/data/design-tokens.ts", [], [], "/Users/<USER>/Documents/SetMee/src/data/navigation.ts", [], [], "/Users/<USER>/Documents/SetMee/src/data/services.ts", [], [], "/Users/<USER>/Documents/SetMee/src/lib/cms-utils.ts", [], [], "/Users/<USER>/Documents/SetMee/src/lib/form-service.ts", [], [], "/Users/<USER>/Documents/SetMee/src/lib/utils.ts", [], [], "/Users/<USER>/Documents/SetMee/src/lib/validations.ts", [], [], "/Users/<USER>/Documents/SetMee/src/types/cms.ts", [], [], "/Users/<USER>/Documents/SetMee/src/types/index.ts", [], []]