"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/CTAVideoSection.tsx":
/*!*****************************************************!*\
  !*** ./src/components/sections/CTAVideoSection.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _lib_validations__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/validations */ \"(app-pages-browser)/./src/lib/validations.ts\");\n/* harmony import */ var _lib_form_service__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/form-service */ \"(app-pages-browser)/./src/lib/form-service.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst CTAVideoSection = ()=>{\n    _s();\n    const [isSubmitted, setIsSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitMessage, setSubmitMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isVideoModalOpen, setIsVideoModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewImage, setPreviewImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('https://img.youtube.com/vi/mReZr_e70OA/hqdefault.jpg');\n    const [isImageLoaded, setIsImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFormSubmitting, setIsFormSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle Escape key to close modal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CTAVideoSection.useEffect\": ()=>{\n            const handleEscape = {\n                \"CTAVideoSection.useEffect.handleEscape\": (e)=>{\n                    if (e.key === 'Escape' && isVideoModalOpen) {\n                        setIsVideoModalOpen(false);\n                    }\n                }\n            }[\"CTAVideoSection.useEffect.handleEscape\"];\n            if (isVideoModalOpen) {\n                document.addEventListener('keydown', handleEscape);\n                // Prevent body scroll when modal is open\n                document.body.style.overflow = 'hidden';\n            }\n            return ({\n                \"CTAVideoSection.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleEscape);\n                    document.body.style.overflow = 'unset';\n                }\n            })[\"CTAVideoSection.useEffect\"];\n        }\n    }[\"CTAVideoSection.useEffect\"], [\n        isVideoModalOpen\n    ]);\n    const handleImageLoad = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CTAVideoSection.useCallback[handleImageLoad]\": ()=>{\n            console.log('✅ Image loaded successfully:', previewImage);\n            setIsImageLoaded(true);\n            setImageError(false);\n        }\n    }[\"CTAVideoSection.useCallback[handleImageLoad]\"], [\n        previewImage\n    ]);\n    const handleImageError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CTAVideoSection.useCallback[handleImageError]\": (e)=>{\n            console.error('❌ Failed to load preview image:', e.currentTarget.src);\n            setImageError(true);\n            setIsImageLoaded(true); // Show fallback content\n        }\n    }[\"CTAVideoSection.useCallback[handleImageError]\"], []);\n    // Debug: Log current state (can be removed in production)\n    // useEffect(() => {\n    //   console.log('🔍 Current image state:', {\n    //     previewImage,\n    //     isImageLoaded,\n    //     imageError\n    //   });\n    // }, [previewImage, isImageLoaded, imageError]);\n    // Timeout for image loading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CTAVideoSection.useEffect\": ()=>{\n            const timeout = setTimeout({\n                \"CTAVideoSection.useEffect.timeout\": ()=>{\n                    if (!isImageLoaded && !imageError) {\n                        // Image loading timeout, keeping fallback\n                        setImageError(true);\n                    }\n                }\n            }[\"CTAVideoSection.useEffect.timeout\"], 5000); // 5 second timeout\n            return ({\n                \"CTAVideoSection.useEffect\": ()=>clearTimeout(timeout)\n            })[\"CTAVideoSection.useEffect\"];\n        }\n    }[\"CTAVideoSection.useEffect\"], [\n        isImageLoaded,\n        imageError\n    ]);\n    const { register, handleSubmit, formState: { errors }, reset } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(_lib_validations__WEBPACK_IMPORTED_MODULE_4__.emailSubscriptionSchema)\n    });\n    const onSubmit = async (data)=>{\n        try {\n            setIsFormSubmitting(true);\n            const result = await (0,_lib_form_service__WEBPACK_IMPORTED_MODULE_5__.submitEmailSubscription)(data);\n            if (result.success) {\n                setIsSubmitted(true);\n                setSubmitMessage(result.message);\n                reset();\n            } else {\n                setSubmitMessage(result.message);\n            }\n        } catch (e) {\n            setSubmitMessage('An unexpected error occurred. Please try again.');\n        } finally{\n            setIsFormSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Section, {\n        id: \"cta-video\",\n        padding: \"xl\",\n        background: \"primary\",\n        className: \"text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                style: {\n                    position: 'relative',\n                    zIndex: 10,\n                    pointerEvents: 'auto'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            \"data-testid\": \"video-player\",\n                            className: \"aspect-video bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl overflow-hidden shadow-2xl relative cursor-pointer group hover:shadow-3xl transition-all duration-300\",\n                            onClick: ()=>setIsVideoModalOpen(true),\n                            style: {\n                                pointerEvents: 'auto',\n                                position: 'relative',\n                                zIndex: 20,\n                                cursor: 'pointer'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full h-full overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center text-white px-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 mx-auto mb-4 bg-white bg-opacity-20 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-8 h-8\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold mb-2\",\n                                                    children: \"Kommo Demo Video\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm opacity-90 mb-1\",\n                                                    children: \"Discover how Kommo can transform your business\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs opacity-70\",\n                                                    children: \"Click to watch our presentation\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isImageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: previewImage,\n                                        alt: \"Kommo Demo Video - Power your sales with Kommo\",\n                                        className: \"absolute inset-0 w-full h-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: previewImage,\n                                        alt: \"\",\n                                        className: \"hidden\",\n                                        onLoad: handleImageLoad,\n                                        onError: handleImageError\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isImageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center justify-center bg-black bg-opacity-20 group-hover:bg-opacity-30 transition-all duration-300 z-20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-20 h-20 bg-white bg-opacity-95 rounded-full flex items-center justify-center hover:bg-opacity-100 transition-all duration-200 shadow-xl group-hover:scale-110 border-4 border-white border-opacity-20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8 text-gray-800 ml-1\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        style: {\n                            position: 'relative',\n                            zIndex: 20,\n                            pointerEvents: 'auto'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-block\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-secondary-500 text-white px-3 py-1 rounded-full text-sm font-semibold uppercase tracking-wide\",\n                                            children: \"SELL MORE WITH kommo\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl md:text-4xl font-bold leading-tight\",\n                                        children: \"A dedicated solution for startups and enterprises\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-primary-100 leading-relaxed\",\n                                        children: \"Get an extended Kommo presentation and see how our CRM solution can help your business grow and scale efficiently.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-primary-700 rounded-xl p-6 space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 text-secondary-400\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold\",\n                                                children: \"Get an extended Kommo presentation!\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    !isSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit(onSubmit),\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        \"data-testid\": \"email-input\",\n                                                        type: \"email\",\n                                                        ...register('email'),\n                                                        placeholder: \"Enter your email address\",\n                                                        className: \"w-full px-4 py-3 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-secondary-500 \".concat(errors.email ? 'ring-2 ring-red-500' : ''),\n                                                        style: {\n                                                            pointerEvents: 'auto !important',\n                                                            position: 'relative',\n                                                            zIndex: 30\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-300\",\n                                                        children: errors.email.message\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            submitMessage && !isSubmitted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg text-sm \".concat(submitMessage.includes('error') || submitMessage.includes('Sorry') ? 'bg-red-500 text-white' : 'bg-green-500 text-white'),\n                                                children: submitMessage\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                type: \"submit\",\n                                                variant: \"secondary\",\n                                                size: \"lg\",\n                                                disabled: isFormSubmitting,\n                                                className: \"w-full\",\n                                                style: {\n                                                    pointerEvents: 'auto'\n                                                },\n                                                children: isFormSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Subscribing...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 21\n                                                }, undefined) : 'Subscribe'\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2 text-green-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: \"Successful!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-primary-200 text-sm\",\n                                                children: submitMessage\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    href: \"/contact\",\n                                    variant: \"outline\",\n                                    size: \"lg\",\n                                    className: \"border-white text-white hover:bg-white hover:text-primary-800\",\n                                    style: {\n                                        pointerEvents: 'auto'\n                                    },\n                                    children: \"Talk to an expert\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, undefined),\n            isVideoModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4\",\n                onClick: ()=>setIsVideoModalOpen(false),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full max-w-4xl aspect-video\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsVideoModalOpen(false),\n                            className: \"absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors duration-200 z-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-8 h-8\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full bg-black rounded-lg overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                src: \"https://www.youtube.com/embed/mReZr_e70OA?autoplay=1&rel=0&modestbranding=1\",\n                                title: \"Kommo Demo - See how Kommo can transform your business\",\n                                className: \"w-full h-full\",\n                                allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\",\n                                allowFullScreen: true,\n                                frameBorder: \"0\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                lineNumber: 293,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CTAVideoSection, \"y/93GsWGEo2AmGgibRYqL0Zlujk=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm\n    ];\n});\n_c = CTAVideoSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CTAVideoSection);\nvar _c;\n$RefreshReg$(_c, \"CTAVideoSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/CTAVideoSection.tsx\n"));

/***/ })

});