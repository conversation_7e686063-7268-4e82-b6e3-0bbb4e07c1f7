"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/form-service.ts":
/*!*********************************!*\
  !*** ./src/lib/form-service.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   submitContactForm: () => (/* binding */ submitContactForm),\n/* harmony export */   submitEmailSubscription: () => (/* binding */ submitEmailSubscription)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Form submission service with Make.com webhook integration\n// Configure webhook URLs in environment variables\n// Make.com webhook integration\nconst sendToMakeWebhook = async (webhookUrl, data)=>{\n    const response = await fetch(webhookUrl, {\n        method: 'POST',\n        headers: {\n            'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n            ...data,\n            timestamp: new Date().toISOString(),\n            source: 'setmee-website'\n        })\n    });\n    if (!response.ok) {\n        throw new Error(\"Webhook failed: \".concat(response.status, \" \").concat(response.statusText));\n    }\n    return response;\n};\nconst submitContactForm = async (data)=>{\n    try {\n        const webhookUrl = process.env.NEXT_PUBLIC_MAKE_CONTACT_WEBHOOK_URL;\n        if (webhookUrl) {\n            // Send to Make.com webhook\n            await sendToMakeWebhook(webhookUrl, {\n                type: 'contact_form',\n                name: data.name,\n                email: data.email,\n                phone: data.phone || '',\n                company: data.company || '',\n                message: data.message\n            });\n            console.log('Contact form sent to Make webhook:', data.email);\n        } else {\n            // Fallback: log to console if no webhook configured\n            console.log('Contact form submitted (no webhook configured):', data);\n        }\n        return {\n            success: true,\n            message: 'Thank you for your message! We will get back to you within 24 hours.'\n        };\n    } catch (error) {\n        console.error('Form submission error:', error);\n        return {\n            success: false,\n            message: 'Sorry, there was an error sending your message. Please try again.'\n        };\n    }\n};\nconst submitEmailSubscription = async (data)=>{\n    try {\n        // Simulate API call delay\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        // Log subscription data (in production, send to email service)\n        console.log('Email subscription:', data);\n        // Simulate success response\n        return {\n            success: true,\n            message: 'Thank you for subscribing! We will send you the presentation shortly.'\n        };\n    } catch (error) {\n        console.error('Subscription error:', error);\n        return {\n            success: false,\n            message: 'Sorry, there was an error with your subscription. Please try again.'\n        };\n    }\n}; // Example integration with Formspree (commented out)\n /*\nexport const submitContactFormFormspree = async (data: ContactFormData) => {\n  const response = await fetch('https://formspree.io/f/YOUR_FORM_ID', {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify(data),\n  });\n  \n  if (!response.ok) {\n    throw new Error('Failed to submit form');\n  }\n  \n  return response.json();\n};\n*/  // Example integration with EmailJS (commented out)\n /*\nimport emailjs from '@emailjs/browser';\n\nexport const submitContactFormEmailJS = async (data: ContactFormData) => {\n  const result = await emailjs.send(\n    'YOUR_SERVICE_ID',\n    'YOUR_TEMPLATE_ID',\n    {\n      from_name: data.name,\n      from_email: data.email,\n      phone: data.phone,\n      company: data.company,\n      message: data.message,\n    },\n    'YOUR_PUBLIC_KEY'\n  );\n  \n  return result;\n};\n*/ \n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/form-service.ts\n"));

/***/ })

});