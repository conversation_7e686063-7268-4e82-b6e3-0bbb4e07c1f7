"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/api/image.js":
/*!*********************************************!*\
  !*** ./node_modules/next/dist/api/image.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport default from dynamic */ _shared_lib_image_external__WEBPACK_IMPORTED_MODULE_0___default.a)\n/* harmony export */ });\n/* harmony import */ var _shared_lib_image_external__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../shared/lib/image-external */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/image-external.js\");\n/* harmony import */ var _shared_lib_image_external__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_shared_lib_image_external__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _shared_lib_image_external__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _shared_lib_image_external__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n//# sourceMappingURL=image.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL2ltYWdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF1RDtBQUNWOztBQUU3QyIsInNvdXJjZXMiOlsiL1VzZXJzL3ZpdGFsaWlsL0RvY3VtZW50cy9TZXRNZWUvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9hcGkvaW1hZ2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgZGVmYXVsdCB9IGZyb20gJy4uL3NoYXJlZC9saWIvaW1hZ2UtZXh0ZXJuYWwnO1xuZXhwb3J0ICogZnJvbSAnLi4vc2hhcmVkL2xpYi9pbWFnZS1leHRlcm5hbCc7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWltYWdlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/image.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-external.js":
/*!*************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/image-external.js ***!
  \*************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return _default;\n    },\n    getImageProps: function() {\n        return getImageProps;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _getimgprops = __webpack_require__(/*! ./get-img-props */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/get-img-props.js\");\nconst _imagecomponent = __webpack_require__(/*! ../../client/image-component */ \"(app-pages-browser)/./node_modules/next/dist/client/image-component.js\");\nconst _imageloader = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! next/dist/shared/lib/image-loader */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/image-loader.js\"));\nfunction getImageProps(imgProps) {\n    const { props } = (0, _getimgprops.getImgProps)(imgProps, {\n        defaultLoader: _imageloader.default,\n        // This is replaced by webpack define plugin\n        imgConf: {\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"dangerouslyAllowSVG\":false,\"unoptimized\":false,\"domains\":[],\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"**.ytimg.com\"},{\"protocol\":\"https\",\"hostname\":\"**.youtube.com\"},{\"protocol\":\"https\",\"hostname\":\"i.ytimg.com\"},{\"protocol\":\"https\",\"hostname\":\"img.youtube.com\"}]}\n    });\n    // Normally we don't care about undefined props because we pass to JSX,\n    // but this exported function could be used by the end user for anything\n    // so we delete undefined props to clean it up a little.\n    for (const [key, value] of Object.entries(props)){\n        if (value === undefined) {\n            delete props[key];\n        }\n    }\n    return {\n        props\n    };\n}\nconst _default = _imagecomponent.Image; //# sourceMappingURL=image-external.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9pbWFnZS1leHRlcm5hbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7SUFpQ0EsT0FBb0I7ZUFBcEI7O0lBakJnQkEsYUFBYTtlQUFiQTs7Ozt5Q0FiWTs0Q0FDTjtrRkFHSTtBQVNuQixTQUFTQSxjQUFjQyxRQUFvQjtJQUNoRCxNQUFNLEVBQUVDLEtBQUssRUFBRSxHQUFHQyxDQUFBQSxHQUFBQSxhQUFBQSxXQUFBQSxFQUFZRixVQUFVO1FBQ3RDRyxlQUFBQSxhQUFBQSxPQUFhO1FBQ2IsNENBQTRDO1FBQzVDQyxTQUFTQyx5WkFBNkI7SUFDeEM7SUFDQSx1RUFBdUU7SUFDdkUsd0VBQXdFO0lBQ3hFLHdEQUF3RDtJQUN4RCxLQUFLLE1BQU0sQ0FBQ0csS0FBS0MsTUFBTSxJQUFJQyxPQUFPQyxPQUFPLENBQUNWLE9BQVE7UUFDaEQsSUFBSVEsVUFBVUcsV0FBVztZQUN2QixPQUFPWCxLQUFLLENBQUNPLElBQTBCO1FBQ3pDO0lBQ0Y7SUFDQSxPQUFPO1FBQUVQO0lBQU07QUFDakI7TUFFQSxXQUFlWSxnQkFBQUEsS0FBSyIsInNvdXJjZXMiOlsiL1VzZXJzL3NyYy9zaGFyZWQvbGliL2ltYWdlLWV4dGVybmFsLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IEltYWdlQ29uZmlnQ29tcGxldGUsIEltYWdlTG9hZGVyUHJvcHMgfSBmcm9tICcuL2ltYWdlLWNvbmZpZydcbmltcG9ydCB0eXBlIHsgSW1hZ2VQcm9wcywgSW1hZ2VMb2FkZXIsIFN0YXRpY0ltYWdlRGF0YSB9IGZyb20gJy4vZ2V0LWltZy1wcm9wcydcblxuaW1wb3J0IHsgZ2V0SW1nUHJvcHMgfSBmcm9tICcuL2dldC1pbWctcHJvcHMnXG5pbXBvcnQgeyBJbWFnZSB9IGZyb20gJy4uLy4uL2NsaWVudC9pbWFnZS1jb21wb25lbnQnXG5cbi8vIEB0cy1pZ25vcmUgLSBUaGlzIGlzIHJlcGxhY2VkIGJ5IHdlYnBhY2sgYWxpYXNcbmltcG9ydCBkZWZhdWx0TG9hZGVyIGZyb20gJ25leHQvZGlzdC9zaGFyZWQvbGliL2ltYWdlLWxvYWRlcidcblxuLyoqXG4gKiBGb3IgbW9yZSBhZHZhbmNlZCB1c2UgY2FzZXMsIHlvdSBjYW4gY2FsbCBgZ2V0SW1hZ2VQcm9wcygpYFxuICogdG8gZ2V0IHRoZSBwcm9wcyB0aGF0IHdvdWxkIGJlIHBhc3NlZCB0byB0aGUgdW5kZXJseWluZyBgPGltZz5gIGVsZW1lbnQsXG4gKiBhbmQgaW5zdGVhZCBwYXNzIHRvIHRoZW0gdG8gYW5vdGhlciBjb21wb25lbnQsIHN0eWxlLCBjYW52YXMsIGV0Yy5cbiAqXG4gKiBSZWFkIG1vcmU6IFtOZXh0LmpzIGRvY3M6IGBnZXRJbWFnZVByb3BzYF0oaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2FwaS1yZWZlcmVuY2UvY29tcG9uZW50cy9pbWFnZSNnZXRpbWFnZXByb3BzKVxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0SW1hZ2VQcm9wcyhpbWdQcm9wczogSW1hZ2VQcm9wcykge1xuICBjb25zdCB7IHByb3BzIH0gPSBnZXRJbWdQcm9wcyhpbWdQcm9wcywge1xuICAgIGRlZmF1bHRMb2FkZXIsXG4gICAgLy8gVGhpcyBpcyByZXBsYWNlZCBieSB3ZWJwYWNrIGRlZmluZSBwbHVnaW5cbiAgICBpbWdDb25mOiBwcm9jZXNzLmVudi5fX05FWFRfSU1BR0VfT1BUUyBhcyBhbnkgYXMgSW1hZ2VDb25maWdDb21wbGV0ZSxcbiAgfSlcbiAgLy8gTm9ybWFsbHkgd2UgZG9uJ3QgY2FyZSBhYm91dCB1bmRlZmluZWQgcHJvcHMgYmVjYXVzZSB3ZSBwYXNzIHRvIEpTWCxcbiAgLy8gYnV0IHRoaXMgZXhwb3J0ZWQgZnVuY3Rpb24gY291bGQgYmUgdXNlZCBieSB0aGUgZW5kIHVzZXIgZm9yIGFueXRoaW5nXG4gIC8vIHNvIHdlIGRlbGV0ZSB1bmRlZmluZWQgcHJvcHMgdG8gY2xlYW4gaXQgdXAgYSBsaXR0bGUuXG4gIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKHByb3BzKSkge1xuICAgIGlmICh2YWx1ZSA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICBkZWxldGUgcHJvcHNba2V5IGFzIGtleW9mIHR5cGVvZiBwcm9wc11cbiAgICB9XG4gIH1cbiAgcmV0dXJuIHsgcHJvcHMgfVxufVxuXG5leHBvcnQgZGVmYXVsdCBJbWFnZVxuXG5leHBvcnQgdHlwZSB7IEltYWdlUHJvcHMsIEltYWdlTG9hZGVyUHJvcHMsIEltYWdlTG9hZGVyLCBTdGF0aWNJbWFnZURhdGEgfVxuIl0sIm5hbWVzIjpbImdldEltYWdlUHJvcHMiLCJpbWdQcm9wcyIsInByb3BzIiwiZ2V0SW1nUHJvcHMiLCJkZWZhdWx0TG9hZGVyIiwiaW1nQ29uZiIsInByb2Nlc3MiLCJlbnYiLCJfX05FWFRfSU1BR0VfT1BUUyIsImtleSIsInZhbHVlIiwiT2JqZWN0IiwiZW50cmllcyIsInVuZGVmaW5lZCIsIkltYWdlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/image-external.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/sections/CTAVideoSection.tsx":
/*!*****************************************************!*\
  !*** ./src/components/sections/CTAVideoSection.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _lib_validations__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/validations */ \"(app-pages-browser)/./src/lib/validations.ts\");\n/* harmony import */ var _lib_form_service__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/form-service */ \"(app-pages-browser)/./src/lib/form-service.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CTAVideoSection = ()=>{\n    _s();\n    const [isSubmitted, setIsSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitMessage, setSubmitMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isVideoModalOpen, setIsVideoModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewImage, setPreviewImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('/images/player-preview.png');\n    const [isImageLoaded, setIsImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFormSubmitting, setIsFormSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle Escape key to close modal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CTAVideoSection.useEffect\": ()=>{\n            const handleEscape = {\n                \"CTAVideoSection.useEffect.handleEscape\": (e)=>{\n                    if (e.key === 'Escape' && isVideoModalOpen) {\n                        setIsVideoModalOpen(false);\n                    }\n                }\n            }[\"CTAVideoSection.useEffect.handleEscape\"];\n            if (isVideoModalOpen) {\n                document.addEventListener('keydown', handleEscape);\n                // Prevent body scroll when modal is open\n                document.body.style.overflow = 'hidden';\n            }\n            return ({\n                \"CTAVideoSection.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleEscape);\n                    document.body.style.overflow = 'unset';\n                }\n            })[\"CTAVideoSection.useEffect\"];\n        }\n    }[\"CTAVideoSection.useEffect\"], [\n        isVideoModalOpen\n    ]);\n    const handleImageLoad = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CTAVideoSection.useCallback[handleImageLoad]\": ()=>{\n            console.log('✅ Image loaded successfully:', previewImage);\n            setIsImageLoaded(true);\n            setImageError(false);\n        }\n    }[\"CTAVideoSection.useCallback[handleImageLoad]\"], [\n        previewImage\n    ]);\n    const handleImageError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CTAVideoSection.useCallback[handleImageError]\": (e)=>{\n            console.error('❌ Failed to load preview image:', e.currentTarget.src);\n            console.error('Error details:', e);\n            setImageError(true);\n            // Try fallback to YouTube thumbnail if not already using it\n            if (!e.currentTarget.src.includes('youtube')) {\n                console.log('🔄 Switching to YouTube thumbnail fallback');\n                setPreviewImage('https://img.youtube.com/vi/mReZr_e70OA/maxresdefault.jpg');\n                setIsImageLoaded(false); // Reset to allow new image to load\n            } else {\n                // If YouTube thumbnail also fails, show a default state\n                console.log('⚠️ YouTube thumbnail also failed, showing default state');\n                setIsImageLoaded(true);\n            }\n        }\n    }[\"CTAVideoSection.useCallback[handleImageError]\"], []);\n    // Debug: Log current state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CTAVideoSection.useEffect\": ()=>{\n            console.log('🔍 Current image state:', {\n                previewImage,\n                isImageLoaded,\n                imageError\n            });\n        }\n    }[\"CTAVideoSection.useEffect\"], [\n        previewImage,\n        isImageLoaded,\n        imageError\n    ]);\n    const { register, handleSubmit, formState: { errors }, reset } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_7__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(_lib_validations__WEBPACK_IMPORTED_MODULE_5__.emailSubscriptionSchema)\n    });\n    const onSubmit = async (data)=>{\n        try {\n            setIsFormSubmitting(true);\n            const result = await (0,_lib_form_service__WEBPACK_IMPORTED_MODULE_6__.submitEmailSubscription)(data);\n            if (result.success) {\n                setIsSubmitted(true);\n                setSubmitMessage(result.message);\n                reset();\n            } else {\n                setSubmitMessage(result.message);\n            }\n        } catch (e) {\n            setSubmitMessage('An unexpected error occurred. Please try again.');\n        } finally{\n            setIsFormSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Section, {\n        id: \"cta-video\",\n        padding: \"xl\",\n        background: \"primary\",\n        className: \"text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"aspect-video bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl overflow-hidden shadow-2xl relative cursor-pointer group hover:shadow-3xl transition-all duration-300\",\n                            onClick: ()=>{\n                                console.log('Video clicked!');\n                                setIsVideoModalOpen(true);\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-br from-gray-700 to-gray-900\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    !isImageLoaded && !imageError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center justify-center bg-gradient-to-br from-gray-700 to-gray-900\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 mx-auto mb-2 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm opacity-75\",\n                                                    children: \"Loading preview...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: previewImage,\n                                        alt: \"Kommo Demo Video - Power your sales with Kommo\",\n                                        fill: true,\n                                        sizes: \"(max-width: 1024px) 100vw, 50vw\",\n                                        className: \"object-cover transition-opacity duration-500 \".concat(isImageLoaded ? 'opacity-100' : 'opacity-0'),\n                                        onLoad: handleImageLoad,\n                                        onError: handleImageError,\n                                        priority: true,\n                                        unoptimized: true\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    imageError && !isImageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center justify-center bg-gradient-to-br from-gray-700 to-gray-900\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 mx-auto mb-4 bg-white bg-opacity-20 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-8 h-8\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm opacity-75\",\n                                                    children: \"Video Preview\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center justify-center bg-black bg-opacity-20 group-hover:bg-opacity-30 transition-all duration-300 z-20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-20 h-20 bg-white bg-opacity-95 rounded-full flex items-center justify-center hover:bg-opacity-100 transition-all duration-200 shadow-xl group-hover:scale-110 border-4 border-white border-opacity-20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8 text-gray-800 ml-1\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-block\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-secondary-500 text-white px-3 py-1 rounded-full text-sm font-semibold uppercase tracking-wide\",\n                                            children: \"SELL MORE WITH kommo\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl md:text-4xl font-bold leading-tight\",\n                                        children: \"A dedicated solution for startups and enterprises\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-primary-100 leading-relaxed\",\n                                        children: \"Get an extended Kommo presentation and see how our CRM solution can help your business grow and scale efficiently.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-primary-700 rounded-xl p-6 space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 text-secondary-400\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold\",\n                                                children: \"Get an extended Kommo presentation!\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    !isSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit(onSubmit),\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        ...register('email'),\n                                                        placeholder: \"Enter your email address\",\n                                                        className: \"w-full px-4 py-3 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-secondary-500 \".concat(errors.email ? 'ring-2 ring-red-500' : '')\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-300\",\n                                                        children: errors.email.message\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            submitMessage && !isSubmitted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg text-sm \".concat(submitMessage.includes('error') || submitMessage.includes('Sorry') ? 'bg-red-500 text-white' : 'bg-green-500 text-white'),\n                                                children: submitMessage\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                type: \"submit\",\n                                                variant: \"secondary\",\n                                                size: \"lg\",\n                                                disabled: isFormSubmitting,\n                                                className: \"w-full\",\n                                                children: isFormSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Subscribing...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 21\n                                                }, undefined) : 'Get Started'\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2 text-green-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: \"Successful!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-primary-200 text-sm\",\n                                                children: submitMessage\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    href: \"/contact\",\n                                    variant: \"outline\",\n                                    size: \"lg\",\n                                    className: \"border-white text-white hover:bg-white hover:text-primary-800\",\n                                    children: \"Talk to an expert\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, undefined),\n            isVideoModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4\",\n                onClick: ()=>setIsVideoModalOpen(false),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full max-w-4xl aspect-video\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsVideoModalOpen(false),\n                            className: \"absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors duration-200 z-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-8 h-8\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full bg-black rounded-lg overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                src: \"https://www.youtube.com/embed/mReZr_e70OA?autoplay=1&rel=0&modestbranding=1\",\n                                title: \"Kommo Demo - See how Kommo can transform your business\",\n                                className: \"w-full h-full\",\n                                allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\",\n                                allowFullScreen: true,\n                                frameBorder: \"0\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                lineNumber: 269,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CTAVideoSection, \"Pb6tKfZoCAW3hNAtjIIOhORjkdY=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_7__.useForm\n    ];\n});\n_c = CTAVideoSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CTAVideoSection);\nvar _c;\n$RefreshReg$(_c, \"CTAVideoSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/CTAVideoSection.tsx\n"));

/***/ })

});