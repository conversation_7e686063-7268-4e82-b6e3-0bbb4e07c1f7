"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog/[slug]/page",{

/***/ "(app-pages-browser)/./src/app/blog/[slug]/page.tsx":
/*!**************************************!*\
  !*** ./src/app/blog/[slug]/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_cms_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/cms-utils */ \"(app-pages-browser)/./src/lib/cms-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst BlogPostPage = ()=>{\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams)();\n    const [post, setPost] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BlogPostPage.useEffect\": ()=>{\n            const slug = params.slug;\n            const posts = (0,_lib_cms_utils__WEBPACK_IMPORTED_MODULE_4__.getBlogPosts)();\n            const foundPost = posts.find({\n                \"BlogPostPage.useEffect.foundPost\": (p)=>p.slug === slug && p.status === 'published'\n            }[\"BlogPostPage.useEffect.foundPost\"]);\n            setPost(foundPost || null);\n            setLoading(false);\n        }\n    }[\"BlogPostPage.useEffect\"], [\n        params.slug\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Загрузка статьи...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!post) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-gray-900 mb-4\",\n                        children: \"404\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-600 mb-8\",\n                        children: \"Статья не найдена\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/blog\",\n                        className: \"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700\",\n                        children: \"Вернуться к блогу\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                lineNumber: 36,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, undefined);\n    }\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('ru-RU', {\n            day: 'numeric',\n            month: 'long',\n            year: 'numeric'\n        });\n    };\n    // Функция для извлечения соответствий шорткодов и URL из комментариев\n    const extractImageMappings = (content)=>{\n        const mappings = {};\n        const commentRegex = /<!-- \\[img:([^\\]]+)\\] = ([^>]+) -->/g;\n        let match;\n        while((match = commentRegex.exec(content)) !== null){\n            mappings[\"[img:\".concat(match[1], \"]\")] = match[2];\n        }\n        return mappings;\n    };\n    // Convert markdown-like content to HTML (basic implementation)\n    const formatContent = (content)=>{\n        const imageMappings = extractImageMappings(content);\n        return content// Убираем комментарии из отображения\n        .replace(/<!-- \\[img:[^\\]]+\\] = [^>]+ -->/g, '').split('\\n\\n').map((paragraph, index)=>{\n            // Handle shortcode images [img:id]\n            if (paragraph.includes('[img:')) {\n                const shortcodeRegex = /\\[img:([^\\]]+)\\]/g;\n                const processedParagraph = paragraph.replace(shortcodeRegex, (match)=>{\n                    const imageUrl = imageMappings[match];\n                    if (imageUrl) {\n                        // Исключаем featuredImage из контента\n                        if ((post === null || post === void 0 ? void 0 : post.featuredImage) && imageUrl === post.featuredImage) {\n                            return ''; // Не показываем featuredImage в контенте\n                        }\n                        return '<div class=\"my-6\"><img src=\"'.concat(imageUrl, '\" alt=\"Изображение\" class=\"w-full max-w-2xl mx-auto rounded-lg shadow-md\" /></div>');\n                    }\n                    return match;\n                });\n                return processedParagraph ? '<div key=\"'.concat(index, '\" class=\"text-gray-700 mb-4 leading-relaxed\">').concat(processedParagraph, \"</div>\") : '';\n            }\n            // Handle regular markdown images ![alt](url)\n            if (paragraph.includes('![') && paragraph.includes('](')) {\n                const imageRegex = /!\\[([^\\]]*)\\]\\(([^)]+)\\)/g;\n                const processedParagraph = paragraph.replace(imageRegex, (match, alt, url)=>{\n                    // Исключаем featuredImage из контента\n                    if ((post === null || post === void 0 ? void 0 : post.featuredImage) && url === post.featuredImage) {\n                        return ''; // Не показываем featuredImage в контенте\n                    }\n                    return '<div class=\"my-6\"><img src=\"'.concat(url, '\" alt=\"').concat(alt, '\" class=\"w-full max-w-2xl mx-auto rounded-lg shadow-md\" /></div>');\n                });\n                return processedParagraph ? '<div key=\"'.concat(index, '\" class=\"text-gray-700 mb-4 leading-relaxed\">').concat(processedParagraph, \"</div>\") : '';\n            }\n            if (paragraph.startsWith('# ')) {\n                return '<h1 key=\"'.concat(index, '\" class=\"text-3xl font-bold text-gray-900 mb-6\">').concat(paragraph.slice(2), \"</h1>\");\n            }\n            if (paragraph.startsWith('## ')) {\n                return '<h2 key=\"'.concat(index, '\" class=\"text-2xl font-semibold text-gray-900 mb-4 mt-8\">').concat(paragraph.slice(3), \"</h2>\");\n            }\n            if (paragraph.startsWith('### ')) {\n                return '<h3 key=\"'.concat(index, '\" class=\"text-xl font-semibold text-gray-900 mb-3 mt-6\">').concat(paragraph.slice(4), \"</h3>\");\n            }\n            return '<p key=\"'.concat(index, '\" class=\"text-gray-700 mb-4 leading-relaxed\">').concat(paragraph, \"</p>\");\n        }).join('');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"hover:text-primary-600\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/blog\",\n                                    className: \"hover:text-primary-600\",\n                                    children: \"Blog\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"text-gray-900 font-medium truncate\",\n                                children: post.title\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                    className: \"bg-white rounded-lg shadow-lg overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-sm text-gray-500 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"time\", {\n                                                dateTime: post.createdAt,\n                                                children: formatDate(post.createdAt)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mx-2\",\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: post.author\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            post.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mx-2\",\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"capitalize\",\n                                                        children: post.category\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                        children: post.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-600 leading-relaxed\",\n                                        children: post.excerpt\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    post.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 mt-6\",\n                                        children: post.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800\",\n                                                children: tag\n                                            }, tag, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"prose prose-lg max-w-none\",\n                                dangerouslySetInnerHTML: {\n                                    __html: formatContent(post.content)\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/blog\",\n                        className: \"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"mr-2 w-5 h-5\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M10 19l-7-7m0 0l7-7m-7 7h18\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Вернуться к блогу\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n            lineNumber: 127,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BlogPostPage, \"F0EN0bzNMY55uoaEHLGKWouRbws=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams\n    ];\n});\n_c = BlogPostPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BlogPostPage);\nvar _c;\n$RefreshReg$(_c, \"BlogPostPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/blog/[slug]/page.tsx\n"));

/***/ })

});