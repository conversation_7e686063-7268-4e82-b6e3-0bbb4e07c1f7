"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog/[slug]/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/noop-head.js":
/*!***************************************************************!*\
  !*** ./node_modules/next/dist/client/components/noop-head.js ***!
  \***************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return NoopHead;\n    }\n}));\nfunction NoopHead() {\n    return null;\n}\n_c = NoopHead;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=noop-head.js.map\nvar _c;\n$RefreshReg$(_c, \"NoopHead\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm9vcC1oZWFkLmpzIiwibWFwcGluZ3MiOiI7Ozs7MkNBQUE7OztlQUF3QkE7OztBQUFUO0lBQ2IsT0FBTztBQUNUO0tBRndCQSIsInNvdXJjZXMiOlsiL1VzZXJzL3NyYy9jbGllbnQvY29tcG9uZW50cy9ub29wLWhlYWQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE5vb3BIZWFkKCkge1xuICByZXR1cm4gbnVsbFxufVxuIl0sIm5hbWVzIjpbIk5vb3BIZWFkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/noop-head.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/blog/[slug]/page.tsx":
/*!**************************************!*\
  !*** ./src/app/blog/[slug]/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_cms_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/cms-utils */ \"(app-pages-browser)/./src/lib/cms-utils.ts\");\n/* harmony import */ var _components_SEOHead__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/SEOHead */ \"(app-pages-browser)/./src/components/SEOHead.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst BlogPostPage = ()=>{\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams)();\n    const [post, setPost] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BlogPostPage.useEffect\": ()=>{\n            const slug = params.slug;\n            const posts = (0,_lib_cms_utils__WEBPACK_IMPORTED_MODULE_4__.getBlogPosts)();\n            const foundPost = posts.find({\n                \"BlogPostPage.useEffect.foundPost\": (p)=>p.slug === slug && p.status === 'published'\n            }[\"BlogPostPage.useEffect.foundPost\"]);\n            setPost(foundPost || null);\n            setLoading(false);\n        }\n    }[\"BlogPostPage.useEffect\"], [\n        params.slug\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Загрузка статьи...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                lineNumber: 26,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!post) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-gray-900 mb-4\",\n                        children: \"404\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-600 mb-8\",\n                        children: \"Статья не найдена\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/blog\",\n                        className: \"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700\",\n                        children: \"Вернуться к блогу\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, undefined);\n    }\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('ru-RU', {\n            day: 'numeric',\n            month: 'long',\n            year: 'numeric'\n        });\n    };\n    // Функция для извлечения соответствий шорткодов и URL из комментариев\n    const extractImageMappings = (content)=>{\n        const mappings = {};\n        const commentRegex = /<!-- \\[img:([^\\]]+)\\] = ([^>]+) -->/g;\n        let match;\n        while((match = commentRegex.exec(content)) !== null){\n            mappings[\"[img:\".concat(match[1], \"]\")] = match[2];\n        }\n        return mappings;\n    };\n    // Convert markdown-like content to HTML (basic implementation)\n    const formatContent = (content)=>{\n        const imageMappings = extractImageMappings(content);\n        return content// Убираем комментарии из отображения\n        .replace(/<!-- \\[img:[^\\]]+\\] = [^>]+ -->/g, '').split('\\n\\n').map((paragraph, index)=>{\n            // Handle shortcode images [img:id]\n            if (paragraph.includes('[img:')) {\n                const shortcodeRegex = /\\[img:([^\\]]+)\\]/g;\n                const processedParagraph = paragraph.replace(shortcodeRegex, (match)=>{\n                    const imageUrl = imageMappings[match];\n                    if (imageUrl) {\n                        // Исключаем featuredImage из контента\n                        if ((post === null || post === void 0 ? void 0 : post.featuredImage) && imageUrl === post.featuredImage) {\n                            return ''; // Не показываем featuredImage в контенте\n                        }\n                        return '<div class=\"my-6\"><img src=\"'.concat(imageUrl, '\" alt=\"Изображение\" class=\"w-full max-w-2xl mx-auto rounded-lg shadow-md\" /></div>');\n                    }\n                    return match;\n                });\n                return processedParagraph ? '<div key=\"'.concat(index, '\" class=\"text-gray-700 mb-4 leading-relaxed\">').concat(processedParagraph, \"</div>\") : '';\n            }\n            // Handle regular markdown images ![alt](url)\n            if (paragraph.includes('![') && paragraph.includes('](')) {\n                const imageRegex = /!\\[([^\\]]*)\\]\\(([^)]+)\\)/g;\n                const processedParagraph = paragraph.replace(imageRegex, (match, alt, url)=>{\n                    // Исключаем featuredImage из контента\n                    if ((post === null || post === void 0 ? void 0 : post.featuredImage) && url === post.featuredImage) {\n                        return ''; // Не показываем featuredImage в контенте\n                    }\n                    return '<div class=\"my-6\"><img src=\"'.concat(url, '\" alt=\"').concat(alt, '\" class=\"w-full max-w-2xl mx-auto rounded-lg shadow-md\" /></div>');\n                });\n                return processedParagraph ? '<div key=\"'.concat(index, '\" class=\"text-gray-700 mb-4 leading-relaxed\">').concat(processedParagraph, \"</div>\") : '';\n            }\n            if (paragraph.startsWith('# ')) {\n                return '<h1 key=\"'.concat(index, '\" class=\"text-3xl font-bold text-gray-900 mb-6\">').concat(paragraph.slice(2), \"</h1>\");\n            }\n            if (paragraph.startsWith('## ')) {\n                return '<h2 key=\"'.concat(index, '\" class=\"text-2xl font-semibold text-gray-900 mb-4 mt-8\">').concat(paragraph.slice(3), \"</h2>\");\n            }\n            if (paragraph.startsWith('### ')) {\n                return '<h3 key=\"'.concat(index, '\" class=\"text-xl font-semibold text-gray-900 mb-3 mt-6\">').concat(paragraph.slice(4), \"</h3>\");\n            }\n            return '<p key=\"'.concat(index, '\" class=\"text-gray-700 mb-4 leading-relaxed\">').concat(paragraph, \"</p>\");\n        }).join('');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            post && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SEOHead__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                seoData: post.seo,\n                title: post.title,\n                description: post.excerpt,\n                url: \"\".concat( true ? window.location.origin : 0, \"/blog/\").concat(post.slug),\n                publishedTime: post.publishedAt,\n                modifiedTime: post.updatedAt,\n                author: post.author,\n                tags: post.tags\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                lineNumber: 129,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                            className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: \"hover:text-primary-600\",\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/blog\",\n                                        className: \"hover:text-primary-600\",\n                                        children: \"Blog\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"text-gray-900 font-medium truncate\",\n                                    children: post.title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                        className: \"bg-white rounded-lg shadow-lg overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                                    className: \"mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-sm text-gray-500 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"time\", {\n                                                    dateTime: post.createdAt,\n                                                    children: formatDate(post.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mx-2\",\n                                                    children: \"•\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: post.author\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                post.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"mx-2\",\n                                                            children: \"•\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"capitalize\",\n                                                            children: post.category\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                            children: post.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        post.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2 mt-6\",\n                                            children: post.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800\",\n                                                    children: tag\n                                                }, tag, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"prose prose-lg max-w-none\",\n                                    dangerouslySetInnerHTML: {\n                                        __html: formatContent(post.content)\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/blog\",\n                            className: \"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"mr-2 w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M10 19l-7-7m0 0l7-7m-7 7h18\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Вернуться к блогу\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/app/blog/[slug]/page.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BlogPostPage, \"F0EN0bzNMY55uoaEHLGKWouRbws=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams\n    ];\n});\n_c = BlogPostPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BlogPostPage);\nvar _c;\n$RefreshReg$(_c, \"BlogPostPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/blog/[slug]/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/SEOHead.tsx":
/*!************************************!*\
  !*** ./src/components/SEOHead.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"(app-pages-browser)/./node_modules/next/dist/client/components/noop-head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst SEOHead = (param)=>{\n    let { seoData, title, description, url, siteName = 'SetMee', defaultImage, publishedTime, modifiedTime, author, tags = [] } = param;\n    // Определяем финальные значения с fallback\n    const finalTitle = seoData.metaTitle || title || 'SetMee';\n    const finalDescription = seoData.metaDescription || description || '';\n    const finalUrl = url || ( true ? window.location.href : 0);\n    // Open Graph данные\n    const ogTitle = seoData.ogTitle || finalTitle;\n    const ogDescription = seoData.ogDescription || finalDescription;\n    const ogImage = seoData.ogImage || defaultImage;\n    const ogType = seoData.ogType || 'website';\n    // Twitter Card данные\n    const twitterTitle = seoData.twitterTitle || finalTitle;\n    const twitterDescription = seoData.twitterDescription || finalDescription;\n    const twitterImage = seoData.twitterImage || ogImage;\n    const twitterCard = seoData.twitterCard || 'summary_large_image';\n    // Canonical URL\n    const canonicalUrl = seoData.canonicalUrl || finalUrl;\n    // Robots meta\n    const robotsContent = [];\n    if (seoData.noIndex) robotsContent.push('noindex');\n    if (seoData.noFollow) robotsContent.push('nofollow');\n    if (seoData.noArchive) robotsContent.push('noarchive');\n    if (seoData.noSnippet) robotsContent.push('nosnippet');\n    const robotsMeta = robotsContent.length > 0 ? robotsContent.join(', ') : 'index, follow';\n    // Структурированные данные\n    const generateStructuredData = ()=>{\n        const baseData = {\n            '@context': 'https://schema.org',\n            '@type': seoData.schemaType || 'WebPage',\n            name: finalTitle,\n            description: finalDescription,\n            url: finalUrl\n        };\n        if (seoData.schemaType === 'Article' || seoData.schemaType === 'BlogPosting') {\n            var _seoData_keywords;\n            return {\n                ...baseData,\n                headline: finalTitle,\n                author: author ? {\n                    '@type': 'Person',\n                    name: author\n                } : undefined,\n                publisher: {\n                    '@type': 'Organization',\n                    name: siteName\n                },\n                datePublished: publishedTime,\n                dateModified: modifiedTime || publishedTime,\n                image: ogImage ? [\n                    ogImage\n                ] : undefined,\n                keywords: (_seoData_keywords = seoData.keywords) === null || _seoData_keywords === void 0 ? void 0 : _seoData_keywords.join(', '),\n                articleSection: tags.length > 0 ? tags[0] : undefined\n            };\n        }\n        return baseData;\n    };\n    const structuredData = generateStructuredData();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                children: finalTitle\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/SEOHead.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"description\",\n                content: finalDescription\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/SEOHead.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, undefined),\n            seoData.keywords && seoData.keywords.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"keywords\",\n                content: seoData.keywords.join(', ')\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/SEOHead.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"robots\",\n                content: robotsMeta\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/SEOHead.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, undefined),\n            canonicalUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                rel: \"canonical\",\n                href: canonicalUrl\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/SEOHead.tsx\",\n                lineNumber: 110,\n                columnNumber: 24\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:title\",\n                content: ogTitle\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/SEOHead.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:description\",\n                content: ogDescription\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/SEOHead.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:type\",\n                content: ogType\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/SEOHead.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:url\",\n                content: finalUrl\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/SEOHead.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:site_name\",\n                content: siteName\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/SEOHead.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, undefined),\n            ogImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:image\",\n                content: ogImage\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/SEOHead.tsx\",\n                lineNumber: 118,\n                columnNumber: 19\n            }, undefined),\n            ogImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:image:alt\",\n                content: finalTitle\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/SEOHead.tsx\",\n                lineNumber: 119,\n                columnNumber: 19\n            }, undefined),\n            (seoData.schemaType === 'Article' || seoData.schemaType === 'BlogPosting') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    publishedTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"article:published_time\",\n                        content: publishedTime\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/SEOHead.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 29\n                    }, undefined),\n                    modifiedTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"article:modified_time\",\n                        content: modifiedTime\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/SEOHead.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 28\n                    }, undefined),\n                    author && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"article:author\",\n                        content: author\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/SEOHead.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 22\n                    }, undefined),\n                    tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            property: \"article:tag\",\n                            content: tag\n                        }, index, false, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/SEOHead.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 13\n                        }, undefined))\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:card\",\n                content: twitterCard\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/SEOHead.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:title\",\n                content: twitterTitle\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/SEOHead.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:description\",\n                content: twitterDescription\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/SEOHead.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, undefined),\n            twitterImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:image\",\n                content: twitterImage\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/SEOHead.tsx\",\n                lineNumber: 137,\n                columnNumber: 24\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"viewport\",\n                content: \"width=device-width, initial-scale=1\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/SEOHead.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                httpEquiv: \"Content-Type\",\n                content: \"text/html; charset=utf-8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/SEOHead.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                type: \"application/ld+json\",\n                dangerouslySetInnerHTML: {\n                    __html: JSON.stringify(structuredData, null, 2)\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/SEOHead.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/components/SEOHead.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, undefined);\n};\n_c = SEOHead;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SEOHead);\nvar _c;\n$RefreshReg$(_c, \"SEOHead\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SEOHead.tsx\n"));

/***/ })

});