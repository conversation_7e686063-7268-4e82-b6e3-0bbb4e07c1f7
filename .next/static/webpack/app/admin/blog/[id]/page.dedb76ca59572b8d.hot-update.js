"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/blog/[id]/page",{

/***/ "(app-pages-browser)/./src/app/admin/blog/[id]/page.tsx":
/*!******************************************!*\
  !*** ./src/app/admin/blog/[id]/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/admin/AdminLayout */ \"(app-pages-browser)/./src/components/admin/AdminLayout.tsx\");\n/* harmony import */ var _components_admin_MediaGallery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/admin/MediaGallery */ \"(app-pages-browser)/./src/components/admin/MediaGallery.tsx\");\n/* harmony import */ var _components_admin_RichTextEditor__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/admin/RichTextEditor */ \"(app-pages-browser)/./src/components/admin/RichTextEditor.tsx\");\n/* harmony import */ var _lib_cms_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/cms-utils */ \"(app-pages-browser)/./src/lib/cms-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst EditBlogPostPage = ()=>{\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [post, setPost] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showMediaGallery, setShowMediaGallery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mediaGalleryMode, setMediaGalleryMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('featured');\n    const [pendingImages, setPendingImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        slug: '',\n        excerpt: '',\n        content: '',\n        status: 'draft',\n        tags: '',\n        featuredImage: '',\n        contentImages: [],\n        metaTitle: '',\n        metaDescription: ''\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditBlogPostPage.useEffect\": ()=>{\n            if (params.id) {\n                const postData = (0,_lib_cms_utils__WEBPACK_IMPORTED_MODULE_6__.getBlogPost)(params.id);\n                if (postData) {\n                    setPost(postData);\n                    setFormData({\n                        title: postData.title,\n                        slug: postData.slug,\n                        excerpt: postData.excerpt,\n                        content: postData.content,\n                        status: postData.status === 'archived' ? 'draft' : postData.status,\n                        tags: postData.tags.join(', '),\n                        featuredImage: postData.featuredImage || '',\n                        contentImages: postData.contentImages || [],\n                        metaTitle: postData.seo.metaTitle || '',\n                        metaDescription: postData.seo.metaDescription || ''\n                    });\n                } else {\n                    router.push('/admin/blog');\n                }\n            }\n        }\n    }[\"EditBlogPostPage.useEffect\"], [\n        params.id,\n        router\n    ]);\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const handleFeaturedImageSelect = (imageUrl)=>{\n        setFormData((prev)=>({\n                ...prev,\n                featuredImage: imageUrl\n            }));\n        setShowMediaGallery(false);\n    };\n    const handleContentImagesSelect = (imageUrls)=>{\n        if (mediaGalleryMode === 'content') {\n            // Insert images as markdown into content\n            const imageMarkdown = imageUrls.map((url)=>\"![Изображение](\".concat(url, \")\")).join('\\n\\n');\n            const currentContent = formData.content;\n            const newContent = currentContent + (currentContent ? '\\n\\n' : '') + imageMarkdown;\n            setFormData((prev)=>({\n                    ...prev,\n                    content: newContent\n                }));\n        } else {\n            // Legacy behavior for contentImages array\n            setFormData((prev)=>({\n                    ...prev,\n                    contentImages: imageUrls\n                }));\n        }\n        setShowMediaGallery(false);\n    };\n    const openMediaGallery = (mode)=>{\n        setMediaGalleryMode(mode);\n        setShowMediaGallery(true);\n    };\n    const removeContentImage = (imageUrl)=>{\n        setFormData((prev)=>({\n                ...prev,\n                contentImages: prev.contentImages.filter((url)=>url !== imageUrl)\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!post) return;\n        setIsLoading(true);\n        try {\n            const updatedData = {\n                title: formData.title,\n                slug: formData.slug,\n                excerpt: formData.excerpt,\n                content: formData.content,\n                status: formData.status,\n                featuredImage: formData.featuredImage || undefined,\n                contentImages: formData.contentImages.length > 0 ? formData.contentImages : undefined,\n                tags: formData.tags.split(',').map((tag)=>tag.trim()).filter(Boolean),\n                seo: {\n                    metaTitle: formData.metaTitle || formData.title,\n                    metaDescription: formData.metaDescription || formData.excerpt,\n                    keywords: formData.tags.split(',').map((tag)=>tag.trim()).filter(Boolean)\n                }\n            };\n            (0,_lib_cms_utils__WEBPACK_IMPORTED_MODULE_6__.updateBlogPost)(post.id, updatedData);\n            // Показываем уведомление об успешном сохранении\n            alert('Статья успешно сохранена!');\n            // Обновляем данные поста в состоянии\n            const updatedPost = {\n                ...post,\n                ...updatedData\n            };\n            setPost(updatedPost);\n        } catch (error) {\n            console.error('Ошибка при обновлении статьи:', error);\n            alert('Ошибка при обновлении статьи');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSaveDraft = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                status: 'draft'\n            }));\n        setTimeout(()=>{\n            var _document_getElementById;\n            (_document_getElementById = document.getElementById('submit-form')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n        }, 0);\n    };\n    const handlePublish = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                status: 'published'\n            }));\n        setTimeout(()=>{\n            var _document_getElementById;\n            (_document_getElementById = document.getElementById('submit-form')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n        }, 0);\n    };\n    const handleSave = ()=>{\n        // Сохранить с текущим статусом\n        setTimeout(()=>{\n            var _document_getElementById;\n            (_document_getElementById = document.getElementById('submit-form')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n        }, 0);\n    };\n    if (!post) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 min-h-screen bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Загрузка...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                lineNumber: 167,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n            lineNumber: 166,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 min-h-screen bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold text-gray-900\",\n                                            children: \"Редактирование статьи\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-gray-600\",\n                                            children: [\n                                                \"Редактирование: \",\n                                                post.title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>router.push('/admin/blog'),\n                                            className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                            children: \"Отмена\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        formData.status === 'published' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: handleSaveDraft,\n                                                    disabled: isLoading,\n                                                    className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\",\n                                                    children: \"Вернуть в черновик\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: handleSave,\n                                                    disabled: isLoading,\n                                                    className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\",\n                                                    children: \"Сохранить изменения\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: handleSaveDraft,\n                                                    disabled: isLoading,\n                                                    className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\",\n                                                    children: \"Сохранить черновик\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: handlePublish,\n                                                    disabled: isLoading,\n                                                    className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\",\n                                                    children: \"Опубликовать\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:col-span-2 space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"title\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Заголовок статьи *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"title\",\n                                                            name: \"title\",\n                                                            required: true,\n                                                            value: formData.title,\n                                                            onChange: handleInputChange,\n                                                            className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                            placeholder: \"Введите заголовок статьи\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"slug\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"URL (slug) *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"slug\",\n                                                            name: \"slug\",\n                                                            required: true,\n                                                            value: formData.slug,\n                                                            onChange: handleInputChange,\n                                                            className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                            placeholder: \"url-статьи\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2 flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        \"URL статьи: /blog/\",\n                                                                        formData.slug || 'url-статьи'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                    lineNumber: 279,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                formData.status === 'published' && formData.slug && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"/blog/\".concat(formData.slug),\n                                                                    target: \"_blank\",\n                                                                    rel: \"noopener noreferrer\",\n                                                                    className: \"inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4 mr-1\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                                lineNumber: 290,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                            lineNumber: 289,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        \"Посмотреть статью\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                    lineNumber: 283,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"excerpt\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Краткое описание *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            id: \"excerpt\",\n                                                            name: \"excerpt\",\n                                                            required: true,\n                                                            rows: 3,\n                                                            value: formData.excerpt,\n                                                            onChange: handleInputChange,\n                                                            className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                            placeholder: \"Краткое описание статьи для превью\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Изображение превью\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                formData.featuredImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative inline-block\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: formData.featuredImage,\n                                                                            alt: \"Featured\",\n                                                                            className: \"w-32 h-32 object-cover rounded-lg border border-gray-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                            lineNumber: 327,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        featuredImage: ''\n                                                                                    })),\n                                                                            className: \"absolute -top-2 -right-2 bg-red-600 text-white rounded-full p-1 hover:bg-red-700\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-4 h-4\",\n                                                                                fill: \"currentColor\",\n                                                                                viewBox: \"0 0 20 20\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    fillRule: \"evenodd\",\n                                                                                    d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                                                                    clipRule: \"evenodd\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                                    lineNumber: 338,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                                lineNumber: 337,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                            lineNumber: 332,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                    lineNumber: 326,\n                                                                    columnNumber: 23\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"mx-auto h-12 w-12 text-gray-400\",\n                                                                            stroke: \"currentColor\",\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 48 48\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\",\n                                                                                strokeWidth: 2,\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                                lineNumber: 345,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                            lineNumber: 344,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"mt-2 text-sm text-gray-600\",\n                                                                            children: \"Изображение не выбрано\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                            lineNumber: 347,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>openMediaGallery('featured'),\n                                                                    className: \"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4 mr-2\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                                lineNumber: 356,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                            lineNumber: 355,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        \"Выбрать изображение\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                    lineNumber: 350,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-4\",\n                                                            children: \"Содержание статьи *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_RichTextEditor__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            value: formData.content,\n                                                            onChange: (content)=>setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        content\n                                                                    })),\n                                                            onImageInsert: ()=>openMediaGallery('content'),\n                                                            placeholder: \"Редактируйте содержание статьи...\",\n                                                            className: \"min-h-[500px]\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"Публикация\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"status\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Статус\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                id: \"status\",\n                                                                name: \"status\",\n                                                                value: formData.status,\n                                                                onChange: handleInputChange,\n                                                                className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"draft\",\n                                                                        children: \"Черновик\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                        lineNumber: 397,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"published\",\n                                                                        children: \"Опубликовано\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                        lineNumber: 398,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-4 text-sm text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"Создано: \",\n                                                                    new Date(post.createdAt).toLocaleDateString('ru-RU')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                lineNumber: 402,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"Обновлено: \",\n                                                                    new Date(post.updatedAt).toLocaleDateString('ru-RU')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"Теги\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"tags\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Теги (через запятую)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"tags\",\n                                                                name: \"tags\",\n                                                                value: formData.tags,\n                                                                onChange: handleInputChange,\n                                                                className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                placeholder: \"тег1, тег2, тег3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"SEO\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"metaTitle\",\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Meta Title\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                        lineNumber: 431,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        id: \"metaTitle\",\n                                                                        name: \"metaTitle\",\n                                                                        value: formData.metaTitle,\n                                                                        onChange: handleInputChange,\n                                                                        className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                        placeholder: \"Заголовок для поисковых систем\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                        lineNumber: 434,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"metaDescription\",\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Meta Description\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                        lineNumber: 445,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                        id: \"metaDescription\",\n                                                                        name: \"metaDescription\",\n                                                                        rows: 3,\n                                                                        value: formData.metaDescription,\n                                                                        onChange: handleInputChange,\n                                                                        className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                        placeholder: \"Описание для поисковых систем\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                        lineNumber: 448,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                lineNumber: 444,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                id: \"submit-form\",\n                                className: \"hidden\",\n                                disabled: isLoading,\n                                children: \"Submit\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_MediaGallery__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showMediaGallery,\n                onClose: ()=>setShowMediaGallery(false),\n                onSelect: mediaGalleryMode === 'featured' ? handleFeaturedImageSelect : (imageUrl)=>handleContentImagesSelect([\n                        imageUrl\n                    ]),\n                onSelectMultiple: mediaGalleryMode === 'content' ? handleContentImagesSelect : undefined,\n                multiple: mediaGalleryMode === 'content',\n                selectedImage: mediaGalleryMode === 'featured' ? formData.featuredImage : undefined,\n                mode: mediaGalleryMode\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                lineNumber: 476,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EditBlogPostPage, \"tsnTRy/JlD9UN3Vz/quzK6l8JOE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c = EditBlogPostPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EditBlogPostPage);\nvar _c;\n$RefreshReg$(_c, \"EditBlogPostPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/blog/[id]/page.tsx\n"));

/***/ })

});