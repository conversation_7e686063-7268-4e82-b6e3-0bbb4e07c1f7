"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/blog/[id]/page",{

/***/ "(app-pages-browser)/./src/components/admin/RichTextEditor.tsx":
/*!*************************************************!*\
  !*** ./src/components/admin/RichTextEditor.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst RichTextEditor = (param)=>{\n    let { value, onChange, onImageInsert, placeholder = \"Начните писать...\", className = \"\", onInsertImage } = param;\n    _s();\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [isToolbarSticky, setIsToolbarSticky] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RichTextEditor.useEffect\": ()=>{\n            // Convert markdown to HTML for display\n            if (editorRef.current && value !== editorRef.current.innerHTML) {\n                editorRef.current.innerHTML = markdownToHtml(value);\n            }\n        }\n    }[\"RichTextEditor.useEffect\"], [\n        value\n    ]);\n    // Функция для получения позиции курсора в markdown тексте\n    const getCursorPositionInMarkdown = ()=>{\n        if (!editorRef.current) return value.length;\n        const selection = window.getSelection();\n        if (!selection || selection.rangeCount === 0) return value.length;\n        // Простое приближение - возвращаем позицию в конце\n        // В реальной реализации здесь была бы сложная логика конвертации HTML позиции в markdown\n        return value.length;\n    };\n    // Функция для вставки изображения в markdown\n    const insertImageIntoMarkdown = (imageUrl)=>{\n        const imageMarkdown = \"![Изображение](\".concat(imageUrl, \")\");\n        const cursorPos = getCursorPositionInMarkdown();\n        const beforeCursor = value.substring(0, cursorPos);\n        const afterCursor = value.substring(cursorPos);\n        // Добавляем переносы строк для красивого форматирования\n        const needsNewlineBefore = beforeCursor && !beforeCursor.endsWith('\\n\\n');\n        const needsNewlineAfter = afterCursor && !afterCursor.startsWith('\\n\\n');\n        const newContent = beforeCursor + (needsNewlineBefore ? '\\n\\n' : '') + imageMarkdown + (needsNewlineAfter ? '\\n\\n' : '') + afterCursor;\n        onChange(newContent);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RichTextEditor.useEffect\": ()=>{\n            // Set up global function for image insertion\n            window.insertImageIntoEditor = insertImageIntoMarkdown;\n            return ({\n                \"RichTextEditor.useEffect\": ()=>{\n                    delete window.insertImageIntoEditor;\n                }\n            })[\"RichTextEditor.useEffect\"];\n        }\n    }[\"RichTextEditor.useEffect\"], [\n        value,\n        onChange\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RichTextEditor.useEffect\": ()=>{\n            const handleScroll = {\n                \"RichTextEditor.useEffect.handleScroll\": ()=>{\n                    if (editorRef.current) {\n                        const rect = editorRef.current.getBoundingClientRect();\n                        setIsToolbarSticky(rect.top <= 100);\n                    }\n                }\n            }[\"RichTextEditor.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"RichTextEditor.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"RichTextEditor.useEffect\"];\n        }\n    }[\"RichTextEditor.useEffect\"], []);\n    const markdownToHtml = (markdown)=>{\n        return markdown.replace(/^### (.*$)/gim, '<h3>$1</h3>').replace(/^## (.*$)/gim, '<h2>$1</h2>').replace(/^# (.*$)/gim, '<h1>$1</h1>').replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>').replace(/\\*(.*?)\\*/g, '<em>$1</em>').replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, '<img src=\"$2\" alt=\"$1\" style=\"max-width: 100%; height: auto; margin: 10px 0;\" />').replace(/\\n\\n/g, '</p><p>').replace(/^(.+)$/gm, '<p>$1</p>').replace(/<p><\\/p>/g, '').replace(/<p>(<h[1-6]>.*<\\/h[1-6]>)<\\/p>/g, '$1').replace(/<p>(<img[^>]*>)<\\/p>/g, '<div style=\"text-align: center; margin: 20px 0;\">$1</div>');\n    };\n    const htmlToMarkdown = (html)=>{\n        return html.replace(/<h1>(.*?)<\\/h1>/g, '# $1\\n\\n').replace(/<h2>(.*?)<\\/h2>/g, '## $1\\n\\n').replace(/<h3>(.*?)<\\/h3>/g, '### $1\\n\\n').replace(/<strong>(.*?)<\\/strong>/g, '**$1**').replace(/<em>(.*?)<\\/em>/g, '*$1*').replace(/<div[^>]*><img src=\"([^\"]*)\" alt=\"([^\"]*)\"[^>]*><\\/div>/g, '![${2}](${1})\\n\\n').replace(/<img src=\"([^\"]*)\" alt=\"([^\"]*)\"[^>]*>/g, '![${2}](${1})\\n\\n').replace(/<p>(.*?)<\\/p>/g, '$1\\n\\n').replace(/<br\\s*\\/?>/g, '\\n').replace(/&nbsp;/g, ' ').replace(/\\n{3,}/g, '\\n\\n').trim();\n    };\n    const handleInput = ()=>{\n        if (editorRef.current) {\n            const html = editorRef.current.innerHTML;\n            const markdown = htmlToMarkdown(html);\n            onChange(markdown);\n        }\n    };\n    const execCommand = (command, value)=>{\n        var _editorRef_current;\n        // eslint-disable-next-line deprecation/deprecation\n        document.execCommand(command, false, value);\n        handleInput();\n        (_editorRef_current = editorRef.current) === null || _editorRef_current === void 0 ? void 0 : _editorRef_current.focus();\n    };\n    const insertHeading = (level)=>{\n        const selection = window.getSelection();\n        if (selection && selection.rangeCount > 0) {\n            const range = selection.getRangeAt(0);\n            const selectedText = range.toString() || 'Заголовок';\n            const heading = document.createElement(\"h\".concat(level));\n            heading.textContent = selectedText;\n            range.deleteContents();\n            range.insertNode(heading);\n            // Move cursor after heading\n            range.setStartAfter(heading);\n            range.setEndAfter(heading);\n            selection.removeAllRanges();\n            selection.addRange(range);\n            handleInput();\n        }\n    };\n    const insertImage = ()=>{\n        if (onImageInsert) {\n            onImageInsert();\n        }\n    };\n    const insertQuote = ()=>{\n        const selection = window.getSelection();\n        if (selection && selection.rangeCount > 0) {\n            const range = selection.getRangeAt(0);\n            const selectedText = range.toString() || 'Цитата';\n            const blockquote = document.createElement('blockquote');\n            blockquote.style.borderLeft = '4px solid #e5e7eb';\n            blockquote.style.paddingLeft = '1rem';\n            blockquote.style.margin = '1rem 0';\n            blockquote.style.fontStyle = 'italic';\n            blockquote.style.color = '#6b7280';\n            blockquote.textContent = selectedText;\n            range.deleteContents();\n            range.insertNode(blockquote);\n            range.setStartAfter(blockquote);\n            range.setEndAfter(blockquote);\n            selection.removeAllRanges();\n            selection.addRange(range);\n            handleInput();\n        }\n    };\n    const insertCode = ()=>{\n        const selection = window.getSelection();\n        if (selection && selection.rangeCount > 0) {\n            const range = selection.getRangeAt(0);\n            const selectedText = range.toString() || 'код';\n            const code = document.createElement('code');\n            code.style.backgroundColor = '#f3f4f6';\n            code.style.padding = '0.25rem 0.5rem';\n            code.style.borderRadius = '0.25rem';\n            code.style.fontFamily = 'monospace';\n            code.style.fontSize = '0.875rem';\n            code.textContent = selectedText;\n            range.deleteContents();\n            range.insertNode(code);\n            range.setStartAfter(code);\n            range.setEndAfter(code);\n            selection.removeAllRanges();\n            selection.addRange(range);\n            handleInput();\n        }\n    };\n    const insertLink = ()=>{\n        const url = prompt('Введите URL ссылки:');\n        if (url) {\n            execCommand('createLink', url);\n        }\n    };\n    const toolbarButtons = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M6 2l3 6H5l3 6\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 228,\n                columnNumber: 9\n            }, undefined),\n            title: 'Заголовок 1',\n            action: ()=>insertHeading(1)\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M6 4l3 4H5l3 4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 237,\n                columnNumber: 9\n            }, undefined),\n            title: 'Заголовок 2',\n            action: ()=>insertHeading(2)\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M6 6l3 2H5l3 2\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 246,\n                columnNumber: 9\n            }, undefined),\n            title: 'Заголовок 3',\n            action: ()=>insertHeading(3)\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                children: \"B\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 255,\n                columnNumber: 13\n            }, undefined),\n            title: 'Жирный',\n            action: ()=>execCommand('bold')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                children: \"I\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 260,\n                columnNumber: 13\n            }, undefined),\n            title: 'Курсив',\n            action: ()=>execCommand('italic')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"u\", {\n                children: \"U\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 265,\n                columnNumber: 13\n            }, undefined),\n            title: 'Подчеркнутый',\n            action: ()=>execCommand('underline')\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M4 6h16M4 12h8m-8 6h16\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 272,\n                columnNumber: 9\n            }, undefined),\n            title: 'По левому краю',\n            action: ()=>execCommand('justifyLeft')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M4 6h16M4 12h16M4 18h16\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 282,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 281,\n                columnNumber: 9\n            }, undefined),\n            title: 'По центру',\n            action: ()=>execCommand('justifyCenter')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M4 6h16M4 12h16M4 18h12\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 290,\n                columnNumber: 9\n            }, undefined),\n            title: 'По правому краю',\n            action: ()=>execCommand('justifyRight')\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M4 6h16M4 10h16M4 14h16M4 18h16\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 301,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 300,\n                columnNumber: 9\n            }, undefined),\n            title: 'Список',\n            action: ()=>execCommand('insertUnorderedList')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M7 20l4-16m2 16l4-16M6 9h14M4 15h14\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 309,\n                columnNumber: 9\n            }, undefined),\n            title: 'Нумерованный список',\n            action: ()=>execCommand('insertOrderedList')\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v6a2 2 0 01-2 2h-2l-4 4z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 320,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 319,\n                columnNumber: 9\n            }, undefined),\n            title: 'Цитата',\n            action: insertQuote\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 329,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 328,\n                columnNumber: 9\n            }, undefined),\n            title: 'Код',\n            action: insertCode\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 338,\n                columnNumber: 9\n            }, undefined),\n            title: 'Ссылка',\n            action: insertLink\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 348,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 347,\n                columnNumber: 9\n            }, undefined),\n            title: 'Изображение',\n            action: insertImage\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M7 16l-4-4m0 0l4-4m-4 4h18\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 358,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 357,\n                columnNumber: 9\n            }, undefined),\n            title: 'Отменить',\n            action: ()=>execCommand('undo')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M17 8l4 4m0 0l-4 4m4-4H3\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 367,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 366,\n                columnNumber: 9\n            }, undefined),\n            title: 'Повторить',\n            action: ()=>execCommand('redo')\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-378f0ccdb29f75a1\" + \" \" + \"border border-gray-300 rounded-lg overflow-hidden \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-378f0ccdb29f75a1\" + \" \" + \"bg-gray-50 border-b border-gray-300 p-2 \".concat(isToolbarSticky ? 'sticky top-0 z-10 shadow-md' : ''),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-378f0ccdb29f75a1\" + \" \" + \"flex flex-wrap items-center gap-1\",\n                    children: toolbarButtons.map((button, index)=>{\n                        if (button.type === 'separator') {\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-378f0ccdb29f75a1\" + \" \" + \"w-px h-6 bg-gray-300 mx-1\"\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 22\n                            }, undefined);\n                        }\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: button.action,\n                            title: button.title,\n                            className: \"jsx-378f0ccdb29f75a1\" + \" \" + \"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors\",\n                            children: button.icon\n                        }, index, false, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 378,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: editorRef,\n                contentEditable: true,\n                onInput: handleInput,\n                onPaste: ()=>{\n                    // Handle paste to maintain formatting\n                    setTimeout(handleInput, 0);\n                },\n                style: {\n                    lineHeight: '1.6'\n                },\n                suppressContentEditableWarning: true,\n                \"data-placeholder\": placeholder,\n                className: \"jsx-378f0ccdb29f75a1\" + \" \" + \"min-h-[400px] p-4 focus:outline-none prose prose-lg max-w-none\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 401,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"378f0ccdb29f75a1\",\n                children: '[contenteditable].jsx-378f0ccdb29f75a1:empty:before{content:attr(data-placeholder);color:#9ca3af;pointer-events:none}[contenteditable].jsx-378f0ccdb29f75a1 h1.jsx-378f0ccdb29f75a1{font-size:2rem;font-weight:bold;margin:1rem 0;line-height:1.2}[contenteditable].jsx-378f0ccdb29f75a1 h2.jsx-378f0ccdb29f75a1{font-size:1.5rem;font-weight:bold;margin:1rem 0;line-height:1.3}[contenteditable].jsx-378f0ccdb29f75a1 h3.jsx-378f0ccdb29f75a1{font-size:1.25rem;font-weight:bold;margin:1rem 0;line-height:1.4}[contenteditable].jsx-378f0ccdb29f75a1 p.jsx-378f0ccdb29f75a1{margin:.5rem 0;line-height:1.6}[contenteditable].jsx-378f0ccdb29f75a1 ul.jsx-378f0ccdb29f75a1,[contenteditable].jsx-378f0ccdb29f75a1 ol.jsx-378f0ccdb29f75a1{margin:1rem 0;padding-left:2rem}[contenteditable].jsx-378f0ccdb29f75a1 li.jsx-378f0ccdb29f75a1{margin:.25rem 0}[contenteditable].jsx-378f0ccdb29f75a1 img.jsx-378f0ccdb29f75a1{max-width:100%;height:auto;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;margin:1rem 0}[contenteditable].jsx-378f0ccdb29f75a1 a.jsx-378f0ccdb29f75a1{color:#3b82f6;text-decoration:underline}[contenteditable].jsx-378f0ccdb29f75a1 a.jsx-378f0ccdb29f75a1:hover{color:#1d4ed8}[contenteditable].jsx-378f0ccdb29f75a1 blockquote.jsx-378f0ccdb29f75a1{border-left:4px solid#e5e7eb;padding-left:1rem;margin:1rem 0;font-style:italic;color:#6b7280;background-color:#f9fafb;padding:1rem;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem}[contenteditable].jsx-378f0ccdb29f75a1 code.jsx-378f0ccdb29f75a1{background-color:#f3f4f6;padding:.25rem .5rem;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;font-family:\"Monaco\",\"Menlo\",\"Ubuntu Mono\",monospace;font-size:.875rem;color:#1f2937}[contenteditable].jsx-378f0ccdb29f75a1 pre.jsx-378f0ccdb29f75a1{background-color:#1f2937;color:#f9fafb;padding:1rem;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;overflow-x:auto;margin:1rem 0}[contenteditable].jsx-378f0ccdb29f75a1 pre.jsx-378f0ccdb29f75a1 code.jsx-378f0ccdb29f75a1{background-color:transparent;padding:0;color:inherit}'\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n        lineNumber: 376,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RichTextEditor, \"pnr2pskr+HJRoIWqvsH/WvBtvkk=\");\n_c = RichTextEditor;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RichTextEditor);\nvar _c;\n$RefreshReg$(_c, \"RichTextEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/RichTextEditor.tsx\n"));

/***/ })

});