"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/blog/[id]/page",{

/***/ "(app-pages-browser)/./src/components/admin/MediaGallery.tsx":
/*!***********************************************!*\
  !*** ./src/components/admin/MediaGallery.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_cms_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/cms-utils */ \"(app-pages-browser)/./src/lib/cms-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst MediaGallery = (param)=>{\n    let { isOpen, onClose, onSelect, selectedImage, multiple = false, onSelectMultiple, mode = 'content', title } = param;\n    _s();\n    const [mediaFiles, setMediaFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedFiles, setSelectedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOver, setDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaGallery.useEffect\": ()=>{\n            if (isOpen) {\n                loadMediaFiles();\n            }\n        }\n    }[\"MediaGallery.useEffect\"], [\n        isOpen\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaGallery.useEffect\": ()=>{\n            if (selectedImage && !multiple) {\n                setSelectedFiles([\n                    selectedImage\n                ]);\n            }\n        }\n    }[\"MediaGallery.useEffect\"], [\n        selectedImage,\n        multiple\n    ]);\n    const loadMediaFiles = ()=>{\n        const files = (0,_lib_cms_utils__WEBPACK_IMPORTED_MODULE_2__.getMediaFiles)();\n        setMediaFiles(files);\n    };\n    const handleFileUpload = async (files)=>{\n        setUploading(true);\n        for(let i = 0; i < files.length; i++){\n            const file = files[i];\n            // Check if file is an image\n            if (!file.type.startsWith('image/')) {\n                continue;\n            }\n            // Create a data URL for the image\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                var _e_target;\n                const dataUrl = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n                const mediaFile = {\n                    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n                    name: file.name.replace(/\\.[^/.]+$/, \"\"),\n                    originalName: file.name,\n                    size: file.size,\n                    type: file.type,\n                    url: dataUrl,\n                    uploadedAt: new Date().toISOString()\n                };\n                (0,_lib_cms_utils__WEBPACK_IMPORTED_MODULE_2__.saveMediaFile)(mediaFile);\n                loadMediaFiles();\n            };\n            reader.readAsDataURL(file);\n        }\n        setUploading(false);\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        setDragOver(false);\n        const files = e.dataTransfer.files;\n        if (files.length > 0) {\n            handleFileUpload(files);\n        }\n    };\n    const handleFileSelect = (e)=>{\n        const files = e.target.files;\n        if (files && files.length > 0) {\n            handleFileUpload(files);\n        }\n    };\n    const handleImageSelect = (imageUrl)=>{\n        if (multiple) {\n            const newSelection = selectedFiles.includes(imageUrl) ? selectedFiles.filter((url)=>url !== imageUrl) : [\n                ...selectedFiles,\n                imageUrl\n            ];\n            setSelectedFiles(newSelection);\n        } else {\n            setSelectedFiles([\n                imageUrl\n            ]);\n        }\n    };\n    const handleConfirmSelection = ()=>{\n        if (multiple && onSelectMultiple) {\n            onSelectMultiple(selectedFiles);\n        } else if (selectedFiles.length > 0) {\n            onSelect(selectedFiles[0]);\n        }\n        onClose();\n    };\n    const handleDeleteFile = (fileId)=>{\n        if (confirm('Удалить это изображение?')) {\n            (0,_lib_cms_utils__WEBPACK_IMPORTED_MODULE_2__.deleteMediaFile)(fileId);\n            loadMediaFiles();\n            // Remove from selection if selected\n            setSelectedFiles((prev)=>prev.filter((url)=>{\n                    const file = mediaFiles.find((f)=>f.id === fileId);\n                    return file ? url !== file.url : true;\n                }));\n        }\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: \"Медиа галерея\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onClose,\n                                            className: \"text-gray-400 hover:text-gray-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M6 18L18 6M6 6l12 12\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-2 border-dashed rounded-lg p-6 text-center mb-6 transition-colors \".concat(dragOver ? 'border-primary-500 bg-primary-50' : 'border-gray-300'),\n                                    onDrop: handleDrop,\n                                    onDragOver: (e)=>{\n                                        e.preventDefault();\n                                        setDragOver(true);\n                                    },\n                                    onDragLeave: ()=>setDragOver(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"mx-auto h-12 w-12 text-gray-400\",\n                                            stroke: \"currentColor\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 48 48\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\",\n                                                strokeWidth: 2,\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"file-upload\",\n                                                    className: \"cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"mt-2 block text-sm font-medium text-gray-900\",\n                                                            children: [\n                                                                \"Перетащите изображения сюда или\",\n                                                                ' ',\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-primary-600 hover:text-primary-500\",\n                                                                    children: \"выберите файлы\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                                    lineNumber: 187,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"file-upload\",\n                                                            name: \"file-upload\",\n                                                            type: \"file\",\n                                                            className: \"sr-only\",\n                                                            multiple: true,\n                                                            accept: \"image/*\",\n                                                            onChange: handleFileSelect\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-xs text-gray-500\",\n                                                    children: \"PNG, JPG, GIF до 10MB\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, undefined),\n                                uploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Загрузка...\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 max-h-96 overflow-y-auto\",\n                                    children: mediaFiles.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all \".concat(selectedFiles.includes(file.url) ? 'border-primary-500 ring-2 ring-primary-200' : 'border-gray-200 hover:border-gray-300'),\n                                            onClick: ()=>handleImageSelect(file.url),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-square\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: file.url,\n                                                        alt: file.name,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                selectedFiles.includes(file.url) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-2 right-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-5 h-5 bg-primary-600 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-3 h-3 text-white\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                                lineNumber: 239,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        handleDeleteFile(file.id);\n                                                    },\n                                                    className: \"absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity bg-red-600 text-white rounded-full p-1 hover:bg-red-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white p-2 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs truncate\",\n                                                            children: file.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-300\",\n                                                            children: formatFileSize(file.size)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, file.id, true, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, undefined),\n                                mediaFiles.length === 0 && !uploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"mx-auto h-12 w-12 text-gray-400\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-sm text-gray-500\",\n                                            children: \"Нет загруженных изображений\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleConfirmSelection,\n                                    disabled: selectedFiles.length === 0,\n                                    className: \"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: multiple ? \"Выбрать (\".concat(selectedFiles.length, \")\") : 'Выбрать'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm\",\n                                    children: \"Отмена\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n            lineNumber: 146,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MediaGallery, \"WU8LBYv1uTx3EGcVj6Eta3JMTIc=\");\n_c = MediaGallery;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MediaGallery);\nvar _c;\n$RefreshReg$(_c, \"MediaGallery\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/MediaGallery.tsx\n"));

/***/ })

});