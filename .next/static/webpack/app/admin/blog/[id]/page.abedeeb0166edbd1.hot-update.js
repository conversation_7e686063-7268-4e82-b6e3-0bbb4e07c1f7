"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/blog/[id]/page",{

/***/ "(app-pages-browser)/./src/components/admin/RichTextEditor.tsx":
/*!*************************************************!*\
  !*** ./src/components/admin/RichTextEditor.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst RichTextEditor = (param)=>{\n    let { value, onChange, onImageInsert, placeholder = \"Начните писать...\", className = \"\", insertImageUrl } = param;\n    _s();\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [isToolbarSticky, setIsToolbarSticky] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RichTextEditor.useEffect\": ()=>{\n            // Convert markdown to HTML for display\n            if (editorRef.current && value !== editorRef.current.innerHTML) {\n                editorRef.current.innerHTML = markdownToHtml(value);\n            }\n        }\n    }[\"RichTextEditor.useEffect\"], [\n        value\n    ]);\n    // Функция для вставки изображения\n    const insertImageIntoEditor = (imageUrl)=>{\n        if (editorRef.current) {\n            // Фокусируемся на редакторе\n            editorRef.current.focus();\n            // Создаем изображение\n            const img = document.createElement('img');\n            img.src = imageUrl;\n            img.alt = 'Изображение';\n            img.style.maxWidth = '100%';\n            img.style.height = 'auto';\n            img.style.margin = '10px 0';\n            img.style.borderRadius = '0.5rem';\n            // Получаем текущую позицию курсора\n            const selection = window.getSelection();\n            if (selection && selection.rangeCount > 0) {\n                const range = selection.getRangeAt(0);\n                // Вставляем изображение в позицию курсора\n                range.deleteContents(); // Удаляем выделенный текст, если есть\n                range.insertNode(img);\n                // Добавляем перенос строки после изображения\n                const br = document.createElement('br');\n                range.setStartAfter(img);\n                range.insertNode(br);\n                // Устанавливаем курсор после изображения\n                range.setStartAfter(br);\n                range.setEndAfter(br);\n                selection.removeAllRanges();\n                selection.addRange(range);\n            } else {\n                // Если нет выделения, добавляем в конец\n                editorRef.current.appendChild(img);\n                const br = document.createElement('br');\n                editorRef.current.appendChild(br);\n            }\n            // Обновляем содержимое\n            handleInput();\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RichTextEditor.useEffect\": ()=>{\n            // Set up global function for image insertion\n            window.insertImageIntoEditor = insertImageIntoEditor;\n            return ({\n                \"RichTextEditor.useEffect\": ()=>{\n                    delete window.insertImageIntoEditor;\n                }\n            })[\"RichTextEditor.useEffect\"];\n        }\n    }[\"RichTextEditor.useEffect\"], []);\n    // Обработка вставки изображения через prop\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RichTextEditor.useEffect\": ()=>{\n            if (insertImageUrl) {\n                insertImageIntoEditor(insertImageUrl);\n            }\n        }\n    }[\"RichTextEditor.useEffect\"], [\n        insertImageUrl\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RichTextEditor.useEffect\": ()=>{\n            const handleScroll = {\n                \"RichTextEditor.useEffect.handleScroll\": ()=>{\n                    if (editorRef.current) {\n                        const rect = editorRef.current.getBoundingClientRect();\n                        setIsToolbarSticky(rect.top <= 100);\n                    }\n                }\n            }[\"RichTextEditor.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"RichTextEditor.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"RichTextEditor.useEffect\"];\n        }\n    }[\"RichTextEditor.useEffect\"], []);\n    const markdownToHtml = (markdown)=>{\n        return markdown.replace(/^### (.*$)/gim, '<h3>$1</h3>').replace(/^## (.*$)/gim, '<h2>$1</h2>').replace(/^# (.*$)/gim, '<h1>$1</h1>').replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>').replace(/\\*(.*?)\\*/g, '<em>$1</em>').replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, '<img src=\"$2\" alt=\"$1\" style=\"max-width: 100%; height: auto; margin: 10px 0;\" />').replace(/\\n\\n/g, '</p><p>').replace(/^(.+)$/gm, '<p>$1</p>').replace(/<p><\\/p>/g, '').replace(/<p>(<h[1-6]>.*<\\/h[1-6]>)<\\/p>/g, '$1').replace(/<p>(<img[^>]*>)<\\/p>/g, '<div style=\"text-align: center; margin: 20px 0;\">$1</div>');\n    };\n    const htmlToMarkdown = (html)=>{\n        return html.replace(/<h1>(.*?)<\\/h1>/g, '# $1\\n\\n').replace(/<h2>(.*?)<\\/h2>/g, '## $1\\n\\n').replace(/<h3>(.*?)<\\/h3>/g, '### $1\\n\\n').replace(/<strong>(.*?)<\\/strong>/g, '**$1**').replace(/<em>(.*?)<\\/em>/g, '*$1*').replace(/<div[^>]*><img src=\"([^\"]*)\" alt=\"([^\"]*)\"[^>]*><\\/div>/g, '![${2}](${1})\\n\\n').replace(/<img src=\"([^\"]*)\" alt=\"([^\"]*)\"[^>]*>/g, '![${2}](${1})\\n\\n').replace(/<p>(.*?)<\\/p>/g, '$1\\n\\n').replace(/<br\\s*\\/?>/g, '\\n').replace(/&nbsp;/g, ' ').replace(/\\n{3,}/g, '\\n\\n').trim();\n    };\n    const handleInput = ()=>{\n        if (editorRef.current) {\n            const html = editorRef.current.innerHTML;\n            const markdown = htmlToMarkdown(html);\n            onChange(markdown);\n        }\n    };\n    const execCommand = (command, value)=>{\n        var _editorRef_current;\n        // eslint-disable-next-line deprecation/deprecation\n        document.execCommand(command, false, value);\n        handleInput();\n        (_editorRef_current = editorRef.current) === null || _editorRef_current === void 0 ? void 0 : _editorRef_current.focus();\n    };\n    const insertHeading = (level)=>{\n        const selection = window.getSelection();\n        if (selection && selection.rangeCount > 0) {\n            const range = selection.getRangeAt(0);\n            const selectedText = range.toString() || 'Заголовок';\n            const heading = document.createElement(\"h\".concat(level));\n            heading.textContent = selectedText;\n            range.deleteContents();\n            range.insertNode(heading);\n            // Move cursor after heading\n            range.setStartAfter(heading);\n            range.setEndAfter(heading);\n            selection.removeAllRanges();\n            selection.addRange(range);\n            handleInput();\n        }\n    };\n    const insertImage = ()=>{\n        if (onImageInsert) {\n            onImageInsert();\n        }\n    };\n    const insertQuote = ()=>{\n        const selection = window.getSelection();\n        if (selection && selection.rangeCount > 0) {\n            const range = selection.getRangeAt(0);\n            const selectedText = range.toString() || 'Цитата';\n            const blockquote = document.createElement('blockquote');\n            blockquote.style.borderLeft = '4px solid #e5e7eb';\n            blockquote.style.paddingLeft = '1rem';\n            blockquote.style.margin = '1rem 0';\n            blockquote.style.fontStyle = 'italic';\n            blockquote.style.color = '#6b7280';\n            blockquote.textContent = selectedText;\n            range.deleteContents();\n            range.insertNode(blockquote);\n            range.setStartAfter(blockquote);\n            range.setEndAfter(blockquote);\n            selection.removeAllRanges();\n            selection.addRange(range);\n            handleInput();\n        }\n    };\n    const insertCode = ()=>{\n        const selection = window.getSelection();\n        if (selection && selection.rangeCount > 0) {\n            const range = selection.getRangeAt(0);\n            const selectedText = range.toString() || 'код';\n            const code = document.createElement('code');\n            code.style.backgroundColor = '#f3f4f6';\n            code.style.padding = '0.25rem 0.5rem';\n            code.style.borderRadius = '0.25rem';\n            code.style.fontFamily = 'monospace';\n            code.style.fontSize = '0.875rem';\n            code.textContent = selectedText;\n            range.deleteContents();\n            range.insertNode(code);\n            range.setStartAfter(code);\n            range.setEndAfter(code);\n            selection.removeAllRanges();\n            selection.addRange(range);\n            handleInput();\n        }\n    };\n    const insertLink = ()=>{\n        const url = prompt('Введите URL ссылки:');\n        if (url) {\n            execCommand('createLink', url);\n        }\n    };\n    const toolbarButtons = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M6 2l3 6H5l3 6\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 249,\n                columnNumber: 9\n            }, undefined),\n            title: 'Заголовок 1',\n            action: ()=>insertHeading(1)\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M6 4l3 4H5l3 4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 258,\n                columnNumber: 9\n            }, undefined),\n            title: 'Заголовок 2',\n            action: ()=>insertHeading(2)\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M6 6l3 2H5l3 2\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 267,\n                columnNumber: 9\n            }, undefined),\n            title: 'Заголовок 3',\n            action: ()=>insertHeading(3)\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                children: \"B\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 276,\n                columnNumber: 13\n            }, undefined),\n            title: 'Жирный',\n            action: ()=>execCommand('bold')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                children: \"I\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 281,\n                columnNumber: 13\n            }, undefined),\n            title: 'Курсив',\n            action: ()=>execCommand('italic')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"u\", {\n                children: \"U\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 286,\n                columnNumber: 13\n            }, undefined),\n            title: 'Подчеркнутый',\n            action: ()=>execCommand('underline')\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M4 6h16M4 12h8m-8 6h16\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 293,\n                columnNumber: 9\n            }, undefined),\n            title: 'По левому краю',\n            action: ()=>execCommand('justifyLeft')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M4 6h16M4 12h16M4 18h16\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 302,\n                columnNumber: 9\n            }, undefined),\n            title: 'По центру',\n            action: ()=>execCommand('justifyCenter')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M4 6h16M4 12h16M4 18h12\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 311,\n                columnNumber: 9\n            }, undefined),\n            title: 'По правому краю',\n            action: ()=>execCommand('justifyRight')\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M4 6h16M4 10h16M4 14h16M4 18h16\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 321,\n                columnNumber: 9\n            }, undefined),\n            title: 'Список',\n            action: ()=>execCommand('insertUnorderedList')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M7 20l4-16m2 16l4-16M6 9h14M4 15h14\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 331,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 330,\n                columnNumber: 9\n            }, undefined),\n            title: 'Нумерованный список',\n            action: ()=>execCommand('insertOrderedList')\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v6a2 2 0 01-2 2h-2l-4 4z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 341,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 340,\n                columnNumber: 9\n            }, undefined),\n            title: 'Цитата',\n            action: insertQuote\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 349,\n                columnNumber: 9\n            }, undefined),\n            title: 'Код',\n            action: insertCode\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 360,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 359,\n                columnNumber: 9\n            }, undefined),\n            title: 'Ссылка',\n            action: insertLink\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 369,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 368,\n                columnNumber: 9\n            }, undefined),\n            title: 'Изображение',\n            action: insertImage\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M7 16l-4-4m0 0l4-4m-4 4h18\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 378,\n                columnNumber: 9\n            }, undefined),\n            title: 'Отменить',\n            action: ()=>execCommand('undo')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M17 8l4 4m0 0l-4 4m4-4H3\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 388,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 387,\n                columnNumber: 9\n            }, undefined),\n            title: 'Повторить',\n            action: ()=>execCommand('redo')\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-378f0ccdb29f75a1\" + \" \" + \"border border-gray-300 rounded-lg overflow-hidden \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-378f0ccdb29f75a1\" + \" \" + \"bg-gray-50 border-b border-gray-300 p-2 \".concat(isToolbarSticky ? 'sticky top-0 z-10 shadow-md' : ''),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-378f0ccdb29f75a1\" + \" \" + \"flex flex-wrap items-center gap-1\",\n                    children: toolbarButtons.map((button, index)=>{\n                        if (button.type === 'separator') {\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-378f0ccdb29f75a1\" + \" \" + \"w-px h-6 bg-gray-300 mx-1\"\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 22\n                            }, undefined);\n                        }\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: button.action,\n                            title: button.title,\n                            className: \"jsx-378f0ccdb29f75a1\" + \" \" + \"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors\",\n                            children: button.icon\n                        }, index, false, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 400,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 399,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: editorRef,\n                contentEditable: true,\n                onInput: handleInput,\n                onPaste: ()=>{\n                    // Handle paste to maintain formatting\n                    setTimeout(handleInput, 0);\n                },\n                style: {\n                    lineHeight: '1.6'\n                },\n                suppressContentEditableWarning: true,\n                \"data-placeholder\": placeholder,\n                className: \"jsx-378f0ccdb29f75a1\" + \" \" + \"min-h-[400px] p-4 focus:outline-none prose prose-lg max-w-none\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 422,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"378f0ccdb29f75a1\",\n                children: '[contenteditable].jsx-378f0ccdb29f75a1:empty:before{content:attr(data-placeholder);color:#9ca3af;pointer-events:none}[contenteditable].jsx-378f0ccdb29f75a1 h1.jsx-378f0ccdb29f75a1{font-size:2rem;font-weight:bold;margin:1rem 0;line-height:1.2}[contenteditable].jsx-378f0ccdb29f75a1 h2.jsx-378f0ccdb29f75a1{font-size:1.5rem;font-weight:bold;margin:1rem 0;line-height:1.3}[contenteditable].jsx-378f0ccdb29f75a1 h3.jsx-378f0ccdb29f75a1{font-size:1.25rem;font-weight:bold;margin:1rem 0;line-height:1.4}[contenteditable].jsx-378f0ccdb29f75a1 p.jsx-378f0ccdb29f75a1{margin:.5rem 0;line-height:1.6}[contenteditable].jsx-378f0ccdb29f75a1 ul.jsx-378f0ccdb29f75a1,[contenteditable].jsx-378f0ccdb29f75a1 ol.jsx-378f0ccdb29f75a1{margin:1rem 0;padding-left:2rem}[contenteditable].jsx-378f0ccdb29f75a1 li.jsx-378f0ccdb29f75a1{margin:.25rem 0}[contenteditable].jsx-378f0ccdb29f75a1 img.jsx-378f0ccdb29f75a1{max-width:100%;height:auto;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;margin:1rem 0}[contenteditable].jsx-378f0ccdb29f75a1 a.jsx-378f0ccdb29f75a1{color:#3b82f6;text-decoration:underline}[contenteditable].jsx-378f0ccdb29f75a1 a.jsx-378f0ccdb29f75a1:hover{color:#1d4ed8}[contenteditable].jsx-378f0ccdb29f75a1 blockquote.jsx-378f0ccdb29f75a1{border-left:4px solid#e5e7eb;padding-left:1rem;margin:1rem 0;font-style:italic;color:#6b7280;background-color:#f9fafb;padding:1rem;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem}[contenteditable].jsx-378f0ccdb29f75a1 code.jsx-378f0ccdb29f75a1{background-color:#f3f4f6;padding:.25rem .5rem;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;font-family:\"Monaco\",\"Menlo\",\"Ubuntu Mono\",monospace;font-size:.875rem;color:#1f2937}[contenteditable].jsx-378f0ccdb29f75a1 pre.jsx-378f0ccdb29f75a1{background-color:#1f2937;color:#f9fafb;padding:1rem;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;overflow-x:auto;margin:1rem 0}[contenteditable].jsx-378f0ccdb29f75a1 pre.jsx-378f0ccdb29f75a1 code.jsx-378f0ccdb29f75a1{background-color:transparent;padding:0;color:inherit}'\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n        lineNumber: 397,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RichTextEditor, \"Ci948IHb2xziu5x8jt1DMbhmlUI=\");\n_c = RichTextEditor;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RichTextEditor);\nvar _c;\n$RefreshReg$(_c, \"RichTextEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2FkbWluL1JpY2hUZXh0RWRpdG9yLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFMkQ7QUFrQjNELE1BQU1JLGlCQUFnRDtRQUFDLEVBQ3JEQyxLQUFLLEVBQ0xDLFFBQVEsRUFDUkMsYUFBYSxFQUNiQyxjQUFjLG1CQUFtQixFQUNqQ0MsWUFBWSxFQUFFLEVBQ2RDLGNBQWMsRUFDZjs7SUFDQyxNQUFNQyxZQUFZVCw2Q0FBTUEsQ0FBaUI7SUFDekMsTUFBTSxDQUFDVSxpQkFBaUJDLG1CQUFtQixHQUFHWiwrQ0FBUUEsQ0FBQztJQUV2REUsZ0RBQVNBO29DQUFDO1lBQ1IsdUNBQXVDO1lBQ3ZDLElBQUlRLFVBQVVHLE9BQU8sSUFBSVQsVUFBVU0sVUFBVUcsT0FBTyxDQUFDQyxTQUFTLEVBQUU7Z0JBQzlESixVQUFVRyxPQUFPLENBQUNDLFNBQVMsR0FBR0MsZUFBZVg7WUFDL0M7UUFDRjttQ0FBRztRQUFDQTtLQUFNO0lBRVYsa0NBQWtDO0lBQ2xDLE1BQU1ZLHdCQUF3QixDQUFDQztRQUM3QixJQUFJUCxVQUFVRyxPQUFPLEVBQUU7WUFDckIsNEJBQTRCO1lBQzVCSCxVQUFVRyxPQUFPLENBQUNLLEtBQUs7WUFFdkIsc0JBQXNCO1lBQ3RCLE1BQU1DLE1BQU1DLFNBQVNDLGFBQWEsQ0FBQztZQUNuQ0YsSUFBSUcsR0FBRyxHQUFHTDtZQUNWRSxJQUFJSSxHQUFHLEdBQUc7WUFDVkosSUFBSUssS0FBSyxDQUFDQyxRQUFRLEdBQUc7WUFDckJOLElBQUlLLEtBQUssQ0FBQ0UsTUFBTSxHQUFHO1lBQ25CUCxJQUFJSyxLQUFLLENBQUNHLE1BQU0sR0FBRztZQUNuQlIsSUFBSUssS0FBSyxDQUFDSSxZQUFZLEdBQUc7WUFFekIsbUNBQW1DO1lBQ25DLE1BQU1DLFlBQVlDLE9BQU9DLFlBQVk7WUFDckMsSUFBSUYsYUFBYUEsVUFBVUcsVUFBVSxHQUFHLEdBQUc7Z0JBQ3pDLE1BQU1DLFFBQVFKLFVBQVVLLFVBQVUsQ0FBQztnQkFFbkMsMENBQTBDO2dCQUMxQ0QsTUFBTUUsY0FBYyxJQUFJLHNDQUFzQztnQkFDOURGLE1BQU1HLFVBQVUsQ0FBQ2pCO2dCQUVqQiw2Q0FBNkM7Z0JBQzdDLE1BQU1rQixLQUFLakIsU0FBU0MsYUFBYSxDQUFDO2dCQUNsQ1ksTUFBTUssYUFBYSxDQUFDbkI7Z0JBQ3BCYyxNQUFNRyxVQUFVLENBQUNDO2dCQUVqQix5Q0FBeUM7Z0JBQ3pDSixNQUFNSyxhQUFhLENBQUNEO2dCQUNwQkosTUFBTU0sV0FBVyxDQUFDRjtnQkFDbEJSLFVBQVVXLGVBQWU7Z0JBQ3pCWCxVQUFVWSxRQUFRLENBQUNSO1lBQ3JCLE9BQU87Z0JBQ0wsd0NBQXdDO2dCQUN4Q3ZCLFVBQVVHLE9BQU8sQ0FBQzZCLFdBQVcsQ0FBQ3ZCO2dCQUM5QixNQUFNa0IsS0FBS2pCLFNBQVNDLGFBQWEsQ0FBQztnQkFDbENYLFVBQVVHLE9BQU8sQ0FBQzZCLFdBQVcsQ0FBQ0w7WUFDaEM7WUFFQSx1QkFBdUI7WUFDdkJNO1FBQ0Y7SUFDRjtJQUVBekMsZ0RBQVNBO29DQUFDO1lBQ1IsNkNBQTZDO1lBQzdDNEIsT0FBT2QscUJBQXFCLEdBQUdBO1lBRS9COzRDQUFPO29CQUNMLE9BQU9jLE9BQU9kLHFCQUFxQjtnQkFDckM7O1FBQ0Y7bUNBQUcsRUFBRTtJQUVMLDJDQUEyQztJQUMzQ2QsZ0RBQVNBO29DQUFDO1lBQ1IsSUFBSU8sZ0JBQWdCO2dCQUNsQk8sc0JBQXNCUDtZQUN4QjtRQUNGO21DQUFHO1FBQUNBO0tBQWU7SUFFbkJQLGdEQUFTQTtvQ0FBQztZQUNSLE1BQU0wQzt5REFBZTtvQkFDbkIsSUFBSWxDLFVBQVVHLE9BQU8sRUFBRTt3QkFDckIsTUFBTWdDLE9BQU9uQyxVQUFVRyxPQUFPLENBQUNpQyxxQkFBcUI7d0JBQ3BEbEMsbUJBQW1CaUMsS0FBS0UsR0FBRyxJQUFJO29CQUNqQztnQkFDRjs7WUFFQWpCLE9BQU9rQixnQkFBZ0IsQ0FBQyxVQUFVSjtZQUNsQzs0Q0FBTyxJQUFNZCxPQUFPbUIsbUJBQW1CLENBQUMsVUFBVUw7O1FBQ3BEO21DQUFHLEVBQUU7SUFFTCxNQUFNN0IsaUJBQWlCLENBQUNtQztRQUN0QixPQUFPQSxTQUNKQyxPQUFPLENBQUMsaUJBQWlCLGVBQ3pCQSxPQUFPLENBQUMsZ0JBQWdCLGVBQ3hCQSxPQUFPLENBQUMsZUFBZSxlQUN2QkEsT0FBTyxDQUFDLGtCQUFrQix1QkFDMUJBLE9BQU8sQ0FBQyxjQUFjLGVBQ3RCQSxPQUFPLENBQUMsNkJBQTZCLG9GQUNyQ0EsT0FBTyxDQUFDLFNBQVMsV0FDakJBLE9BQU8sQ0FBQyxZQUFZLGFBQ3BCQSxPQUFPLENBQUMsYUFBYSxJQUNyQkEsT0FBTyxDQUFDLG1DQUFtQyxNQUMzQ0EsT0FBTyxDQUFDLHlCQUF5QjtJQUN0QztJQUVBLE1BQU1DLGlCQUFpQixDQUFDQztRQUN0QixPQUFPQSxLQUNKRixPQUFPLENBQUMsb0JBQW9CLFlBQzVCQSxPQUFPLENBQUMsb0JBQW9CLGFBQzVCQSxPQUFPLENBQUMsb0JBQW9CLGNBQzVCQSxPQUFPLENBQUMsNEJBQTRCLFVBQ3BDQSxPQUFPLENBQUMsb0JBQW9CLFFBQzVCQSxPQUFPLENBQUMsNERBQTRELHFCQUNwRUEsT0FBTyxDQUFDLDJDQUEyQyxxQkFDbkRBLE9BQU8sQ0FBQyxrQkFBa0IsVUFDMUJBLE9BQU8sQ0FBQyxlQUFlLE1BQ3ZCQSxPQUFPLENBQUMsV0FBVyxLQUNuQkEsT0FBTyxDQUFDLFdBQVcsUUFDbkJHLElBQUk7SUFDVDtJQUVBLE1BQU1YLGNBQWM7UUFDbEIsSUFBSWpDLFVBQVVHLE9BQU8sRUFBRTtZQUNyQixNQUFNd0MsT0FBTzNDLFVBQVVHLE9BQU8sQ0FBQ0MsU0FBUztZQUN4QyxNQUFNb0MsV0FBV0UsZUFBZUM7WUFDaENoRCxTQUFTNkM7UUFDWDtJQUNGO0lBRUEsTUFBTUssY0FBYyxDQUFDQyxTQUFpQnBEO1lBSXBDTTtRQUhBLG1EQUFtRDtRQUNuRFUsU0FBU21DLFdBQVcsQ0FBQ0MsU0FBUyxPQUFPcEQ7UUFDckN1QztTQUNBakMscUJBQUFBLFVBQVVHLE9BQU8sY0FBakJILHlDQUFBQSxtQkFBbUJRLEtBQUs7SUFDMUI7SUFFQSxNQUFNdUMsZ0JBQWdCLENBQUNDO1FBQ3JCLE1BQU03QixZQUFZQyxPQUFPQyxZQUFZO1FBQ3JDLElBQUlGLGFBQWFBLFVBQVVHLFVBQVUsR0FBRyxHQUFHO1lBQ3pDLE1BQU1DLFFBQVFKLFVBQVVLLFVBQVUsQ0FBQztZQUNuQyxNQUFNeUIsZUFBZTFCLE1BQU0yQixRQUFRLE1BQU07WUFFekMsTUFBTUMsVUFBVXpDLFNBQVNDLGFBQWEsQ0FBQyxJQUFVLE9BQU5xQztZQUMzQ0csUUFBUUMsV0FBVyxHQUFHSDtZQUV0QjFCLE1BQU1FLGNBQWM7WUFDcEJGLE1BQU1HLFVBQVUsQ0FBQ3lCO1lBRWpCLDRCQUE0QjtZQUM1QjVCLE1BQU1LLGFBQWEsQ0FBQ3VCO1lBQ3BCNUIsTUFBTU0sV0FBVyxDQUFDc0I7WUFDbEJoQyxVQUFVVyxlQUFlO1lBQ3pCWCxVQUFVWSxRQUFRLENBQUNSO1lBRW5CVTtRQUNGO0lBQ0Y7SUFFQSxNQUFNb0IsY0FBYztRQUNsQixJQUFJekQsZUFBZTtZQUNqQkE7UUFDRjtJQUNGO0lBRUEsTUFBTTBELGNBQWM7UUFDbEIsTUFBTW5DLFlBQVlDLE9BQU9DLFlBQVk7UUFDckMsSUFBSUYsYUFBYUEsVUFBVUcsVUFBVSxHQUFHLEdBQUc7WUFDekMsTUFBTUMsUUFBUUosVUFBVUssVUFBVSxDQUFDO1lBQ25DLE1BQU15QixlQUFlMUIsTUFBTTJCLFFBQVEsTUFBTTtZQUV6QyxNQUFNSyxhQUFhN0MsU0FBU0MsYUFBYSxDQUFDO1lBQzFDNEMsV0FBV3pDLEtBQUssQ0FBQzBDLFVBQVUsR0FBRztZQUM5QkQsV0FBV3pDLEtBQUssQ0FBQzJDLFdBQVcsR0FBRztZQUMvQkYsV0FBV3pDLEtBQUssQ0FBQ0csTUFBTSxHQUFHO1lBQzFCc0MsV0FBV3pDLEtBQUssQ0FBQzRDLFNBQVMsR0FBRztZQUM3QkgsV0FBV3pDLEtBQUssQ0FBQzZDLEtBQUssR0FBRztZQUN6QkosV0FBV0gsV0FBVyxHQUFHSDtZQUV6QjFCLE1BQU1FLGNBQWM7WUFDcEJGLE1BQU1HLFVBQVUsQ0FBQzZCO1lBRWpCaEMsTUFBTUssYUFBYSxDQUFDMkI7WUFDcEJoQyxNQUFNTSxXQUFXLENBQUMwQjtZQUNsQnBDLFVBQVVXLGVBQWU7WUFDekJYLFVBQVVZLFFBQVEsQ0FBQ1I7WUFFbkJVO1FBQ0Y7SUFDRjtJQUVBLE1BQU0yQixhQUFhO1FBQ2pCLE1BQU16QyxZQUFZQyxPQUFPQyxZQUFZO1FBQ3JDLElBQUlGLGFBQWFBLFVBQVVHLFVBQVUsR0FBRyxHQUFHO1lBQ3pDLE1BQU1DLFFBQVFKLFVBQVVLLFVBQVUsQ0FBQztZQUNuQyxNQUFNeUIsZUFBZTFCLE1BQU0yQixRQUFRLE1BQU07WUFFekMsTUFBTVcsT0FBT25ELFNBQVNDLGFBQWEsQ0FBQztZQUNwQ2tELEtBQUsvQyxLQUFLLENBQUNnRCxlQUFlLEdBQUc7WUFDN0JELEtBQUsvQyxLQUFLLENBQUNpRCxPQUFPLEdBQUc7WUFDckJGLEtBQUsvQyxLQUFLLENBQUNJLFlBQVksR0FBRztZQUMxQjJDLEtBQUsvQyxLQUFLLENBQUNrRCxVQUFVLEdBQUc7WUFDeEJILEtBQUsvQyxLQUFLLENBQUNtRCxRQUFRLEdBQUc7WUFDdEJKLEtBQUtULFdBQVcsR0FBR0g7WUFFbkIxQixNQUFNRSxjQUFjO1lBQ3BCRixNQUFNRyxVQUFVLENBQUNtQztZQUVqQnRDLE1BQU1LLGFBQWEsQ0FBQ2lDO1lBQ3BCdEMsTUFBTU0sV0FBVyxDQUFDZ0M7WUFDbEIxQyxVQUFVVyxlQUFlO1lBQ3pCWCxVQUFVWSxRQUFRLENBQUNSO1lBRW5CVTtRQUNGO0lBQ0Y7SUFFQSxNQUFNaUMsYUFBYTtRQUNqQixNQUFNQyxNQUFNQyxPQUFPO1FBQ25CLElBQUlELEtBQUs7WUFDUHRCLFlBQVksY0FBY3NCO1FBQzVCO0lBQ0Y7SUFFQSxNQUFNRSxpQkFBaUI7UUFDckI7WUFDRUMsb0JBQ0UsOERBQUNDO2dCQUFJekUsV0FBVTtnQkFBVTBFLE1BQUs7Z0JBQU9DLFFBQU87Z0JBQWVDLFNBQVE7MEJBQ2pFLDRFQUFDQztvQkFBS0MsZUFBYztvQkFBUUMsZ0JBQWU7b0JBQVFDLGFBQWE7b0JBQUdDLEdBQUU7Ozs7Ozs7Ozs7O1lBR3pFQyxPQUFPO1lBQ1BDLFFBQVEsSUFBTWxDLGNBQWM7UUFDOUI7UUFDQTtZQUNFdUIsb0JBQ0UsOERBQUNDO2dCQUFJekUsV0FBVTtnQkFBVTBFLE1BQUs7Z0JBQU9DLFFBQU87Z0JBQWVDLFNBQVE7MEJBQ2pFLDRFQUFDQztvQkFBS0MsZUFBYztvQkFBUUMsZ0JBQWU7b0JBQVFDLGFBQWE7b0JBQUdDLEdBQUU7Ozs7Ozs7Ozs7O1lBR3pFQyxPQUFPO1lBQ1BDLFFBQVEsSUFBTWxDLGNBQWM7UUFDOUI7UUFDQTtZQUNFdUIsb0JBQ0UsOERBQUNDO2dCQUFJekUsV0FBVTtnQkFBVTBFLE1BQUs7Z0JBQU9DLFFBQU87Z0JBQWVDLFNBQVE7MEJBQ2pFLDRFQUFDQztvQkFBS0MsZUFBYztvQkFBUUMsZ0JBQWU7b0JBQVFDLGFBQWE7b0JBQUdDLEdBQUU7Ozs7Ozs7Ozs7O1lBR3pFQyxPQUFPO1lBQ1BDLFFBQVEsSUFBTWxDLGNBQWM7UUFDOUI7UUFDQTtZQUFFbUMsTUFBTTtRQUFZO1FBQ3BCO1lBQ0VaLG9CQUFNLDhEQUFDYTswQkFBTzs7Ozs7O1lBQ2RILE9BQU87WUFDUEMsUUFBUSxJQUFNcEMsWUFBWTtRQUM1QjtRQUNBO1lBQ0V5QixvQkFBTSw4REFBQ2M7MEJBQUc7Ozs7OztZQUNWSixPQUFPO1lBQ1BDLFFBQVEsSUFBTXBDLFlBQVk7UUFDNUI7UUFDQTtZQUNFeUIsb0JBQU0sOERBQUNlOzBCQUFFOzs7Ozs7WUFDVEwsT0FBTztZQUNQQyxRQUFRLElBQU1wQyxZQUFZO1FBQzVCO1FBQ0E7WUFBRXFDLE1BQU07UUFBWTtRQUNwQjtZQUNFWixvQkFDRSw4REFBQ0M7Z0JBQUl6RSxXQUFVO2dCQUFVMEUsTUFBSztnQkFBT0MsUUFBTztnQkFBZUMsU0FBUTswQkFDakUsNEVBQUNDO29CQUFLQyxlQUFjO29CQUFRQyxnQkFBZTtvQkFBUUMsYUFBYTtvQkFBR0MsR0FBRTs7Ozs7Ozs7Ozs7WUFHekVDLE9BQU87WUFDUEMsUUFBUSxJQUFNcEMsWUFBWTtRQUM1QjtRQUNBO1lBQ0V5QixvQkFDRSw4REFBQ0M7Z0JBQUl6RSxXQUFVO2dCQUFVMEUsTUFBSztnQkFBT0MsUUFBTztnQkFBZUMsU0FBUTswQkFDakUsNEVBQUNDO29CQUFLQyxlQUFjO29CQUFRQyxnQkFBZTtvQkFBUUMsYUFBYTtvQkFBR0MsR0FBRTs7Ozs7Ozs7Ozs7WUFHekVDLE9BQU87WUFDUEMsUUFBUSxJQUFNcEMsWUFBWTtRQUM1QjtRQUNBO1lBQ0V5QixvQkFDRSw4REFBQ0M7Z0JBQUl6RSxXQUFVO2dCQUFVMEUsTUFBSztnQkFBT0MsUUFBTztnQkFBZUMsU0FBUTswQkFDakUsNEVBQUNDO29CQUFLQyxlQUFjO29CQUFRQyxnQkFBZTtvQkFBUUMsYUFBYTtvQkFBR0MsR0FBRTs7Ozs7Ozs7Ozs7WUFHekVDLE9BQU87WUFDUEMsUUFBUSxJQUFNcEMsWUFBWTtRQUM1QjtRQUNBO1lBQUVxQyxNQUFNO1FBQVk7UUFDcEI7WUFDRVosb0JBQ0UsOERBQUNDO2dCQUFJekUsV0FBVTtnQkFBVTBFLE1BQUs7Z0JBQU9DLFFBQU87Z0JBQWVDLFNBQVE7MEJBQ2pFLDRFQUFDQztvQkFBS0MsZUFBYztvQkFBUUMsZ0JBQWU7b0JBQVFDLGFBQWE7b0JBQUdDLEdBQUU7Ozs7Ozs7Ozs7O1lBR3pFQyxPQUFPO1lBQ1BDLFFBQVEsSUFBTXBDLFlBQVk7UUFDNUI7UUFDQTtZQUNFeUIsb0JBQ0UsOERBQUNDO2dCQUFJekUsV0FBVTtnQkFBVTBFLE1BQUs7Z0JBQU9DLFFBQU87Z0JBQWVDLFNBQVE7MEJBQ2pFLDRFQUFDQztvQkFBS0MsZUFBYztvQkFBUUMsZ0JBQWU7b0JBQVFDLGFBQWE7b0JBQUdDLEdBQUU7Ozs7Ozs7Ozs7O1lBR3pFQyxPQUFPO1lBQ1BDLFFBQVEsSUFBTXBDLFlBQVk7UUFDNUI7UUFDQTtZQUFFcUMsTUFBTTtRQUFZO1FBQ3BCO1lBQ0VaLG9CQUNFLDhEQUFDQztnQkFBSXpFLFdBQVU7Z0JBQVUwRSxNQUFLO2dCQUFPQyxRQUFPO2dCQUFlQyxTQUFROzBCQUNqRSw0RUFBQ0M7b0JBQUtDLGVBQWM7b0JBQVFDLGdCQUFlO29CQUFRQyxhQUFhO29CQUFHQyxHQUFFOzs7Ozs7Ozs7OztZQUd6RUMsT0FBTztZQUNQQyxRQUFRM0I7UUFDVjtRQUNBO1lBQ0VnQixvQkFDRSw4REFBQ0M7Z0JBQUl6RSxXQUFVO2dCQUFVMEUsTUFBSztnQkFBT0MsUUFBTztnQkFBZUMsU0FBUTswQkFDakUsNEVBQUNDO29CQUFLQyxlQUFjO29CQUFRQyxnQkFBZTtvQkFBUUMsYUFBYTtvQkFBR0MsR0FBRTs7Ozs7Ozs7Ozs7WUFHekVDLE9BQU87WUFDUEMsUUFBUXJCO1FBQ1Y7UUFDQTtZQUFFc0IsTUFBTTtRQUFZO1FBQ3BCO1lBQ0VaLG9CQUNFLDhEQUFDQztnQkFBSXpFLFdBQVU7Z0JBQVUwRSxNQUFLO2dCQUFPQyxRQUFPO2dCQUFlQyxTQUFROzBCQUNqRSw0RUFBQ0M7b0JBQUtDLGVBQWM7b0JBQVFDLGdCQUFlO29CQUFRQyxhQUFhO29CQUFHQyxHQUFFOzs7Ozs7Ozs7OztZQUd6RUMsT0FBTztZQUNQQyxRQUFRZjtRQUNWO1FBQ0E7WUFDRUksb0JBQ0UsOERBQUNDO2dCQUFJekUsV0FBVTtnQkFBVTBFLE1BQUs7Z0JBQU9DLFFBQU87Z0JBQWVDLFNBQVE7MEJBQ2pFLDRFQUFDQztvQkFBS0MsZUFBYztvQkFBUUMsZ0JBQWU7b0JBQVFDLGFBQWE7b0JBQUdDLEdBQUU7Ozs7Ozs7Ozs7O1lBR3pFQyxPQUFPO1lBQ1BDLFFBQVE1QjtRQUNWO1FBQ0E7WUFBRTZCLE1BQU07UUFBWTtRQUNwQjtZQUNFWixvQkFDRSw4REFBQ0M7Z0JBQUl6RSxXQUFVO2dCQUFVMEUsTUFBSztnQkFBT0MsUUFBTztnQkFBZUMsU0FBUTswQkFDakUsNEVBQUNDO29CQUFLQyxlQUFjO29CQUFRQyxnQkFBZTtvQkFBUUMsYUFBYTtvQkFBR0MsR0FBRTs7Ozs7Ozs7Ozs7WUFHekVDLE9BQU87WUFDUEMsUUFBUSxJQUFNcEMsWUFBWTtRQUM1QjtRQUNBO1lBQ0V5QixvQkFDRSw4REFBQ0M7Z0JBQUl6RSxXQUFVO2dCQUFVMEUsTUFBSztnQkFBT0MsUUFBTztnQkFBZUMsU0FBUTswQkFDakUsNEVBQUNDO29CQUFLQyxlQUFjO29CQUFRQyxnQkFBZTtvQkFBUUMsYUFBYTtvQkFBR0MsR0FBRTs7Ozs7Ozs7Ozs7WUFHekVDLE9BQU87WUFDUEMsUUFBUSxJQUFNcEMsWUFBWTtRQUM1QjtLQUNEO0lBRUQscUJBQ0UsOERBQUN5QztrREFBZSxxREFBK0QsT0FBVnhGOzswQkFFbkUsOERBQUN3RjswREFBZSwyQ0FBZ0csT0FBckRyRixrQkFBa0IsZ0NBQWdDOzBCQUMzRyw0RUFBQ3FGOzhEQUFjOzhCQUNaakIsZUFBZWtCLEdBQUcsQ0FBQyxDQUFDQyxRQUFRQzt3QkFDM0IsSUFBSUQsT0FBT04sSUFBSSxLQUFLLGFBQWE7NEJBQy9CLHFCQUFPLDhEQUFDSTswRUFBMEI7K0JBQWpCRzs7Ozs7d0JBQ25CO3dCQUVBLHFCQUNFLDhEQUFDRDs0QkFFQ04sTUFBSzs0QkFDTFEsU0FBU0YsT0FBT1AsTUFBTTs0QkFDdEJELE9BQU9RLE9BQU9SLEtBQUs7c0VBQ1Q7c0NBRVRRLE9BQU9sQixJQUFJOzJCQU5QbUI7Ozs7O29CQVNYOzs7Ozs7Ozs7OzswQkFLSiw4REFBQ0g7Z0JBQ0NLLEtBQUszRjtnQkFDTDRGLGVBQWU7Z0JBQ2ZDLFNBQVM1RDtnQkFDVDZELFNBQVM7b0JBQ1Asc0NBQXNDO29CQUN0Q0MsV0FBVzlELGFBQWE7Z0JBQzFCO2dCQUVBbkIsT0FBTztvQkFDTGtGLFlBQVk7Z0JBQ2Q7Z0JBQ0FDLGdDQUFnQztnQkFDaENDLG9CQUFrQnJHOzBEQUxSOzs7Ozs7Ozs7Ozs7Ozs7O0FBdUdsQjtHQWhnQk1KO0tBQUFBO0FBa2dCTixpRUFBZUEsY0FBY0EsRUFBQyIsInNvdXJjZXMiOlsiL1VzZXJzL3ZpdGFsaWlsL0RvY3VtZW50cy9TZXRNZWUvc3JjL2NvbXBvbmVudHMvYWRtaW4vUmljaFRleHRFZGl0b3IudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VSZWYsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcblxuaW50ZXJmYWNlIFJpY2hUZXh0RWRpdG9yUHJvcHMge1xuICB2YWx1ZTogc3RyaW5nO1xuICBvbkNoYW5nZTogKHZhbHVlOiBzdHJpbmcpID0+IHZvaWQ7XG4gIG9uSW1hZ2VJbnNlcnQ/OiAoKSA9PiB2b2lkO1xuICBwbGFjZWhvbGRlcj86IHN0cmluZztcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xuICBvbkluc2VydEltYWdlPzogKGltYWdlVXJsOiBzdHJpbmcpID0+IHZvaWQ7IC8vIENhbGxiYWNrINC00LvRjyDQstGB0YLQsNCy0LrQuCDQuNC30L7QsdGA0LDQttC10L3QuNGPXG59XG5cbi8vIEdsb2JhbCBmdW5jdGlvbiB0byBpbnNlcnQgaW1hZ2UgaW50byBhY3RpdmUgZWRpdG9yXG5kZWNsYXJlIGdsb2JhbCB7XG4gIGludGVyZmFjZSBXaW5kb3cge1xuICAgIGluc2VydEltYWdlSW50b0VkaXRvcj86IChpbWFnZVVybDogc3RyaW5nKSA9PiB2b2lkO1xuICB9XG59XG5cbmNvbnN0IFJpY2hUZXh0RWRpdG9yOiBSZWFjdC5GQzxSaWNoVGV4dEVkaXRvclByb3BzPiA9ICh7XG4gIHZhbHVlLFxuICBvbkNoYW5nZSxcbiAgb25JbWFnZUluc2VydCxcbiAgcGxhY2Vob2xkZXIgPSBcItCd0LDRh9C90LjRgtC1INC/0LjRgdCw0YLRjC4uLlwiLFxuICBjbGFzc05hbWUgPSBcIlwiLFxuICBpbnNlcnRJbWFnZVVybFxufSkgPT4ge1xuICBjb25zdCBlZGl0b3JSZWYgPSB1c2VSZWY8SFRNTERpdkVsZW1lbnQ+KG51bGwpO1xuICBjb25zdCBbaXNUb29sYmFyU3RpY2t5LCBzZXRJc1Rvb2xiYXJTdGlja3ldID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gQ29udmVydCBtYXJrZG93biB0byBIVE1MIGZvciBkaXNwbGF5XG4gICAgaWYgKGVkaXRvclJlZi5jdXJyZW50ICYmIHZhbHVlICE9PSBlZGl0b3JSZWYuY3VycmVudC5pbm5lckhUTUwpIHtcbiAgICAgIGVkaXRvclJlZi5jdXJyZW50LmlubmVySFRNTCA9IG1hcmtkb3duVG9IdG1sKHZhbHVlKTtcbiAgICB9XG4gIH0sIFt2YWx1ZV0pO1xuXG4gIC8vINCk0YPQvdC60YbQuNGPINC00LvRjyDQstGB0YLQsNCy0LrQuCDQuNC30L7QsdGA0LDQttC10L3QuNGPXG4gIGNvbnN0IGluc2VydEltYWdlSW50b0VkaXRvciA9IChpbWFnZVVybDogc3RyaW5nKSA9PiB7XG4gICAgaWYgKGVkaXRvclJlZi5jdXJyZW50KSB7XG4gICAgICAvLyDQpNC+0LrRg9GB0LjRgNGD0LXQvNGB0Y8g0L3QsCDRgNC10LTQsNC60YLQvtGA0LVcbiAgICAgIGVkaXRvclJlZi5jdXJyZW50LmZvY3VzKCk7XG5cbiAgICAgIC8vINCh0L7Qt9C00LDQtdC8INC40LfQvtCx0YDQsNC20LXQvdC40LVcbiAgICAgIGNvbnN0IGltZyA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2ltZycpO1xuICAgICAgaW1nLnNyYyA9IGltYWdlVXJsO1xuICAgICAgaW1nLmFsdCA9ICfQmNC30L7QsdGA0LDQttC10L3QuNC1JztcbiAgICAgIGltZy5zdHlsZS5tYXhXaWR0aCA9ICcxMDAlJztcbiAgICAgIGltZy5zdHlsZS5oZWlnaHQgPSAnYXV0byc7XG4gICAgICBpbWcuc3R5bGUubWFyZ2luID0gJzEwcHggMCc7XG4gICAgICBpbWcuc3R5bGUuYm9yZGVyUmFkaXVzID0gJzAuNXJlbSc7XG5cbiAgICAgIC8vINCf0L7Qu9GD0YfQsNC10Lwg0YLQtdC60YPRidGD0Y4g0L/QvtC30LjRhtC40Y4g0LrRg9GA0YHQvtGA0LBcbiAgICAgIGNvbnN0IHNlbGVjdGlvbiA9IHdpbmRvdy5nZXRTZWxlY3Rpb24oKTtcbiAgICAgIGlmIChzZWxlY3Rpb24gJiYgc2VsZWN0aW9uLnJhbmdlQ291bnQgPiAwKSB7XG4gICAgICAgIGNvbnN0IHJhbmdlID0gc2VsZWN0aW9uLmdldFJhbmdlQXQoMCk7XG5cbiAgICAgICAgLy8g0JLRgdGC0LDQstC70Y/QtdC8INC40LfQvtCx0YDQsNC20LXQvdC40LUg0LIg0L/QvtC30LjRhtC40Y4g0LrRg9GA0YHQvtGA0LBcbiAgICAgICAgcmFuZ2UuZGVsZXRlQ29udGVudHMoKTsgLy8g0KPQtNCw0LvRj9C10Lwg0LLRi9C00LXQu9C10L3QvdGL0Lkg0YLQtdC60YHRgiwg0LXRgdC70Lgg0LXRgdGC0YxcbiAgICAgICAgcmFuZ2UuaW5zZXJ0Tm9kZShpbWcpO1xuXG4gICAgICAgIC8vINCU0L7QsdCw0LLQu9GP0LXQvCDQv9C10YDQtdC90L7RgSDRgdGC0YDQvtC60Lgg0L/QvtGB0LvQtSDQuNC30L7QsdGA0LDQttC10L3QuNGPXG4gICAgICAgIGNvbnN0IGJyID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYnInKTtcbiAgICAgICAgcmFuZ2Uuc2V0U3RhcnRBZnRlcihpbWcpO1xuICAgICAgICByYW5nZS5pbnNlcnROb2RlKGJyKTtcblxuICAgICAgICAvLyDQo9GB0YLQsNC90LDQstC70LjQstCw0LXQvCDQutGD0YDRgdC+0YAg0L/QvtGB0LvQtSDQuNC30L7QsdGA0LDQttC10L3QuNGPXG4gICAgICAgIHJhbmdlLnNldFN0YXJ0QWZ0ZXIoYnIpO1xuICAgICAgICByYW5nZS5zZXRFbmRBZnRlcihicik7XG4gICAgICAgIHNlbGVjdGlvbi5yZW1vdmVBbGxSYW5nZXMoKTtcbiAgICAgICAgc2VsZWN0aW9uLmFkZFJhbmdlKHJhbmdlKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vINCV0YHQu9C4INC90LXRgiDQstGL0LTQtdC70LXQvdC40Y8sINC00L7QsdCw0LLQu9GP0LXQvCDQsiDQutC+0L3QtdGGXG4gICAgICAgIGVkaXRvclJlZi5jdXJyZW50LmFwcGVuZENoaWxkKGltZyk7XG4gICAgICAgIGNvbnN0IGJyID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYnInKTtcbiAgICAgICAgZWRpdG9yUmVmLmN1cnJlbnQuYXBwZW5kQ2hpbGQoYnIpO1xuICAgICAgfVxuXG4gICAgICAvLyDQntCx0L3QvtCy0LvRj9C10Lwg0YHQvtC00LXRgNC20LjQvNC+0LVcbiAgICAgIGhhbmRsZUlucHV0KCk7XG4gICAgfVxuICB9O1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gU2V0IHVwIGdsb2JhbCBmdW5jdGlvbiBmb3IgaW1hZ2UgaW5zZXJ0aW9uXG4gICAgd2luZG93Lmluc2VydEltYWdlSW50b0VkaXRvciA9IGluc2VydEltYWdlSW50b0VkaXRvcjtcblxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBkZWxldGUgd2luZG93Lmluc2VydEltYWdlSW50b0VkaXRvcjtcbiAgICB9O1xuICB9LCBbXSk7XG5cbiAgLy8g0J7QsdGA0LDQsdC+0YLQutCwINCy0YHRgtCw0LLQutC4INC40LfQvtCx0YDQsNC20LXQvdC40Y8g0YfQtdGA0LXQtyBwcm9wXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGluc2VydEltYWdlVXJsKSB7XG4gICAgICBpbnNlcnRJbWFnZUludG9FZGl0b3IoaW5zZXJ0SW1hZ2VVcmwpO1xuICAgIH1cbiAgfSwgW2luc2VydEltYWdlVXJsXSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBoYW5kbGVTY3JvbGwgPSAoKSA9PiB7XG4gICAgICBpZiAoZWRpdG9yUmVmLmN1cnJlbnQpIHtcbiAgICAgICAgY29uc3QgcmVjdCA9IGVkaXRvclJlZi5jdXJyZW50LmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpO1xuICAgICAgICBzZXRJc1Rvb2xiYXJTdGlja3kocmVjdC50b3AgPD0gMTAwKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Njcm9sbCcsIGhhbmRsZVNjcm9sbCk7XG4gICAgcmV0dXJuICgpID0+IHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdzY3JvbGwnLCBoYW5kbGVTY3JvbGwpO1xuICB9LCBbXSk7XG5cbiAgY29uc3QgbWFya2Rvd25Ub0h0bWwgPSAobWFya2Rvd246IHN0cmluZyk6IHN0cmluZyA9PiB7XG4gICAgcmV0dXJuIG1hcmtkb3duXG4gICAgICAucmVwbGFjZSgvXiMjIyAoLiokKS9naW0sICc8aDM+JDE8L2gzPicpXG4gICAgICAucmVwbGFjZSgvXiMjICguKiQpL2dpbSwgJzxoMj4kMTwvaDI+JylcbiAgICAgIC5yZXBsYWNlKC9eIyAoLiokKS9naW0sICc8aDE+JDE8L2gxPicpXG4gICAgICAucmVwbGFjZSgvXFwqXFwqKC4qPylcXCpcXCovZywgJzxzdHJvbmc+JDE8L3N0cm9uZz4nKVxuICAgICAgLnJlcGxhY2UoL1xcKiguKj8pXFwqL2csICc8ZW0+JDE8L2VtPicpXG4gICAgICAucmVwbGFjZSgvIVxcWyhbXlxcXV0qKVxcXVxcKChbXildKylcXCkvZywgJzxpbWcgc3JjPVwiJDJcIiBhbHQ9XCIkMVwiIHN0eWxlPVwibWF4LXdpZHRoOiAxMDAlOyBoZWlnaHQ6IGF1dG87IG1hcmdpbjogMTBweCAwO1wiIC8+JylcbiAgICAgIC5yZXBsYWNlKC9cXG5cXG4vZywgJzwvcD48cD4nKVxuICAgICAgLnJlcGxhY2UoL14oLispJC9nbSwgJzxwPiQxPC9wPicpXG4gICAgICAucmVwbGFjZSgvPHA+PFxcL3A+L2csICcnKVxuICAgICAgLnJlcGxhY2UoLzxwPig8aFsxLTZdPi4qPFxcL2hbMS02XT4pPFxcL3A+L2csICckMScpXG4gICAgICAucmVwbGFjZSgvPHA+KDxpbWdbXj5dKj4pPFxcL3A+L2csICc8ZGl2IHN0eWxlPVwidGV4dC1hbGlnbjogY2VudGVyOyBtYXJnaW46IDIwcHggMDtcIj4kMTwvZGl2PicpO1xuICB9O1xuXG4gIGNvbnN0IGh0bWxUb01hcmtkb3duID0gKGh0bWw6IHN0cmluZyk6IHN0cmluZyA9PiB7XG4gICAgcmV0dXJuIGh0bWxcbiAgICAgIC5yZXBsYWNlKC88aDE+KC4qPyk8XFwvaDE+L2csICcjICQxXFxuXFxuJylcbiAgICAgIC5yZXBsYWNlKC88aDI+KC4qPyk8XFwvaDI+L2csICcjIyAkMVxcblxcbicpXG4gICAgICAucmVwbGFjZSgvPGgzPiguKj8pPFxcL2gzPi9nLCAnIyMjICQxXFxuXFxuJylcbiAgICAgIC5yZXBsYWNlKC88c3Ryb25nPiguKj8pPFxcL3N0cm9uZz4vZywgJyoqJDEqKicpXG4gICAgICAucmVwbGFjZSgvPGVtPiguKj8pPFxcL2VtPi9nLCAnKiQxKicpXG4gICAgICAucmVwbGFjZSgvPGRpdltePl0qPjxpbWcgc3JjPVwiKFteXCJdKilcIiBhbHQ9XCIoW15cIl0qKVwiW14+XSo+PFxcL2Rpdj4vZywgJyFbJHsyfV0oJHsxfSlcXG5cXG4nKVxuICAgICAgLnJlcGxhY2UoLzxpbWcgc3JjPVwiKFteXCJdKilcIiBhbHQ9XCIoW15cIl0qKVwiW14+XSo+L2csICchWyR7Mn1dKCR7MX0pXFxuXFxuJylcbiAgICAgIC5yZXBsYWNlKC88cD4oLio/KTxcXC9wPi9nLCAnJDFcXG5cXG4nKVxuICAgICAgLnJlcGxhY2UoLzxiclxccypcXC8/Pi9nLCAnXFxuJylcbiAgICAgIC5yZXBsYWNlKC8mbmJzcDsvZywgJyAnKVxuICAgICAgLnJlcGxhY2UoL1xcbnszLH0vZywgJ1xcblxcbicpXG4gICAgICAudHJpbSgpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUlucHV0ID0gKCkgPT4ge1xuICAgIGlmIChlZGl0b3JSZWYuY3VycmVudCkge1xuICAgICAgY29uc3QgaHRtbCA9IGVkaXRvclJlZi5jdXJyZW50LmlubmVySFRNTDtcbiAgICAgIGNvbnN0IG1hcmtkb3duID0gaHRtbFRvTWFya2Rvd24oaHRtbCk7XG4gICAgICBvbkNoYW5nZShtYXJrZG93bik7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGV4ZWNDb21tYW5kID0gKGNvbW1hbmQ6IHN0cmluZywgdmFsdWU/OiBzdHJpbmcpID0+IHtcbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgZGVwcmVjYXRpb24vZGVwcmVjYXRpb25cbiAgICBkb2N1bWVudC5leGVjQ29tbWFuZChjb21tYW5kLCBmYWxzZSwgdmFsdWUpO1xuICAgIGhhbmRsZUlucHV0KCk7XG4gICAgZWRpdG9yUmVmLmN1cnJlbnQ/LmZvY3VzKCk7XG4gIH07XG5cbiAgY29uc3QgaW5zZXJ0SGVhZGluZyA9IChsZXZlbDogbnVtYmVyKSA9PiB7XG4gICAgY29uc3Qgc2VsZWN0aW9uID0gd2luZG93LmdldFNlbGVjdGlvbigpO1xuICAgIGlmIChzZWxlY3Rpb24gJiYgc2VsZWN0aW9uLnJhbmdlQ291bnQgPiAwKSB7XG4gICAgICBjb25zdCByYW5nZSA9IHNlbGVjdGlvbi5nZXRSYW5nZUF0KDApO1xuICAgICAgY29uc3Qgc2VsZWN0ZWRUZXh0ID0gcmFuZ2UudG9TdHJpbmcoKSB8fCAn0JfQsNCz0L7Qu9C+0LLQvtC6JztcbiAgICAgIFxuICAgICAgY29uc3QgaGVhZGluZyA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoYGgke2xldmVsfWApO1xuICAgICAgaGVhZGluZy50ZXh0Q29udGVudCA9IHNlbGVjdGVkVGV4dDtcbiAgICAgIFxuICAgICAgcmFuZ2UuZGVsZXRlQ29udGVudHMoKTtcbiAgICAgIHJhbmdlLmluc2VydE5vZGUoaGVhZGluZyk7XG4gICAgICBcbiAgICAgIC8vIE1vdmUgY3Vyc29yIGFmdGVyIGhlYWRpbmdcbiAgICAgIHJhbmdlLnNldFN0YXJ0QWZ0ZXIoaGVhZGluZyk7XG4gICAgICByYW5nZS5zZXRFbmRBZnRlcihoZWFkaW5nKTtcbiAgICAgIHNlbGVjdGlvbi5yZW1vdmVBbGxSYW5nZXMoKTtcbiAgICAgIHNlbGVjdGlvbi5hZGRSYW5nZShyYW5nZSk7XG4gICAgICBcbiAgICAgIGhhbmRsZUlucHV0KCk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGluc2VydEltYWdlID0gKCkgPT4ge1xuICAgIGlmIChvbkltYWdlSW5zZXJ0KSB7XG4gICAgICBvbkltYWdlSW5zZXJ0KCk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGluc2VydFF1b3RlID0gKCkgPT4ge1xuICAgIGNvbnN0IHNlbGVjdGlvbiA9IHdpbmRvdy5nZXRTZWxlY3Rpb24oKTtcbiAgICBpZiAoc2VsZWN0aW9uICYmIHNlbGVjdGlvbi5yYW5nZUNvdW50ID4gMCkge1xuICAgICAgY29uc3QgcmFuZ2UgPSBzZWxlY3Rpb24uZ2V0UmFuZ2VBdCgwKTtcbiAgICAgIGNvbnN0IHNlbGVjdGVkVGV4dCA9IHJhbmdlLnRvU3RyaW5nKCkgfHwgJ9Cm0LjRgtCw0YLQsCc7XG5cbiAgICAgIGNvbnN0IGJsb2NrcXVvdGUgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdibG9ja3F1b3RlJyk7XG4gICAgICBibG9ja3F1b3RlLnN0eWxlLmJvcmRlckxlZnQgPSAnNHB4IHNvbGlkICNlNWU3ZWInO1xuICAgICAgYmxvY2txdW90ZS5zdHlsZS5wYWRkaW5nTGVmdCA9ICcxcmVtJztcbiAgICAgIGJsb2NrcXVvdGUuc3R5bGUubWFyZ2luID0gJzFyZW0gMCc7XG4gICAgICBibG9ja3F1b3RlLnN0eWxlLmZvbnRTdHlsZSA9ICdpdGFsaWMnO1xuICAgICAgYmxvY2txdW90ZS5zdHlsZS5jb2xvciA9ICcjNmI3MjgwJztcbiAgICAgIGJsb2NrcXVvdGUudGV4dENvbnRlbnQgPSBzZWxlY3RlZFRleHQ7XG5cbiAgICAgIHJhbmdlLmRlbGV0ZUNvbnRlbnRzKCk7XG4gICAgICByYW5nZS5pbnNlcnROb2RlKGJsb2NrcXVvdGUpO1xuXG4gICAgICByYW5nZS5zZXRTdGFydEFmdGVyKGJsb2NrcXVvdGUpO1xuICAgICAgcmFuZ2Uuc2V0RW5kQWZ0ZXIoYmxvY2txdW90ZSk7XG4gICAgICBzZWxlY3Rpb24ucmVtb3ZlQWxsUmFuZ2VzKCk7XG4gICAgICBzZWxlY3Rpb24uYWRkUmFuZ2UocmFuZ2UpO1xuXG4gICAgICBoYW5kbGVJbnB1dCgpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBpbnNlcnRDb2RlID0gKCkgPT4ge1xuICAgIGNvbnN0IHNlbGVjdGlvbiA9IHdpbmRvdy5nZXRTZWxlY3Rpb24oKTtcbiAgICBpZiAoc2VsZWN0aW9uICYmIHNlbGVjdGlvbi5yYW5nZUNvdW50ID4gMCkge1xuICAgICAgY29uc3QgcmFuZ2UgPSBzZWxlY3Rpb24uZ2V0UmFuZ2VBdCgwKTtcbiAgICAgIGNvbnN0IHNlbGVjdGVkVGV4dCA9IHJhbmdlLnRvU3RyaW5nKCkgfHwgJ9C60L7QtCc7XG5cbiAgICAgIGNvbnN0IGNvZGUgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdjb2RlJyk7XG4gICAgICBjb2RlLnN0eWxlLmJhY2tncm91bmRDb2xvciA9ICcjZjNmNGY2JztcbiAgICAgIGNvZGUuc3R5bGUucGFkZGluZyA9ICcwLjI1cmVtIDAuNXJlbSc7XG4gICAgICBjb2RlLnN0eWxlLmJvcmRlclJhZGl1cyA9ICcwLjI1cmVtJztcbiAgICAgIGNvZGUuc3R5bGUuZm9udEZhbWlseSA9ICdtb25vc3BhY2UnO1xuICAgICAgY29kZS5zdHlsZS5mb250U2l6ZSA9ICcwLjg3NXJlbSc7XG4gICAgICBjb2RlLnRleHRDb250ZW50ID0gc2VsZWN0ZWRUZXh0O1xuXG4gICAgICByYW5nZS5kZWxldGVDb250ZW50cygpO1xuICAgICAgcmFuZ2UuaW5zZXJ0Tm9kZShjb2RlKTtcblxuICAgICAgcmFuZ2Uuc2V0U3RhcnRBZnRlcihjb2RlKTtcbiAgICAgIHJhbmdlLnNldEVuZEFmdGVyKGNvZGUpO1xuICAgICAgc2VsZWN0aW9uLnJlbW92ZUFsbFJhbmdlcygpO1xuICAgICAgc2VsZWN0aW9uLmFkZFJhbmdlKHJhbmdlKTtcblxuICAgICAgaGFuZGxlSW5wdXQoKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaW5zZXJ0TGluayA9ICgpID0+IHtcbiAgICBjb25zdCB1cmwgPSBwcm9tcHQoJ9CS0LLQtdC00LjRgtC1IFVSTCDRgdGB0YvQu9C60Lg6Jyk7XG4gICAgaWYgKHVybCkge1xuICAgICAgZXhlY0NvbW1hbmQoJ2NyZWF0ZUxpbmsnLCB1cmwpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCB0b29sYmFyQnV0dG9ucyA9IFtcbiAgICB7XG4gICAgICBpY29uOiAoXG4gICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk02IDJsMyA2SDVsMyA2XCIgLz5cbiAgICAgICAgPC9zdmc+XG4gICAgICApLFxuICAgICAgdGl0bGU6ICfQl9Cw0LPQvtC70L7QstC+0LogMScsXG4gICAgICBhY3Rpb246ICgpID0+IGluc2VydEhlYWRpbmcoMSlcbiAgICB9LFxuICAgIHtcbiAgICAgIGljb246IChcbiAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTYgNGwzIDRINWwzIDRcIiAvPlxuICAgICAgICA8L3N2Zz5cbiAgICAgICksXG4gICAgICB0aXRsZTogJ9CX0LDQs9C+0LvQvtCy0L7QuiAyJyxcbiAgICAgIGFjdGlvbjogKCkgPT4gaW5zZXJ0SGVhZGluZygyKVxuICAgIH0sXG4gICAge1xuICAgICAgaWNvbjogKFxuICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNNiA2bDMgMkg1bDMgMlwiIC8+XG4gICAgICAgIDwvc3ZnPlxuICAgICAgKSxcbiAgICAgIHRpdGxlOiAn0JfQsNCz0L7Qu9C+0LLQvtC6IDMnLFxuICAgICAgYWN0aW9uOiAoKSA9PiBpbnNlcnRIZWFkaW5nKDMpXG4gICAgfSxcbiAgICB7IHR5cGU6ICdzZXBhcmF0b3InIH0sXG4gICAge1xuICAgICAgaWNvbjogPHN0cm9uZz5CPC9zdHJvbmc+LFxuICAgICAgdGl0bGU6ICfQltC40YDQvdGL0LknLFxuICAgICAgYWN0aW9uOiAoKSA9PiBleGVjQ29tbWFuZCgnYm9sZCcpXG4gICAgfSxcbiAgICB7XG4gICAgICBpY29uOiA8ZW0+STwvZW0+LFxuICAgICAgdGl0bGU6ICfQmtGD0YDRgdC40LInLFxuICAgICAgYWN0aW9uOiAoKSA9PiBleGVjQ29tbWFuZCgnaXRhbGljJylcbiAgICB9LFxuICAgIHtcbiAgICAgIGljb246IDx1PlU8L3U+LFxuICAgICAgdGl0bGU6ICfQn9C+0LTRh9C10YDQutC90YPRgtGL0LknLFxuICAgICAgYWN0aW9uOiAoKSA9PiBleGVjQ29tbWFuZCgndW5kZXJsaW5lJylcbiAgICB9LFxuICAgIHsgdHlwZTogJ3NlcGFyYXRvcicgfSxcbiAgICB7XG4gICAgICBpY29uOiAoXG4gICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk00IDZoMTZNNCAxMmg4bS04IDZoMTZcIiAvPlxuICAgICAgICA8L3N2Zz5cbiAgICAgICksXG4gICAgICB0aXRsZTogJ9Cf0L4g0LvQtdCy0L7QvNGDINC60YDQsNGOJyxcbiAgICAgIGFjdGlvbjogKCkgPT4gZXhlY0NvbW1hbmQoJ2p1c3RpZnlMZWZ0JylcbiAgICB9LFxuICAgIHtcbiAgICAgIGljb246IChcbiAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTQgNmgxNk00IDEyaDE2TTQgMThoMTZcIiAvPlxuICAgICAgICA8L3N2Zz5cbiAgICAgICksXG4gICAgICB0aXRsZTogJ9Cf0L4g0YbQtdC90YLRgNGDJyxcbiAgICAgIGFjdGlvbjogKCkgPT4gZXhlY0NvbW1hbmQoJ2p1c3RpZnlDZW50ZXInKVxuICAgIH0sXG4gICAge1xuICAgICAgaWNvbjogKFxuICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNNCA2aDE2TTQgMTJoMTZNNCAxOGgxMlwiIC8+XG4gICAgICAgIDwvc3ZnPlxuICAgICAgKSxcbiAgICAgIHRpdGxlOiAn0J/QviDQv9GA0LDQstC+0LzRgyDQutGA0LDRjicsXG4gICAgICBhY3Rpb246ICgpID0+IGV4ZWNDb21tYW5kKCdqdXN0aWZ5UmlnaHQnKVxuICAgIH0sXG4gICAgeyB0eXBlOiAnc2VwYXJhdG9yJyB9LFxuICAgIHtcbiAgICAgIGljb246IChcbiAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTQgNmgxNk00IDEwaDE2TTQgMTRoMTZNNCAxOGgxNlwiIC8+XG4gICAgICAgIDwvc3ZnPlxuICAgICAgKSxcbiAgICAgIHRpdGxlOiAn0KHQv9C40YHQvtC6JyxcbiAgICAgIGFjdGlvbjogKCkgPT4gZXhlY0NvbW1hbmQoJ2luc2VydFVub3JkZXJlZExpc3QnKVxuICAgIH0sXG4gICAge1xuICAgICAgaWNvbjogKFxuICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNNyAyMGw0LTE2bTIgMTZsNC0xNk02IDloMTRNNCAxNWgxNFwiIC8+XG4gICAgICAgIDwvc3ZnPlxuICAgICAgKSxcbiAgICAgIHRpdGxlOiAn0J3Rg9C80LXRgNC+0LLQsNC90L3Ri9C5INGB0L/QuNGB0L7QuicsXG4gICAgICBhY3Rpb246ICgpID0+IGV4ZWNDb21tYW5kKCdpbnNlcnRPcmRlcmVkTGlzdCcpXG4gICAgfSxcbiAgICB7IHR5cGU6ICdzZXBhcmF0b3InIH0sXG4gICAge1xuICAgICAgaWNvbjogKFxuICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNNyA4aDEwTTcgMTJoNG0xIDhsLTQtNEg1YTIgMiAwIDAxLTItMlY2YTIgMiAwIDAxMi0yaDE0YTIgMiAwIDAxMiAydjZhMiAyIDAgMDEtMiAyaC0ybC00IDR6XCIgLz5cbiAgICAgICAgPC9zdmc+XG4gICAgICApLFxuICAgICAgdGl0bGU6ICfQptC40YLQsNGC0LAnLFxuICAgICAgYWN0aW9uOiBpbnNlcnRRdW90ZVxuICAgIH0sXG4gICAge1xuICAgICAgaWNvbjogKFxuICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTAgMjBsNC0xNm00IDRsNCA0LTQgNE02IDE2bC00LTQgNC00XCIgLz5cbiAgICAgICAgPC9zdmc+XG4gICAgICApLFxuICAgICAgdGl0bGU6ICfQmtC+0LQnLFxuICAgICAgYWN0aW9uOiBpbnNlcnRDb2RlXG4gICAgfSxcbiAgICB7IHR5cGU6ICdzZXBhcmF0b3InIH0sXG4gICAge1xuICAgICAgaWNvbjogKFxuICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTMuODI4IDEwLjE3MmE0IDQgMCAwMC01LjY1NiAwbC00IDRhNCA0IDAgMTA1LjY1NiA1LjY1NmwxLjEwMi0xLjEwMW0tLjc1OC00Ljg5OWE0IDQgMCAwMDUuNjU2IDBsNC00YTQgNCAwIDAwLTUuNjU2LTUuNjU2bC0xLjEgMS4xXCIgLz5cbiAgICAgICAgPC9zdmc+XG4gICAgICApLFxuICAgICAgdGl0bGU6ICfQodGB0YvQu9C60LAnLFxuICAgICAgYWN0aW9uOiBpbnNlcnRMaW5rXG4gICAgfSxcbiAgICB7XG4gICAgICBpY29uOiAoXG4gICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk00IDE2bDQuNTg2LTQuNTg2YTIgMiAwIDAxMi44MjggMEwxNiAxNm0tMi0ybDEuNTg2LTEuNTg2YTIgMiAwIDAxMi44MjggMEwyMCAxNG0tNi02aC4wMU02IDIwaDEyYTIgMiAwIDAwMi0yVjZhMiAyIDAgMDAtMi0ySDZhMiAyIDAgMDAtMiAydjEyYTIgMiAwIDAwMiAyelwiIC8+XG4gICAgICAgIDwvc3ZnPlxuICAgICAgKSxcbiAgICAgIHRpdGxlOiAn0JjQt9C+0LHRgNCw0LbQtdC90LjQtScsXG4gICAgICBhY3Rpb246IGluc2VydEltYWdlXG4gICAgfSxcbiAgICB7IHR5cGU6ICdzZXBhcmF0b3InIH0sXG4gICAge1xuICAgICAgaWNvbjogKFxuICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNNyAxNmwtNC00bTAgMGw0LTRtLTQgNGgxOFwiIC8+XG4gICAgICAgIDwvc3ZnPlxuICAgICAgKSxcbiAgICAgIHRpdGxlOiAn0J7RgtC80LXQvdC40YLRjCcsXG4gICAgICBhY3Rpb246ICgpID0+IGV4ZWNDb21tYW5kKCd1bmRvJylcbiAgICB9LFxuICAgIHtcbiAgICAgIGljb246IChcbiAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTE3IDhsNCA0bTAgMGwtNCA0bTQtNEgzXCIgLz5cbiAgICAgICAgPC9zdmc+XG4gICAgICApLFxuICAgICAgdGl0bGU6ICfQn9C+0LLRgtC+0YDQuNGC0YwnLFxuICAgICAgYWN0aW9uOiAoKSA9PiBleGVjQ29tbWFuZCgncmVkbycpXG4gICAgfVxuICBdO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2Bib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuICR7Y2xhc3NOYW1lfWB9PlxuICAgICAgey8qIFRvb2xiYXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT17YGJnLWdyYXktNTAgYm9yZGVyLWIgYm9yZGVyLWdyYXktMzAwIHAtMiAke2lzVG9vbGJhclN0aWNreSA/ICdzdGlja3kgdG9wLTAgei0xMCBzaGFkb3ctbWQnIDogJyd9YH0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAgaXRlbXMtY2VudGVyIGdhcC0xXCI+XG4gICAgICAgICAge3Rvb2xiYXJCdXR0b25zLm1hcCgoYnV0dG9uLCBpbmRleCkgPT4ge1xuICAgICAgICAgICAgaWYgKGJ1dHRvbi50eXBlID09PSAnc2VwYXJhdG9yJykge1xuICAgICAgICAgICAgICByZXR1cm4gPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJ3LXB4IGgtNiBiZy1ncmF5LTMwMCBteC0xXCIgLz47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2J1dHRvbi5hY3Rpb259XG4gICAgICAgICAgICAgICAgdGl0bGU9e2J1dHRvbi50aXRsZX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTIgdGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyYXktOTAwIGhvdmVyOmJnLWdyYXktMjAwIHJvdW5kZWQgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2J1dHRvbi5pY29ufVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfSl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBFZGl0b3IgKi99XG4gICAgICA8ZGl2XG4gICAgICAgIHJlZj17ZWRpdG9yUmVmfVxuICAgICAgICBjb250ZW50RWRpdGFibGVcbiAgICAgICAgb25JbnB1dD17aGFuZGxlSW5wdXR9XG4gICAgICAgIG9uUGFzdGU9eygpID0+IHtcbiAgICAgICAgICAvLyBIYW5kbGUgcGFzdGUgdG8gbWFpbnRhaW4gZm9ybWF0dGluZ1xuICAgICAgICAgIHNldFRpbWVvdXQoaGFuZGxlSW5wdXQsIDApO1xuICAgICAgICB9fVxuICAgICAgICBjbGFzc05hbWU9XCJtaW4taC1bNDAwcHhdIHAtNCBmb2N1czpvdXRsaW5lLW5vbmUgcHJvc2UgcHJvc2UtbGcgbWF4LXctbm9uZVwiXG4gICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgbGluZUhlaWdodDogJzEuNicsXG4gICAgICAgIH19XG4gICAgICAgIHN1cHByZXNzQ29udGVudEVkaXRhYmxlV2FybmluZz17dHJ1ZX1cbiAgICAgICAgZGF0YS1wbGFjZWhvbGRlcj17cGxhY2Vob2xkZXJ9XG4gICAgICAvPlxuXG4gICAgICA8c3R5bGUganN4PntgXG4gICAgICAgIFtjb250ZW50ZWRpdGFibGVdOmVtcHR5OmJlZm9yZSB7XG4gICAgICAgICAgY29udGVudDogYXR0cihkYXRhLXBsYWNlaG9sZGVyKTtcbiAgICAgICAgICBjb2xvcjogIzljYTNhZjtcbiAgICAgICAgICBwb2ludGVyLWV2ZW50czogbm9uZTtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgW2NvbnRlbnRlZGl0YWJsZV0gaDEge1xuICAgICAgICAgIGZvbnQtc2l6ZTogMnJlbTtcbiAgICAgICAgICBmb250LXdlaWdodDogYm9sZDtcbiAgICAgICAgICBtYXJnaW46IDFyZW0gMDtcbiAgICAgICAgICBsaW5lLWhlaWdodDogMS4yO1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICBbY29udGVudGVkaXRhYmxlXSBoMiB7XG4gICAgICAgICAgZm9udC1zaXplOiAxLjVyZW07XG4gICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7XG4gICAgICAgICAgbWFyZ2luOiAxcmVtIDA7XG4gICAgICAgICAgbGluZS1oZWlnaHQ6IDEuMztcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgW2NvbnRlbnRlZGl0YWJsZV0gaDMge1xuICAgICAgICAgIGZvbnQtc2l6ZTogMS4yNXJlbTtcbiAgICAgICAgICBmb250LXdlaWdodDogYm9sZDtcbiAgICAgICAgICBtYXJnaW46IDFyZW0gMDtcbiAgICAgICAgICBsaW5lLWhlaWdodDogMS40O1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICBbY29udGVudGVkaXRhYmxlXSBwIHtcbiAgICAgICAgICBtYXJnaW46IDAuNXJlbSAwO1xuICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjY7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIFtjb250ZW50ZWRpdGFibGVdIHVsLCBbY29udGVudGVkaXRhYmxlXSBvbCB7XG4gICAgICAgICAgbWFyZ2luOiAxcmVtIDA7XG4gICAgICAgICAgcGFkZGluZy1sZWZ0OiAycmVtO1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICBbY29udGVudGVkaXRhYmxlXSBsaSB7XG4gICAgICAgICAgbWFyZ2luOiAwLjI1cmVtIDA7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIFtjb250ZW50ZWRpdGFibGVdIGltZyB7XG4gICAgICAgICAgbWF4LXdpZHRoOiAxMDAlO1xuICAgICAgICAgIGhlaWdodDogYXV0bztcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiAwLjVyZW07XG4gICAgICAgICAgbWFyZ2luOiAxcmVtIDA7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIFtjb250ZW50ZWRpdGFibGVdIGEge1xuICAgICAgICAgIGNvbG9yOiAjM2I4MmY2O1xuICAgICAgICAgIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lO1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICBbY29udGVudGVkaXRhYmxlXSBhOmhvdmVyIHtcbiAgICAgICAgICBjb2xvcjogIzFkNGVkODtcbiAgICAgICAgfVxuXG4gICAgICAgIFtjb250ZW50ZWRpdGFibGVdIGJsb2NrcXVvdGUge1xuICAgICAgICAgIGJvcmRlci1sZWZ0OiA0cHggc29saWQgI2U1ZTdlYjtcbiAgICAgICAgICBwYWRkaW5nLWxlZnQ6IDFyZW07XG4gICAgICAgICAgbWFyZ2luOiAxcmVtIDA7XG4gICAgICAgICAgZm9udC1zdHlsZTogaXRhbGljO1xuICAgICAgICAgIGNvbG9yOiAjNmI3MjgwO1xuICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmOWZhZmI7XG4gICAgICAgICAgcGFkZGluZzogMXJlbTtcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiAwLjVyZW07XG4gICAgICAgIH1cblxuICAgICAgICBbY29udGVudGVkaXRhYmxlXSBjb2RlIHtcbiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjNmNGY2O1xuICAgICAgICAgIHBhZGRpbmc6IDAuMjVyZW0gMC41cmVtO1xuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDAuMjVyZW07XG4gICAgICAgICAgZm9udC1mYW1pbHk6ICdNb25hY28nLCAnTWVubG8nLCAnVWJ1bnR1IE1vbm8nLCBtb25vc3BhY2U7XG4gICAgICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcbiAgICAgICAgICBjb2xvcjogIzFmMjkzNztcbiAgICAgICAgfVxuXG4gICAgICAgIFtjb250ZW50ZWRpdGFibGVdIHByZSB7XG4gICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzFmMjkzNztcbiAgICAgICAgICBjb2xvcjogI2Y5ZmFmYjtcbiAgICAgICAgICBwYWRkaW5nOiAxcmVtO1xuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDAuNXJlbTtcbiAgICAgICAgICBvdmVyZmxvdy14OiBhdXRvO1xuICAgICAgICAgIG1hcmdpbjogMXJlbSAwO1xuICAgICAgICB9XG5cbiAgICAgICAgW2NvbnRlbnRlZGl0YWJsZV0gcHJlIGNvZGUge1xuICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6IHRyYW5zcGFyZW50O1xuICAgICAgICAgIHBhZGRpbmc6IDA7XG4gICAgICAgICAgY29sb3I6IGluaGVyaXQ7XG4gICAgICAgIH1cbiAgICAgIGB9PC9zdHlsZT5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFJpY2hUZXh0RWRpdG9yO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VSZWYiLCJ1c2VFZmZlY3QiLCJSaWNoVGV4dEVkaXRvciIsInZhbHVlIiwib25DaGFuZ2UiLCJvbkltYWdlSW5zZXJ0IiwicGxhY2Vob2xkZXIiLCJjbGFzc05hbWUiLCJpbnNlcnRJbWFnZVVybCIsImVkaXRvclJlZiIsImlzVG9vbGJhclN0aWNreSIsInNldElzVG9vbGJhclN0aWNreSIsImN1cnJlbnQiLCJpbm5lckhUTUwiLCJtYXJrZG93blRvSHRtbCIsImluc2VydEltYWdlSW50b0VkaXRvciIsImltYWdlVXJsIiwiZm9jdXMiLCJpbWciLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJzcmMiLCJhbHQiLCJzdHlsZSIsIm1heFdpZHRoIiwiaGVpZ2h0IiwibWFyZ2luIiwiYm9yZGVyUmFkaXVzIiwic2VsZWN0aW9uIiwid2luZG93IiwiZ2V0U2VsZWN0aW9uIiwicmFuZ2VDb3VudCIsInJhbmdlIiwiZ2V0UmFuZ2VBdCIsImRlbGV0ZUNvbnRlbnRzIiwiaW5zZXJ0Tm9kZSIsImJyIiwic2V0U3RhcnRBZnRlciIsInNldEVuZEFmdGVyIiwicmVtb3ZlQWxsUmFuZ2VzIiwiYWRkUmFuZ2UiLCJhcHBlbmRDaGlsZCIsImhhbmRsZUlucHV0IiwiaGFuZGxlU2Nyb2xsIiwicmVjdCIsImdldEJvdW5kaW5nQ2xpZW50UmVjdCIsInRvcCIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwibWFya2Rvd24iLCJyZXBsYWNlIiwiaHRtbFRvTWFya2Rvd24iLCJodG1sIiwidHJpbSIsImV4ZWNDb21tYW5kIiwiY29tbWFuZCIsImluc2VydEhlYWRpbmciLCJsZXZlbCIsInNlbGVjdGVkVGV4dCIsInRvU3RyaW5nIiwiaGVhZGluZyIsInRleHRDb250ZW50IiwiaW5zZXJ0SW1hZ2UiLCJpbnNlcnRRdW90ZSIsImJsb2NrcXVvdGUiLCJib3JkZXJMZWZ0IiwicGFkZGluZ0xlZnQiLCJmb250U3R5bGUiLCJjb2xvciIsImluc2VydENvZGUiLCJjb2RlIiwiYmFja2dyb3VuZENvbG9yIiwicGFkZGluZyIsImZvbnRGYW1pbHkiLCJmb250U2l6ZSIsImluc2VydExpbmsiLCJ1cmwiLCJwcm9tcHQiLCJ0b29sYmFyQnV0dG9ucyIsImljb24iLCJzdmciLCJmaWxsIiwic3Ryb2tlIiwidmlld0JveCIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiLCJ0aXRsZSIsImFjdGlvbiIsInR5cGUiLCJzdHJvbmciLCJlbSIsInUiLCJkaXYiLCJtYXAiLCJidXR0b24iLCJpbmRleCIsIm9uQ2xpY2siLCJyZWYiLCJjb250ZW50RWRpdGFibGUiLCJvbklucHV0Iiwib25QYXN0ZSIsInNldFRpbWVvdXQiLCJsaW5lSGVpZ2h0Iiwic3VwcHJlc3NDb250ZW50RWRpdGFibGVXYXJuaW5nIiwiZGF0YS1wbGFjZWhvbGRlciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/RichTextEditor.tsx\n"));

/***/ })

});