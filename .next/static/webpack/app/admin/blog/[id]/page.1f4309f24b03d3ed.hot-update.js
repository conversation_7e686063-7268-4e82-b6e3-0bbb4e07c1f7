"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/blog/[id]/page",{

/***/ "(app-pages-browser)/./src/components/admin/MediaGallery.tsx":
/*!***********************************************!*\
  !*** ./src/components/admin/MediaGallery.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_cms_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/cms-utils */ \"(app-pages-browser)/./src/lib/cms-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst MediaGallery = (param)=>{\n    let { isOpen, onClose, onSelect, selectedImage, multiple = false, onSelectMultiple, mode = 'content', title } = param;\n    _s();\n    const [mediaFiles, setMediaFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedFiles, setSelectedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOver, setDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaGallery.useEffect\": ()=>{\n            if (isOpen) {\n                loadMediaFiles();\n            }\n        }\n    }[\"MediaGallery.useEffect\"], [\n        isOpen\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaGallery.useEffect\": ()=>{\n            // Инициализируем выбранные файлы только для режима превью\n            if (mode === 'featured' && selectedImage && !multiple) {\n                setSelectedFiles([\n                    selectedImage\n                ]);\n            } else if (mode === 'content') {\n                // Для контента всегда начинаем с пустого выбора\n                setSelectedFiles([]);\n            }\n        }\n    }[\"MediaGallery.useEffect\"], [\n        selectedImage,\n        multiple,\n        mode,\n        isOpen\n    ]);\n    const loadMediaFiles = ()=>{\n        const files = (0,_lib_cms_utils__WEBPACK_IMPORTED_MODULE_2__.getMediaFiles)();\n        setMediaFiles(files);\n    };\n    const handleFileUpload = async (files)=>{\n        setUploading(true);\n        for(let i = 0; i < files.length; i++){\n            const file = files[i];\n            // Check if file is an image\n            if (!file.type.startsWith('image/')) {\n                continue;\n            }\n            // Create a data URL for the image\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                var _e_target;\n                const dataUrl = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n                const mediaFile = {\n                    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n                    name: file.name.replace(/\\.[^/.]+$/, \"\"),\n                    originalName: file.name,\n                    size: file.size,\n                    type: file.type,\n                    url: dataUrl,\n                    uploadedAt: new Date().toISOString()\n                };\n                (0,_lib_cms_utils__WEBPACK_IMPORTED_MODULE_2__.saveMediaFile)(mediaFile);\n                loadMediaFiles();\n            };\n            reader.readAsDataURL(file);\n        }\n        setUploading(false);\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        setDragOver(false);\n        const files = e.dataTransfer.files;\n        if (files.length > 0) {\n            handleFileUpload(files);\n        }\n    };\n    const handleFileSelect = (e)=>{\n        const files = e.target.files;\n        if (files && files.length > 0) {\n            handleFileUpload(files);\n        }\n    };\n    const handleImageSelect = (imageUrl)=>{\n        if (multiple) {\n            const newSelection = selectedFiles.includes(imageUrl) ? selectedFiles.filter((url)=>url !== imageUrl) : [\n                ...selectedFiles,\n                imageUrl\n            ];\n            setSelectedFiles(newSelection);\n        } else {\n            setSelectedFiles([\n                imageUrl\n            ]);\n        }\n    };\n    const handleConfirmSelection = ()=>{\n        if (multiple && onSelectMultiple) {\n            onSelectMultiple(selectedFiles);\n        } else if (selectedFiles.length > 0) {\n            onSelect(selectedFiles[0]);\n        }\n        onClose();\n    };\n    const handleDeleteFile = (fileId)=>{\n        if (confirm('Удалить это изображение?')) {\n            (0,_lib_cms_utils__WEBPACK_IMPORTED_MODULE_2__.deleteMediaFile)(fileId);\n            loadMediaFiles();\n            // Remove from selection if selected\n            setSelectedFiles((prev)=>prev.filter((url)=>{\n                    const file = mediaFiles.find((f)=>f.id === fileId);\n                    return file ? url !== file.url : true;\n                }));\n        }\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: title || (mode === 'featured' ? 'Выбрать изображение превью' : 'Выбрать изображения для контента')\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onClose,\n                                            className: \"text-gray-400 hover:text-gray-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M6 18L18 6M6 6l12 12\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-2 border-dashed rounded-lg p-6 text-center mb-6 transition-colors \".concat(dragOver ? 'border-primary-500 bg-primary-50' : 'border-gray-300'),\n                                    onDrop: handleDrop,\n                                    onDragOver: (e)=>{\n                                        e.preventDefault();\n                                        setDragOver(true);\n                                    },\n                                    onDragLeave: ()=>setDragOver(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"mx-auto h-12 w-12 text-gray-400\",\n                                            stroke: \"currentColor\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 48 48\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\",\n                                                strokeWidth: 2,\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"file-upload\",\n                                                    className: \"cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"mt-2 block text-sm font-medium text-gray-900\",\n                                                            children: [\n                                                                \"Перетащите изображения сюда или\",\n                                                                ' ',\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-primary-600 hover:text-primary-500\",\n                                                                    children: \"выберите файлы\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                                    lineNumber: 191,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"file-upload\",\n                                                            name: \"file-upload\",\n                                                            type: \"file\",\n                                                            className: \"sr-only\",\n                                                            multiple: true,\n                                                            accept: \"image/*\",\n                                                            onChange: handleFileSelect\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-xs text-gray-500\",\n                                                    children: \"PNG, JPG, GIF до 10MB\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, undefined),\n                                uploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Загрузка...\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 max-h-96 overflow-y-auto\",\n                                    children: mediaFiles.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all \".concat(selectedFiles.includes(file.url) ? 'border-primary-500 ring-2 ring-primary-200' : 'border-gray-200 hover:border-gray-300'),\n                                            onClick: ()=>handleImageSelect(file.url),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-square\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: file.url,\n                                                        alt: file.name,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                selectedFiles.includes(file.url) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-2 right-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-5 h-5 bg-primary-600 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-3 h-3 text-white\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        handleDeleteFile(file.id);\n                                                    },\n                                                    className: \"absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity bg-red-600 text-white rounded-full p-1 hover:bg-red-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white p-2 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs truncate\",\n                                                            children: file.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-300\",\n                                                            children: formatFileSize(file.size)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, file.id, true, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, undefined),\n                                mediaFiles.length === 0 && !uploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"mx-auto h-12 w-12 text-gray-400\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-sm text-gray-500\",\n                                            children: \"Нет загруженных изображений\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleConfirmSelection,\n                                    disabled: selectedFiles.length === 0,\n                                    className: \"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: multiple ? \"Выбрать (\".concat(selectedFiles.length, \")\") : 'Выбрать'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm\",\n                                    children: \"Отмена\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n            lineNumber: 150,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n        lineNumber: 149,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MediaGallery, \"WU8LBYv1uTx3EGcVj6Eta3JMTIc=\");\n_c = MediaGallery;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MediaGallery);\nvar _c;\n$RefreshReg$(_c, \"MediaGallery\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/MediaGallery.tsx\n"));

/***/ })

});