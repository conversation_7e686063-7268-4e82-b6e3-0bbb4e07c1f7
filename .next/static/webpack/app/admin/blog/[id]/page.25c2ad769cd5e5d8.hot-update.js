"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/blog/[id]/page",{

/***/ "(app-pages-browser)/./src/app/admin/blog/[id]/page.tsx":
/*!******************************************!*\
  !*** ./src/app/admin/blog/[id]/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/admin/AdminLayout */ \"(app-pages-browser)/./src/components/admin/AdminLayout.tsx\");\n/* harmony import */ var _components_admin_MediaGallery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/admin/MediaGallery */ \"(app-pages-browser)/./src/components/admin/MediaGallery.tsx\");\n/* harmony import */ var _lib_cms_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/cms-utils */ \"(app-pages-browser)/./src/lib/cms-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst EditBlogPostPage = ()=>{\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [post, setPost] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showMediaGallery, setShowMediaGallery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mediaGalleryMode, setMediaGalleryMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('featured');\n    const [pendingImages, setPendingImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        slug: '',\n        excerpt: '',\n        content: '',\n        status: 'draft',\n        tags: '',\n        featuredImage: '',\n        contentImages: [],\n        metaTitle: '',\n        metaDescription: ''\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditBlogPostPage.useEffect\": ()=>{\n            if (params.id) {\n                const postData = (0,_lib_cms_utils__WEBPACK_IMPORTED_MODULE_5__.getBlogPost)(params.id);\n                if (postData) {\n                    setPost(postData);\n                    setFormData({\n                        title: postData.title,\n                        slug: postData.slug,\n                        excerpt: postData.excerpt,\n                        content: postData.content,\n                        status: postData.status === 'archived' ? 'draft' : postData.status,\n                        tags: postData.tags.join(', '),\n                        featuredImage: postData.featuredImage || '',\n                        contentImages: postData.contentImages || [],\n                        metaTitle: postData.seo.metaTitle || '',\n                        metaDescription: postData.seo.metaDescription || ''\n                    });\n                } else {\n                    router.push('/admin/blog');\n                }\n            }\n        }\n    }[\"EditBlogPostPage.useEffect\"], [\n        params.id,\n        router\n    ]);\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const handleFeaturedImageSelect = (imageUrl)=>{\n        setFormData((prev)=>({\n                ...prev,\n                featuredImage: imageUrl\n            }));\n        setShowMediaGallery(false);\n    };\n    const handleContentImagesSelect = (imageUrls)=>{\n        if (mediaGalleryMode === 'content') {\n            // Сохраняем изображения для вставки\n            setPendingImages(imageUrls);\n            // Пытаемся вставить через глобальную функцию\n            imageUrls.forEach((imageUrl)=>{\n                if (window.insertImageIntoEditor) {\n                    window.insertImageIntoEditor(imageUrl);\n                } else {\n                    // Fallback: добавляем в конец контента\n                    const imageMarkdown = \"![Изображение](\".concat(imageUrl, \")\");\n                    setFormData((prev)=>({\n                            ...prev,\n                            content: prev.content + (prev.content ? '\\n\\n' : '') + imageMarkdown\n                        }));\n                }\n            });\n            // Очищаем pending изображения\n            setTimeout(()=>setPendingImages([]), 100);\n        } else {\n            // Legacy behavior for contentImages array\n            setFormData((prev)=>({\n                    ...prev,\n                    contentImages: imageUrls\n                }));\n        }\n        setShowMediaGallery(false);\n    };\n    const openMediaGallery = (mode)=>{\n        setMediaGalleryMode(mode);\n        setShowMediaGallery(true);\n    };\n    const removeContentImage = (imageUrl)=>{\n        setFormData((prev)=>({\n                ...prev,\n                contentImages: prev.contentImages.filter((url)=>url !== imageUrl)\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!post) return;\n        setIsLoading(true);\n        try {\n            const updatedData = {\n                title: formData.title,\n                slug: formData.slug,\n                excerpt: formData.excerpt,\n                content: formData.content,\n                status: formData.status,\n                featuredImage: formData.featuredImage || undefined,\n                contentImages: formData.contentImages.length > 0 ? formData.contentImages : undefined,\n                tags: formData.tags.split(',').map((tag)=>tag.trim()).filter(Boolean),\n                seo: {\n                    metaTitle: formData.metaTitle || formData.title,\n                    metaDescription: formData.metaDescription || formData.excerpt,\n                    keywords: formData.tags.split(',').map((tag)=>tag.trim()).filter(Boolean)\n                }\n            };\n            (0,_lib_cms_utils__WEBPACK_IMPORTED_MODULE_5__.updateBlogPost)(post.id, updatedData);\n            // Показываем уведомление об успешном сохранении\n            alert('Статья успешно сохранена!');\n            // Обновляем данные поста в состоянии\n            const updatedPost = {\n                ...post,\n                ...updatedData\n            };\n            setPost(updatedPost);\n        } catch (error) {\n            console.error('Ошибка при обновлении статьи:', error);\n            alert('Ошибка при обновлении статьи');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSaveDraft = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                status: 'draft'\n            }));\n        setTimeout(()=>{\n            var _document_getElementById;\n            (_document_getElementById = document.getElementById('submit-form')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n        }, 0);\n    };\n    const handlePublish = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                status: 'published'\n            }));\n        setTimeout(()=>{\n            var _document_getElementById;\n            (_document_getElementById = document.getElementById('submit-form')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n        }, 0);\n    };\n    const handleSave = ()=>{\n        // Сохранить с текущим статусом\n        setTimeout(()=>{\n            var _document_getElementById;\n            (_document_getElementById = document.getElementById('submit-form')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n        }, 0);\n    };\n    if (!post) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 min-h-screen bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Загрузка...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                lineNumber: 177,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n            lineNumber: 176,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 min-h-screen bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold text-gray-900\",\n                                            children: \"Редактирование статьи\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-gray-600\",\n                                            children: [\n                                                \"Редактирование: \",\n                                                post.title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>router.push('/admin/blog'),\n                                            className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                            children: \"Отмена\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        formData.status === 'published' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: handleSaveDraft,\n                                                    disabled: isLoading,\n                                                    className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\",\n                                                    children: \"Вернуть в черновик\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: handleSave,\n                                                    disabled: isLoading,\n                                                    className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\",\n                                                    children: \"Сохранить изменения\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: handleSaveDraft,\n                                                    disabled: isLoading,\n                                                    className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\",\n                                                    children: \"Сохранить черновик\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: handlePublish,\n                                                    disabled: isLoading,\n                                                    className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\",\n                                                    children: \"Опубликовать\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:col-span-2 space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"title\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Заголовок статьи *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"title\",\n                                                            name: \"title\",\n                                                            required: true,\n                                                            value: formData.title,\n                                                            onChange: handleInputChange,\n                                                            className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                            placeholder: \"Введите заголовок статьи\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"slug\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"URL (slug) *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"slug\",\n                                                            name: \"slug\",\n                                                            required: true,\n                                                            value: formData.slug,\n                                                            onChange: handleInputChange,\n                                                            className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                            placeholder: \"url-статьи\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2 flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        \"URL статьи: /blog/\",\n                                                                        formData.slug || 'url-статьи'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                formData.status === 'published' && formData.slug && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"/blog/\".concat(formData.slug),\n                                                                    target: \"_blank\",\n                                                                    rel: \"noopener noreferrer\",\n                                                                    className: \"inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4 mr-1\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                                lineNumber: 300,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                            lineNumber: 299,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        \"Посмотреть статью\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                    lineNumber: 293,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"excerpt\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Краткое описание *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            id: \"excerpt\",\n                                                            name: \"excerpt\",\n                                                            required: true,\n                                                            rows: 3,\n                                                            value: formData.excerpt,\n                                                            onChange: handleInputChange,\n                                                            className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                            placeholder: \"Краткое описание статьи для превью\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Изображение превью\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                formData.featuredImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative inline-block\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: formData.featuredImage,\n                                                                            alt: \"Featured\",\n                                                                            className: \"w-32 h-32 object-cover rounded-lg border border-gray-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                            lineNumber: 337,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        featuredImage: ''\n                                                                                    })),\n                                                                            className: \"absolute -top-2 -right-2 bg-red-600 text-white rounded-full p-1 hover:bg-red-700\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-4 h-4\",\n                                                                                fill: \"currentColor\",\n                                                                                viewBox: \"0 0 20 20\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    fillRule: \"evenodd\",\n                                                                                    d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                                                                    clipRule: \"evenodd\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                                    lineNumber: 348,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                                lineNumber: 347,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                            lineNumber: 342,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                    lineNumber: 336,\n                                                                    columnNumber: 23\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"mx-auto h-12 w-12 text-gray-400\",\n                                                                            stroke: \"currentColor\",\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 48 48\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\",\n                                                                                strokeWidth: 2,\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                                lineNumber: 355,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                            lineNumber: 354,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"mt-2 text-sm text-gray-600\",\n                                                                            children: \"Изображение не выбрано\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                            lineNumber: 357,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>openMediaGallery('featured'),\n                                                                    className: \"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4 mr-2\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                                lineNumber: 366,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                            lineNumber: 365,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        \"Выбрать изображение\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                    lineNumber: 360,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-4\",\n                                                            children: \"Содержание статьи *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RichTextEditor, {\n                                                            value: formData.content,\n                                                            onChange: (content)=>setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        content\n                                                                    })),\n                                                            onImageInsert: ()=>openMediaGallery('content'),\n                                                            placeholder: \"Редактируйте содержание статьи...\",\n                                                            className: \"min-h-[500px]\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"Публикация\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"status\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Статус\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                id: \"status\",\n                                                                name: \"status\",\n                                                                value: formData.status,\n                                                                onChange: handleInputChange,\n                                                                className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"draft\",\n                                                                        children: \"Черновик\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                        lineNumber: 407,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"published\",\n                                                                        children: \"Опубликовано\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                        lineNumber: 408,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-4 text-sm text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"Создано: \",\n                                                                    new Date(post.createdAt).toLocaleDateString('ru-RU')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"Обновлено: \",\n                                                                    new Date(post.updatedAt).toLocaleDateString('ru-RU')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"Теги\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"tags\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Теги (через запятую)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"tags\",\n                                                                name: \"tags\",\n                                                                value: formData.tags,\n                                                                onChange: handleInputChange,\n                                                                className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                placeholder: \"тег1, тег2, тег3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"SEO\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"metaTitle\",\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Meta Title\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                        lineNumber: 441,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        id: \"metaTitle\",\n                                                                        name: \"metaTitle\",\n                                                                        value: formData.metaTitle,\n                                                                        onChange: handleInputChange,\n                                                                        className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                        placeholder: \"Заголовок для поисковых систем\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                        lineNumber: 444,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"metaDescription\",\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Meta Description\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                        lineNumber: 455,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                        id: \"metaDescription\",\n                                                                        name: \"metaDescription\",\n                                                                        rows: 3,\n                                                                        value: formData.metaDescription,\n                                                                        onChange: handleInputChange,\n                                                                        className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                        placeholder: \"Описание для поисковых систем\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                        lineNumber: 458,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                id: \"submit-form\",\n                                className: \"hidden\",\n                                disabled: isLoading,\n                                children: \"Submit\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_MediaGallery__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showMediaGallery,\n                onClose: ()=>setShowMediaGallery(false),\n                onSelect: mediaGalleryMode === 'featured' ? handleFeaturedImageSelect : (imageUrl)=>handleContentImagesSelect([\n                        imageUrl\n                    ]),\n                onSelectMultiple: mediaGalleryMode === 'content' ? handleContentImagesSelect : undefined,\n                multiple: mediaGalleryMode === 'content',\n                selectedImage: mediaGalleryMode === 'featured' ? formData.featuredImage : undefined,\n                mode: mediaGalleryMode\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                lineNumber: 486,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n        lineNumber: 187,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EditBlogPostPage, \"tsnTRy/JlD9UN3Vz/quzK6l8JOE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c = EditBlogPostPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EditBlogPostPage);\nvar _c;\n$RefreshReg$(_c, \"EditBlogPostPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/blog/[id]/page.tsx\n"));

/***/ })

});