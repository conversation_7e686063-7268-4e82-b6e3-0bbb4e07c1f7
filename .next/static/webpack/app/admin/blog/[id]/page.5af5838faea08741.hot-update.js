"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/blog/[id]/page",{

/***/ "(app-pages-browser)/./src/components/admin/MediaGallery.tsx":
/*!***********************************************!*\
  !*** ./src/components/admin/MediaGallery.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_media_storage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/media-storage */ \"(app-pages-browser)/./src/lib/media-storage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst MediaGallery = (param)=>{\n    let { isOpen, onClose, onSelect, selectedImage, multiple = false, onSelectMultiple, mode = 'content', title } = param;\n    _s();\n    const [mediaFiles, setMediaFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedFiles, setSelectedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOver, setDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaGallery.useEffect\": ()=>{\n            if (isOpen) {\n                loadMediaFiles();\n            }\n        }\n    }[\"MediaGallery.useEffect\"], [\n        isOpen\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaGallery.useEffect\": ()=>{\n            // Инициализируем выбранные файлы только для режима превью\n            if (mode === 'featured' && selectedImage && !multiple) {\n                setSelectedFiles([\n                    selectedImage\n                ]);\n            } else if (mode === 'content') {\n                // Для контента всегда начинаем с пустого выбора\n                setSelectedFiles([]);\n            }\n        }\n    }[\"MediaGallery.useEffect\"], [\n        selectedImage,\n        multiple,\n        mode,\n        isOpen\n    ]);\n    const loadMediaFiles = ()=>{\n        const files = (0,_lib_media_storage__WEBPACK_IMPORTED_MODULE_2__.getMediaFiles)();\n        setMediaFiles(files);\n    };\n    const handleFileUpload = async (files)=>{\n        setUploading(true);\n        for(let i = 0; i < files.length; i++){\n            const file = files[i];\n            // Check if file is an image\n            if (!file.type.startsWith('image/')) {\n                continue;\n            }\n            // Create a data URL for the image\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                var _e_target;\n                const dataUrl = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n                const mediaFile = {\n                    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n                    name: file.name.replace(/\\.[^/.]+$/, \"\"),\n                    originalName: file.name,\n                    size: file.size,\n                    type: file.type,\n                    url: dataUrl,\n                    uploadedAt: new Date().toISOString()\n                };\n                saveMediaFile(mediaFile);\n                loadMediaFiles();\n            };\n            reader.readAsDataURL(file);\n        }\n        setUploading(false);\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        setDragOver(false);\n        const files = e.dataTransfer.files;\n        if (files.length > 0) {\n            handleFileUpload(files);\n        }\n    };\n    const handleFileSelect = (e)=>{\n        const files = e.target.files;\n        if (files && files.length > 0) {\n            handleFileUpload(files);\n        }\n    };\n    const handleImageSelect = (imageUrl)=>{\n        if (multiple) {\n            const newSelection = selectedFiles.includes(imageUrl) ? selectedFiles.filter((url)=>url !== imageUrl) : [\n                ...selectedFiles,\n                imageUrl\n            ];\n            setSelectedFiles(newSelection);\n        } else {\n            setSelectedFiles([\n                imageUrl\n            ]);\n        }\n    };\n    const handleConfirmSelection = ()=>{\n        if (multiple && onSelectMultiple) {\n            onSelectMultiple(selectedFiles);\n        } else if (selectedFiles.length > 0) {\n            onSelect(selectedFiles[0]);\n        }\n        onClose();\n    };\n    const handleDeleteFile = (fileId)=>{\n        if (confirm('Удалить это изображение?')) {\n            (0,_lib_media_storage__WEBPACK_IMPORTED_MODULE_2__.deleteMediaFile)(fileId);\n            loadMediaFiles();\n            // Remove from selection if selected\n            setSelectedFiles((prev)=>prev.filter((url)=>{\n                    const file = mediaFiles.find((f)=>f.id === fileId);\n                    return file ? url !== file.url : true;\n                }));\n        }\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: title || (mode === 'featured' ? 'Выбрать изображение превью' : 'Выбрать изображения для контента')\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onClose,\n                                            className: \"text-gray-400 hover:text-gray-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M6 18L18 6M6 6l12 12\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-2 border-dashed rounded-lg p-6 text-center mb-6 transition-colors \".concat(dragOver ? 'border-primary-500 bg-primary-50' : 'border-gray-300'),\n                                    onDrop: handleDrop,\n                                    onDragOver: (e)=>{\n                                        e.preventDefault();\n                                        setDragOver(true);\n                                    },\n                                    onDragLeave: ()=>setDragOver(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"mx-auto h-12 w-12 text-gray-400\",\n                                            stroke: \"currentColor\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 48 48\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\",\n                                                strokeWidth: 2,\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"file-upload\",\n                                                    className: \"cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"mt-2 block text-sm font-medium text-gray-900\",\n                                                            children: [\n                                                                \"Перетащите изображения сюда или\",\n                                                                ' ',\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-primary-600 hover:text-primary-500\",\n                                                                    children: \"выберите файлы\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                                    lineNumber: 191,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"file-upload\",\n                                                            name: \"file-upload\",\n                                                            type: \"file\",\n                                                            className: \"sr-only\",\n                                                            multiple: true,\n                                                            accept: \"image/*\",\n                                                            onChange: handleFileSelect\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-xs text-gray-500\",\n                                                    children: \"PNG, JPG, GIF до 10MB\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, undefined),\n                                uploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Загрузка...\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 max-h-96 overflow-y-auto\",\n                                    children: mediaFiles.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all \".concat(selectedFiles.includes(file.url) ? 'border-primary-500 ring-2 ring-primary-200' : 'border-gray-200 hover:border-gray-300'),\n                                            onClick: ()=>handleImageSelect(file.url),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-square\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: file.url,\n                                                        alt: file.name,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                selectedFiles.includes(file.url) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-2 right-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-5 h-5 bg-primary-600 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-3 h-3 text-white\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        handleDeleteFile(file.id);\n                                                    },\n                                                    className: \"absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity bg-red-600 text-white rounded-full p-1 hover:bg-red-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white p-2 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs truncate\",\n                                                            children: file.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-300\",\n                                                            children: formatFileSize(file.size)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, file.id, true, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, undefined),\n                                mediaFiles.length === 0 && !uploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"mx-auto h-12 w-12 text-gray-400\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-sm text-gray-500\",\n                                            children: \"Нет загруженных изображений\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleConfirmSelection,\n                                    disabled: selectedFiles.length === 0,\n                                    className: \"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: mode === 'featured' ? 'Установить как превью' : multiple ? \"Вставить в статью (\".concat(selectedFiles.length, \")\") : 'Вставить в статью'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm\",\n                                    children: \"Отмена\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n            lineNumber: 150,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n        lineNumber: 149,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MediaGallery, \"WU8LBYv1uTx3EGcVj6Eta3JMTIc=\");\n_c = MediaGallery;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MediaGallery);\nvar _c;\n$RefreshReg$(_c, \"MediaGallery\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/MediaGallery.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/media-storage.ts":
/*!**********************************!*\
  !*** ./src/lib/media-storage.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearAllMedia: () => (/* binding */ clearAllMedia),\n/* harmony export */   deleteMediaFile: () => (/* binding */ deleteMediaFile),\n/* harmony export */   getMediaFiles: () => (/* binding */ getMediaFiles),\n/* harmony export */   getStorageInfo: () => (/* binding */ getStorageInfo),\n/* harmony export */   uploadMediaFile: () => (/* binding */ uploadMediaFile)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ uploadMediaFile,getMediaFiles,deleteMediaFile,clearAllMedia,getStorageInfo auto */ // Функция для сжатия изображения\nconst compressImage = function(file) {\n    let maxWidth = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1200, quality = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0.8;\n    return new Promise((resolve, reject)=>{\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        const img = new Image();\n        img.onload = ()=>{\n            // Вычисляем новые размеры с сохранением пропорций\n            let { width, height } = img;\n            if (width > maxWidth) {\n                height = height * maxWidth / width;\n                width = maxWidth;\n            }\n            canvas.width = width;\n            canvas.height = height;\n            // Рисуем сжатое изображение\n            ctx === null || ctx === void 0 ? void 0 : ctx.drawImage(img, 0, 0, width, height);\n            // Конвертируем в base64 с сжатием\n            const compressedDataUrl = canvas.toDataURL('image/jpeg', quality);\n            resolve(compressedDataUrl);\n        };\n        img.onerror = reject;\n        img.src = URL.createObjectURL(file);\n    });\n};\n// Функция для создания миниатюры\nconst createThumbnail = (file)=>{\n    return compressImage(file, 300, 0.7);\n};\n// IndexedDB для хранения больших файлов\nclass MediaDB {\n    async init() {\n        return new Promise((resolve, reject)=>{\n            const request = indexedDB.open(this.dbName, this.version);\n            request.onerror = ()=>reject(request.error);\n            request.onsuccess = ()=>{\n                this.db = request.result;\n                resolve();\n            };\n            request.onupgradeneeded = (event)=>{\n                const db = event.target.result;\n                // Создаем хранилище для медиа-файлов\n                if (!db.objectStoreNames.contains('media')) {\n                    const store = db.createObjectStore('media', {\n                        keyPath: 'id'\n                    });\n                    store.createIndex('uploadedAt', 'uploadedAt', {\n                        unique: false\n                    });\n                }\n            };\n        });\n    }\n    async saveFile(mediaFile) {\n        if (!this.db) await this.init();\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                'media'\n            ], 'readwrite');\n            const store = transaction.objectStore('media');\n            const request = store.put(mediaFile);\n            request.onsuccess = ()=>resolve(true);\n            request.onerror = ()=>reject(request.error);\n        });\n    }\n    async getFiles() {\n        if (!this.db) await this.init();\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                'media'\n            ], 'readonly');\n            const store = transaction.objectStore('media');\n            const request = store.getAll();\n            request.onsuccess = ()=>resolve(request.result || []);\n            request.onerror = ()=>reject(request.error);\n        });\n    }\n    async deleteFile(id) {\n        if (!this.db) await this.init();\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                'media'\n            ], 'readwrite');\n            const store = transaction.objectStore('media');\n            const request = store.delete(id);\n            request.onsuccess = ()=>resolve(true);\n            request.onerror = ()=>reject(request.error);\n        });\n    }\n    async clearAll() {\n        if (!this.db) await this.init();\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                'media'\n            ], 'readwrite');\n            const store = transaction.objectStore('media');\n            const request = store.clear();\n            request.onsuccess = ()=>resolve(true);\n            request.onerror = ()=>reject(request.error);\n        });\n    }\n    constructor(){\n        this.dbName = 'MediaStorage';\n        this.version = 1;\n        this.db = null;\n    }\n}\nconst mediaDB = new MediaDB();\n// Fallback к localStorage для метаданных (без изображений)\nconst STORAGE_KEY = 'media_metadata';\nconst saveToLocalStorage = (metadata)=>{\n    try {\n        localStorage.setItem(STORAGE_KEY, JSON.stringify(metadata));\n        return true;\n    } catch (error) {\n        console.error('Error saving to localStorage:', error);\n        return false;\n    }\n};\nconst getFromLocalStorage = ()=>{\n    try {\n        const data = localStorage.getItem(STORAGE_KEY);\n        return data ? JSON.parse(data) : [];\n    } catch (error) {\n        console.error('Error reading from localStorage:', error);\n        return [];\n    }\n};\n// Основные функции для работы с медиа\nconst uploadMediaFile = async (file)=>{\n    try {\n        // Проверяем, что это изображение\n        if (!file.type.startsWith('image/')) {\n            throw new Error('Поддерживаются только изображения');\n        }\n        // Создаем ID и метаданные\n        const id = Date.now().toString() + Math.random().toString(36).substr(2, 9);\n        const uploadedAt = new Date().toISOString();\n        // Сжимаем изображение\n        const compressedUrl = await compressImage(file);\n        const mediaFile = {\n            id,\n            name: file.name,\n            url: compressedUrl,\n            size: file.size,\n            type: file.type,\n            uploadedAt\n        };\n        // Пытаемся сохранить в IndexedDB\n        try {\n            await mediaDB.saveFile(mediaFile);\n        } catch (error) {\n            console.warn('IndexedDB failed, using localStorage fallback:', error);\n            // Fallback: сохраняем только метаданные в localStorage\n            const metadata = getFromLocalStorage();\n            metadata.push({\n                id,\n                name: file.name,\n                size: file.size,\n                type: file.type,\n                uploadedAt\n            });\n            if (!saveToLocalStorage(metadata)) {\n                throw new Error('Недостаточно места для хранения');\n            }\n            // Для fallback используем Object URL (временный)\n            mediaFile.url = URL.createObjectURL(file);\n        }\n        return mediaFile;\n    } catch (error) {\n        console.error('Error uploading media file:', error);\n        return null;\n    }\n};\nconst getMediaFiles = async ()=>{\n    try {\n        // Пытаемся получить из IndexedDB\n        const files = await mediaDB.getFiles();\n        return files.sort((a, b)=>new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime());\n    } catch (error) {\n        console.warn('IndexedDB failed, using localStorage fallback:', error);\n        // Fallback: получаем метаданные из localStorage\n        const metadata = getFromLocalStorage();\n        return metadata.map((meta)=>({\n                ...meta,\n                url: '' // URL будет пустой для fallback\n            })).sort((a, b)=>new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime());\n    }\n};\nconst deleteMediaFile = async (id)=>{\n    try {\n        // Пытаемся удалить из IndexedDB\n        await mediaDB.deleteFile(id);\n        return true;\n    } catch (error) {\n        console.warn('IndexedDB failed, using localStorage fallback:', error);\n        // Fallback: удаляем из localStorage\n        const metadata = getFromLocalStorage();\n        const filtered = metadata.filter((item)=>item.id !== id);\n        return saveToLocalStorage(filtered);\n    }\n};\nconst clearAllMedia = async ()=>{\n    try {\n        await mediaDB.clearAll();\n        localStorage.removeItem(STORAGE_KEY);\n        return true;\n    } catch (error) {\n        console.error('Error clearing media:', error);\n        return false;\n    }\n};\n// Утилита для проверки доступного места\nconst getStorageInfo = ()=>{\n    let used = 0;\n    let available = 5 * 1024 * 1024; // 5MB по умолчанию\n    try {\n        // Подсчитываем использованное место в localStorage\n        for(let key in localStorage){\n            if (localStorage.hasOwnProperty(key)) {\n                used += localStorage[key].length;\n            }\n        }\n    } catch (error) {\n        console.error('Error calculating storage:', error);\n    }\n    return {\n        used,\n        available: available - used\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/media-storage.ts\n"));

/***/ })

});