"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/blog/[id]/page",{

/***/ "(app-pages-browser)/./src/app/admin/blog/[id]/page.tsx":
/*!******************************************!*\
  !*** ./src/app/admin/blog/[id]/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/admin/AdminLayout */ \"(app-pages-browser)/./src/components/admin/AdminLayout.tsx\");\n/* harmony import */ var _components_admin_RichTextEditor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/admin/RichTextEditor */ \"(app-pages-browser)/./src/components/admin/RichTextEditor.tsx\");\n/* harmony import */ var _lib_cms_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/cms-utils */ \"(app-pages-browser)/./src/lib/cms-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst EditBlogPostPage = ()=>{\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [post, setPost] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showMediaGallery, setShowMediaGallery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mediaGalleryMode, setMediaGalleryMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('featured');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        slug: '',\n        excerpt: '',\n        content: '',\n        status: 'draft',\n        tags: '',\n        featuredImage: '',\n        contentImages: [],\n        metaTitle: '',\n        metaDescription: ''\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditBlogPostPage.useEffect\": ()=>{\n            if (params.id) {\n                const postData = (0,_lib_cms_utils__WEBPACK_IMPORTED_MODULE_5__.getBlogPost)(params.id);\n                if (postData) {\n                    setPost(postData);\n                    setFormData({\n                        title: postData.title,\n                        slug: postData.slug,\n                        excerpt: postData.excerpt,\n                        content: postData.content,\n                        status: postData.status === 'archived' ? 'draft' : postData.status,\n                        tags: postData.tags.join(', '),\n                        featuredImage: postData.featuredImage || '',\n                        contentImages: postData.contentImages || [],\n                        metaTitle: postData.seo.metaTitle || '',\n                        metaDescription: postData.seo.metaDescription || ''\n                    });\n                } else {\n                    router.push('/admin/blog');\n                }\n            }\n        }\n    }[\"EditBlogPostPage.useEffect\"], [\n        params.id,\n        router\n    ]);\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const handleFeaturedImageSelect = (imageUrl)=>{\n        setFormData((prev)=>({\n                ...prev,\n                featuredImage: imageUrl\n            }));\n        setShowMediaGallery(false);\n    };\n    const handleContentImagesSelect = (imageUrls)=>{\n        if (mediaGalleryMode === 'content') {\n            // Insert images into rich text editor using global function\n            imageUrls.forEach((imageUrl)=>{\n                if (window.insertImageIntoEditor) {\n                    window.insertImageIntoEditor(imageUrl);\n                }\n            });\n        } else {\n            // Legacy behavior for contentImages array\n            setFormData((prev)=>({\n                    ...prev,\n                    contentImages: imageUrls\n                }));\n        }\n        setShowMediaGallery(false);\n    };\n    const openMediaGallery = (mode)=>{\n        setMediaGalleryMode(mode);\n        setShowMediaGallery(true);\n    };\n    const removeContentImage = (imageUrl)=>{\n        setFormData((prev)=>({\n                ...prev,\n                contentImages: prev.contentImages.filter((url)=>url !== imageUrl)\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!post) return;\n        setIsLoading(true);\n        try {\n            const updatedData = {\n                title: formData.title,\n                slug: formData.slug,\n                excerpt: formData.excerpt,\n                content: formData.content,\n                status: formData.status,\n                featuredImage: formData.featuredImage || undefined,\n                contentImages: formData.contentImages.length > 0 ? formData.contentImages : undefined,\n                tags: formData.tags.split(',').map((tag)=>tag.trim()).filter(Boolean),\n                seo: {\n                    metaTitle: formData.metaTitle || formData.title,\n                    metaDescription: formData.metaDescription || formData.excerpt,\n                    keywords: formData.tags.split(',').map((tag)=>tag.trim()).filter(Boolean)\n                }\n            };\n            (0,_lib_cms_utils__WEBPACK_IMPORTED_MODULE_5__.updateBlogPost)(post.id, updatedData);\n            router.push('/admin/blog');\n        } catch (error) {\n            console.error('Ошибка при обновлении статьи:', error);\n            alert('Ошибка при обновлении статьи');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSaveDraft = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                status: 'draft'\n            }));\n        setTimeout(()=>{\n            var _document_getElementById;\n            (_document_getElementById = document.getElementById('submit-form')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n        }, 0);\n    };\n    const handlePublish = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                status: 'published'\n            }));\n        setTimeout(()=>{\n            var _document_getElementById;\n            (_document_getElementById = document.getElementById('submit-form')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n        }, 0);\n    };\n    const handleSave = ()=>{\n        // Сохранить с текущим статусом\n        setTimeout(()=>{\n            var _document_getElementById;\n            (_document_getElementById = document.getElementById('submit-form')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n        }, 0);\n    };\n    if (!post) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 min-h-screen bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Загрузка...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                lineNumber: 157,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n            lineNumber: 156,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"Редактирование статьи\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-gray-600\",\n                                        children: [\n                                            \"Редактирование: \",\n                                            post.title\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>router.push('/admin/blog'),\n                                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                        children: \"Отмена\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleSaveDraft,\n                                        disabled: isLoading,\n                                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\",\n                                        children: \"Сохранить черновик\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handlePublish,\n                                        disabled: isLoading,\n                                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\",\n                                        children: \"Опубликовать\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2 space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg shadow p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"title\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Заголовок статьи *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"title\",\n                                                        name: \"title\",\n                                                        required: true,\n                                                        value: formData.title,\n                                                        onChange: handleInputChange,\n                                                        className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                        placeholder: \"Введите заголовок статьи\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg shadow p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"slug\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"URL (slug) *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"slug\",\n                                                        name: \"slug\",\n                                                        required: true,\n                                                        value: formData.slug,\n                                                        onChange: handleInputChange,\n                                                        className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                        placeholder: \"url-статьи\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    \"URL статьи: /blog/\",\n                                                                    formData.slug || 'url-статьи'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                lineNumber: 246,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            formData.status === 'published' && formData.slug && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"/blog/\".concat(formData.slug),\n                                                                target: \"_blank\",\n                                                                rel: \"noopener noreferrer\",\n                                                                className: \"inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 mr-1\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                            lineNumber: 257,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                        lineNumber: 256,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    \"Посмотреть статью\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                lineNumber: 250,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg shadow p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"excerpt\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Краткое описание *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        id: \"excerpt\",\n                                                        name: \"excerpt\",\n                                                        required: true,\n                                                        rows: 3,\n                                                        value: formData.excerpt,\n                                                        onChange: handleInputChange,\n                                                        className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                        placeholder: \"Краткое описание статьи для превью\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg shadow p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Изображение превью\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            formData.featuredImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative inline-block\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: formData.featuredImage,\n                                                                        alt: \"Featured\",\n                                                                        className: \"w-32 h-32 object-cover rounded-lg border border-gray-300\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                        lineNumber: 294,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    featuredImage: ''\n                                                                                })),\n                                                                        className: \"absolute -top-2 -right-2 bg-red-600 text-white rounded-full p-1 hover:bg-red-700\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                                lineNumber: 305,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                            lineNumber: 304,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                        lineNumber: 299,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 23\n                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"mx-auto h-12 w-12 text-gray-400\",\n                                                                        stroke: \"currentColor\",\n                                                                        fill: \"none\",\n                                                                        viewBox: \"0 0 48 48\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\",\n                                                                            strokeWidth: 2,\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                            lineNumber: 312,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                        lineNumber: 311,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"mt-2 text-sm text-gray-600\",\n                                                                        children: \"Изображение не выбрано\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                        lineNumber: 314,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>openMediaGallery('featured'),\n                                                                className: \"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 mr-2\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                            lineNumber: 323,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                        lineNumber: 322,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \"Выбрать изображение\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg shadow p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-4\",\n                                                        children: \"Содержание статьи *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_RichTextEditor__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        value: formData.content,\n                                                        onChange: (content)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    content\n                                                                })),\n                                                        onImageInsert: ()=>openMediaGallery('content'),\n                                                        placeholder: \"Редактируйте содержание статьи...\",\n                                                        className: \"min-h-[500px]\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg shadow p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                    children: \"Публикация\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"status\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Статус\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"status\",\n                                                            name: \"status\",\n                                                            value: formData.status,\n                                                            onChange: handleInputChange,\n                                                            className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"draft\",\n                                                                    children: \"Черновик\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"published\",\n                                                                    children: \"Опубликовано\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 text-sm text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Создано: \",\n                                                                new Date(post.createdAt).toLocaleDateString('ru-RU')\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Обновлено: \",\n                                                                new Date(post.updatedAt).toLocaleDateString('ru-RU')\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg shadow p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                    children: \"Теги\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"tags\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Теги (через запятую)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"tags\",\n                                                            name: \"tags\",\n                                                            value: formData.tags,\n                                                            onChange: handleInputChange,\n                                                            className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                            placeholder: \"тег1, тег2, тег3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg shadow p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                    children: \"SEO\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"metaTitle\",\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: \"Meta Title\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                    lineNumber: 398,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    id: \"metaTitle\",\n                                                                    name: \"metaTitle\",\n                                                                    value: formData.metaTitle,\n                                                                    onChange: handleInputChange,\n                                                                    className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                    placeholder: \"Заголовок для поисковых систем\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                    lineNumber: 401,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"metaDescription\",\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: \"Meta Description\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                    lineNumber: 412,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                    id: \"metaDescription\",\n                                                                    name: \"metaDescription\",\n                                                                    rows: 3,\n                                                                    value: formData.metaDescription,\n                                                                    onChange: handleInputChange,\n                                                                    className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                    placeholder: \"Описание для поисковых систем\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                                    lineNumber: 415,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            id: \"submit-form\",\n                            className: \"hidden\",\n                            disabled: isLoading,\n                            children: \"Submit\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n            lineNumber: 168,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/[id]/page.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EditBlogPostPage, \"plQS+yY8DgQlQno2JbaIS6SVM3M=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c = EditBlogPostPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EditBlogPostPage);\nvar _c;\n$RefreshReg$(_c, \"EditBlogPostPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/blog/[id]/page.tsx\n"));

/***/ })

});