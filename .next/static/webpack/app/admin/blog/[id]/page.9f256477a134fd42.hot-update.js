"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/blog/[id]/page",{

/***/ "(app-pages-browser)/./src/components/admin/MediaGallery.tsx":
/*!***********************************************!*\
  !*** ./src/components/admin/MediaGallery.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_cms_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/cms-utils */ \"(app-pages-browser)/./src/lib/cms-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst MediaGallery = (param)=>{\n    let { isOpen, onClose, onSelect, selectedImage, multiple = false, onSelectMultiple, mode = 'content', title } = param;\n    _s();\n    const [mediaFiles, setMediaFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedFiles, setSelectedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOver, setDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaGallery.useEffect\": ()=>{\n            if (isOpen) {\n                loadMediaFiles();\n            }\n        }\n    }[\"MediaGallery.useEffect\"], [\n        isOpen\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaGallery.useEffect\": ()=>{\n            // Инициализируем выбранные файлы только для режима превью\n            if (mode === 'featured' && selectedImage && !multiple) {\n                setSelectedFiles([\n                    selectedImage\n                ]);\n            } else if (mode === 'content') {\n                // Для контента всегда начинаем с пустого выбора\n                setSelectedFiles([]);\n            }\n        }\n    }[\"MediaGallery.useEffect\"], [\n        selectedImage,\n        multiple,\n        mode,\n        isOpen\n    ]);\n    const loadMediaFiles = ()=>{\n        const files = (0,_lib_cms_utils__WEBPACK_IMPORTED_MODULE_2__.getMediaFiles)();\n        setMediaFiles(files);\n    };\n    const handleFileUpload = async (files)=>{\n        setUploading(true);\n        for(let i = 0; i < files.length; i++){\n            const file = files[i];\n            // Check if file is an image\n            if (!file.type.startsWith('image/')) {\n                continue;\n            }\n            // Create a data URL for the image\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                var _e_target;\n                const dataUrl = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n                const mediaFile = {\n                    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n                    name: file.name.replace(/\\.[^/.]+$/, \"\"),\n                    originalName: file.name,\n                    size: file.size,\n                    type: file.type,\n                    url: dataUrl,\n                    uploadedAt: new Date().toISOString()\n                };\n                (0,_lib_cms_utils__WEBPACK_IMPORTED_MODULE_2__.saveMediaFile)(mediaFile);\n                loadMediaFiles();\n            };\n            reader.readAsDataURL(file);\n        }\n        setUploading(false);\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        setDragOver(false);\n        const files = e.dataTransfer.files;\n        if (files.length > 0) {\n            handleFileUpload(files);\n        }\n    };\n    const handleFileSelect = (e)=>{\n        const files = e.target.files;\n        if (files && files.length > 0) {\n            handleFileUpload(files);\n        }\n    };\n    const handleImageSelect = (imageUrl)=>{\n        if (multiple) {\n            const newSelection = selectedFiles.includes(imageUrl) ? selectedFiles.filter((url)=>url !== imageUrl) : [\n                ...selectedFiles,\n                imageUrl\n            ];\n            setSelectedFiles(newSelection);\n        } else {\n            setSelectedFiles([\n                imageUrl\n            ]);\n        }\n    };\n    const handleConfirmSelection = ()=>{\n        if (multiple && onSelectMultiple) {\n            onSelectMultiple(selectedFiles);\n        } else if (selectedFiles.length > 0) {\n            onSelect(selectedFiles[0]);\n        }\n        onClose();\n    };\n    const handleDeleteFile = (fileId)=>{\n        if (confirm('Удалить это изображение?')) {\n            (0,_lib_cms_utils__WEBPACK_IMPORTED_MODULE_2__.deleteMediaFile)(fileId);\n            loadMediaFiles();\n            // Remove from selection if selected\n            setSelectedFiles((prev)=>prev.filter((url)=>{\n                    const file = mediaFiles.find((f)=>f.id === fileId);\n                    return file ? url !== file.url : true;\n                }));\n        }\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: \"Медиа галерея\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onClose,\n                                            className: \"text-gray-400 hover:text-gray-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M6 18L18 6M6 6l12 12\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-2 border-dashed rounded-lg p-6 text-center mb-6 transition-colors \".concat(dragOver ? 'border-primary-500 bg-primary-50' : 'border-gray-300'),\n                                    onDrop: handleDrop,\n                                    onDragOver: (e)=>{\n                                        e.preventDefault();\n                                        setDragOver(true);\n                                    },\n                                    onDragLeave: ()=>setDragOver(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"mx-auto h-12 w-12 text-gray-400\",\n                                            stroke: \"currentColor\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 48 48\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\",\n                                                strokeWidth: 2,\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"file-upload\",\n                                                    className: \"cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"mt-2 block text-sm font-medium text-gray-900\",\n                                                            children: [\n                                                                \"Перетащите изображения сюда или\",\n                                                                ' ',\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-primary-600 hover:text-primary-500\",\n                                                                    children: \"выберите файлы\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                                    lineNumber: 191,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"file-upload\",\n                                                            name: \"file-upload\",\n                                                            type: \"file\",\n                                                            className: \"sr-only\",\n                                                            multiple: true,\n                                                            accept: \"image/*\",\n                                                            onChange: handleFileSelect\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-xs text-gray-500\",\n                                                    children: \"PNG, JPG, GIF до 10MB\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, undefined),\n                                uploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Загрузка...\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 max-h-96 overflow-y-auto\",\n                                    children: mediaFiles.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all \".concat(selectedFiles.includes(file.url) ? 'border-primary-500 ring-2 ring-primary-200' : 'border-gray-200 hover:border-gray-300'),\n                                            onClick: ()=>handleImageSelect(file.url),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-square\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: file.url,\n                                                        alt: file.name,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                selectedFiles.includes(file.url) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-2 right-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-5 h-5 bg-primary-600 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-3 h-3 text-white\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        handleDeleteFile(file.id);\n                                                    },\n                                                    className: \"absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity bg-red-600 text-white rounded-full p-1 hover:bg-red-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white p-2 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs truncate\",\n                                                            children: file.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-300\",\n                                                            children: formatFileSize(file.size)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, file.id, true, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, undefined),\n                                mediaFiles.length === 0 && !uploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"mx-auto h-12 w-12 text-gray-400\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-sm text-gray-500\",\n                                            children: \"Нет загруженных изображений\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleConfirmSelection,\n                                    disabled: selectedFiles.length === 0,\n                                    className: \"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: multiple ? \"Выбрать (\".concat(selectedFiles.length, \")\") : 'Выбрать'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm\",\n                                    children: \"Отмена\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n            lineNumber: 150,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n        lineNumber: 149,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MediaGallery, \"WU8LBYv1uTx3EGcVj6Eta3JMTIc=\");\n_c = MediaGallery;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MediaGallery);\nvar _c;\n$RefreshReg$(_c, \"MediaGallery\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/MediaGallery.tsx\n"));

/***/ })

});