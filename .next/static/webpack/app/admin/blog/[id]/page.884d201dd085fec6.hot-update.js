"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/blog/[id]/page",{

/***/ "(app-pages-browser)/./src/components/admin/SimpleMarkdownEditor.tsx":
/*!*******************************************************!*\
  !*** ./src/components/admin/SimpleMarkdownEditor.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst SimpleMarkdownEditor = (param)=>{\n    let { value, onChange, onImageInsert, placeholder = \"Начните писать...\", className = \"\" } = param;\n    _s();\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isPreview, setIsPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Функция для создания короткого ID изображения\n    const createImageShortcode = (imageUrl)=>{\n        // Создаем короткий ID из URL\n        const urlParts = imageUrl.split('/');\n        const filename = urlParts[urlParts.length - 1];\n        const shortId = filename.split('.')[0].slice(-8); // Последние 8 символов имени файла\n        return \"[img:\".concat(shortId, \"]\");\n    };\n    // Функция для вставки изображения в позицию курсора\n    const insertImageAtCursor = (imageUrl)=>{\n        if (textareaRef.current) {\n            const textarea = textareaRef.current;\n            const start = textarea.selectionStart;\n            const end = textarea.selectionEnd;\n            // Создаем шорткод вместо полного markdown\n            const imageShortcode = createImageShortcode(imageUrl);\n            // Сохраняем соответствие шорткода и URL в комментарии\n            const imageComment = \"<!-- \".concat(imageShortcode, \" = \").concat(imageUrl, \" -->\");\n            const beforeCursor = value.substring(0, start);\n            const afterCursor = value.substring(end);\n            // Добавляем переносы строк для красивого форматирования\n            const needsNewlineBefore = start > 0 && !beforeCursor.endsWith('\\n');\n            const needsNewlineAfter = afterCursor.length > 0 && !afterCursor.startsWith('\\n');\n            const newContent = beforeCursor + (needsNewlineBefore ? '\\n' : '') + imageShortcode + (needsNewlineAfter ? '\\n' : '') + afterCursor;\n            // Добавляем комментарий с соответствием в конец документа, если его еще нет\n            const finalContent = newContent.includes(imageComment) ? newContent : newContent + '\\n\\n' + imageComment;\n            onChange(finalContent);\n            // Устанавливаем курсор после вставленного шорткода\n            const newCursorPosition = start + (needsNewlineBefore ? 1 : 0) + imageShortcode.length;\n            setTimeout(()=>{\n                textarea.focus();\n                textarea.setSelectionRange(newCursorPosition, newCursorPosition);\n            }, 0);\n        }\n    };\n    // Глобальная функция для вставки изображений\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleMarkdownEditor.useEffect\": ()=>{\n            window.insertImageIntoEditor = insertImageAtCursor;\n            return ({\n                \"SimpleMarkdownEditor.useEffect\": ()=>{\n                    delete window.insertImageIntoEditor;\n                }\n            })[\"SimpleMarkdownEditor.useEffect\"];\n        }\n    }[\"SimpleMarkdownEditor.useEffect\"], [\n        value\n    ]);\n    // Функция для конвертации markdown в HTML для превью\n    const markdownToHtml = (markdown)=>{\n        return markdown.replace(/^### (.*$)/gim, '<h3 class=\"text-xl font-semibold text-gray-900 mb-3 mt-6\">$1</h3>').replace(/^## (.*$)/gim, '<h2 class=\"text-2xl font-semibold text-gray-900 mb-4 mt-8\">$1</h2>').replace(/^# (.*$)/gim, '<h1 class=\"text-3xl font-bold text-gray-900 mb-6\">$1</h1>').replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>').replace(/\\*(.*?)\\*/g, '<em>$1</em>').replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, '<div class=\"my-6\"><img src=\"$2\" alt=\"$1\" class=\"w-full max-w-2xl mx-auto rounded-lg shadow-md\" /></div>').replace(/\\n\\n/g, '</p><p class=\"text-gray-700 mb-4 leading-relaxed\">').replace(/^(.+)$/gm, '<p class=\"text-gray-700 mb-4 leading-relaxed\">$1</p>').replace(/<p class=\"text-gray-700 mb-4 leading-relaxed\"><\\/p>/g, '').replace(/<p class=\"text-gray-700 mb-4 leading-relaxed\">(<h[1-6][^>]*>.*<\\/h[1-6]>)<\\/p>/g, '$1').replace(/<p class=\"text-gray-700 mb-4 leading-relaxed\">(<div[^>]*>.*<\\/div>)<\\/p>/g, '$1');\n    };\n    const toolbarButtons = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                children: \"H1\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 100,\n                columnNumber: 13\n            }, undefined),\n            title: 'Заголовок 1',\n            action: ()=>insertText('# ')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                children: \"H2\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 105,\n                columnNumber: 13\n            }, undefined),\n            title: 'Заголовок 2',\n            action: ()=>insertText('## ')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                children: \"H3\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 110,\n                columnNumber: 13\n            }, undefined),\n            title: 'Заголовок 3',\n            action: ()=>insertText('### ')\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                children: \"B\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 116,\n                columnNumber: 13\n            }, undefined),\n            title: 'Жирный',\n            action: ()=>wrapText('**', '**')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                children: \"I\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 121,\n                columnNumber: 13\n            }, undefined),\n            title: 'Курсив',\n            action: ()=>wrapText('*', '*')\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, undefined),\n            title: 'Изображение',\n            action: onImageInsert || (()=>{})\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 138,\n                columnNumber: 9\n            }, undefined),\n            title: isPreview ? 'Редактировать' : 'Превью',\n            action: ()=>setIsPreview(!isPreview)\n        }\n    ];\n    const insertText = (text)=>{\n        if (textareaRef.current) {\n            const textarea = textareaRef.current;\n            const start = textarea.selectionStart;\n            const end = textarea.selectionEnd;\n            const beforeCursor = value.substring(0, start);\n            const afterCursor = value.substring(end);\n            const newContent = beforeCursor + text + afterCursor;\n            onChange(newContent);\n            setTimeout(()=>{\n                textarea.focus();\n                textarea.setSelectionRange(start + text.length, start + text.length);\n            }, 0);\n        }\n    };\n    const wrapText = (before, after)=>{\n        if (textareaRef.current) {\n            const textarea = textareaRef.current;\n            const start = textarea.selectionStart;\n            const end = textarea.selectionEnd;\n            const selectedText = value.substring(start, end);\n            const beforeCursor = value.substring(0, start);\n            const afterCursor = value.substring(end);\n            const newContent = beforeCursor + before + selectedText + after + afterCursor;\n            onChange(newContent);\n            setTimeout(()=>{\n                textarea.focus();\n                if (selectedText) {\n                    textarea.setSelectionRange(start + before.length, end + before.length);\n                } else {\n                    textarea.setSelectionRange(start + before.length, start + before.length);\n                }\n            }, 0);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border border-gray-300 rounded-lg overflow-hidden \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 border-b border-gray-300 p-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap items-center gap-1\",\n                    children: toolbarButtons.map((button, index)=>{\n                        if (button.type === 'separator') {\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-px h-6 bg-gray-300 mx-1\"\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 22\n                            }, undefined);\n                        }\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: button.action,\n                            title: button.title,\n                            className: \"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors\",\n                            children: button.icon\n                        }, index, false, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, undefined),\n            isPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-[400px] p-4 prose prose-lg max-w-none bg-white\",\n                dangerouslySetInnerHTML: {\n                    __html: markdownToHtml(value)\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 218,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                ref: textareaRef,\n                value: value,\n                onChange: (e)=>onChange(e.target.value),\n                placeholder: placeholder,\n                className: \"w-full min-h-[400px] p-4 border-none resize-none focus:outline-none font-mono text-sm leading-relaxed\",\n                style: {\n                    fontFamily: 'Monaco, Menlo, \"Ubuntu Mono\", monospace'\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 223,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n        lineNumber: 192,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SimpleMarkdownEditor, \"2Khj9zGOxvkdc2HjTdSVtR+iHPI=\");\n_c = SimpleMarkdownEditor;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SimpleMarkdownEditor);\nvar _c;\n$RefreshReg$(_c, \"SimpleMarkdownEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/SimpleMarkdownEditor.tsx\n"));

/***/ })

});