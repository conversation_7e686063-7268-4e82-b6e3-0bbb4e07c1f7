"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/blog/[id]/page",{

/***/ "(app-pages-browser)/./src/components/admin/MediaGallery.tsx":
/*!***********************************************!*\
  !*** ./src/components/admin/MediaGallery.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_media_storage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/media-storage */ \"(app-pages-browser)/./src/lib/media-storage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst MediaGallery = (param)=>{\n    let { isOpen, onClose, onSelect, selectedImage, multiple = false, onSelectMultiple, mode = 'content', title } = param;\n    _s();\n    const [mediaFiles, setMediaFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedFiles, setSelectedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOver, setDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaGallery.useEffect\": ()=>{\n            if (isOpen) {\n                loadMediaFiles();\n            }\n        }\n    }[\"MediaGallery.useEffect\"], [\n        isOpen\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaGallery.useEffect\": ()=>{\n            // Инициализируем выбранные файлы только для режима превью\n            if (mode === 'featured' && selectedImage && !multiple) {\n                setSelectedFiles([\n                    selectedImage\n                ]);\n            } else if (mode === 'content') {\n                // Для контента всегда начинаем с пустого выбора\n                setSelectedFiles([]);\n            }\n        }\n    }[\"MediaGallery.useEffect\"], [\n        selectedImage,\n        multiple,\n        mode,\n        isOpen\n    ]);\n    const loadMediaFiles = async ()=>{\n        try {\n            const files = await (0,_lib_media_storage__WEBPACK_IMPORTED_MODULE_2__.getMediaFiles)();\n            setMediaFiles(files);\n        } catch (error) {\n            console.error('Error loading media files:', error);\n            setMediaFiles([]);\n        }\n    };\n    const handleFileUpload = async (files)=>{\n        setUploading(true);\n        try {\n            const uploadPromises = Array.from(files).map(async (file)=>{\n                if (!file.type.startsWith('image/')) {\n                    return null;\n                }\n                const mediaFile = await (0,_lib_media_storage__WEBPACK_IMPORTED_MODULE_2__.uploadMediaFile)(file);\n                return mediaFile;\n            });\n            const results = await Promise.all(uploadPromises);\n            const successfulUploads = results.filter(Boolean);\n            if (successfulUploads.length > 0) {\n                await loadMediaFiles();\n            }\n            if (successfulUploads.length < files.length) {\n                alert(\"Загружено \".concat(successfulUploads.length, \" из \").concat(files.length, \" файлов. Некоторые файлы не удалось загрузить.\"));\n            }\n        } catch (error) {\n            console.error('Error uploading files:', error);\n            alert('Ошибка при загрузке файлов. Возможно, недостаточно места для хранения.');\n        } finally{\n            setUploading(false);\n        }\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        setDragOver(false);\n        const files = e.dataTransfer.files;\n        if (files.length > 0) {\n            handleFileUpload(files);\n        }\n    };\n    const handleFileSelect = (e)=>{\n        const files = e.target.files;\n        if (files && files.length > 0) {\n            handleFileUpload(files);\n        }\n    };\n    const handleImageSelect = (imageUrl)=>{\n        if (multiple) {\n            const newSelection = selectedFiles.includes(imageUrl) ? selectedFiles.filter((url)=>url !== imageUrl) : [\n                ...selectedFiles,\n                imageUrl\n            ];\n            setSelectedFiles(newSelection);\n        } else {\n            setSelectedFiles([\n                imageUrl\n            ]);\n        }\n    };\n    const handleConfirmSelection = ()=>{\n        if (multiple && onSelectMultiple) {\n            onSelectMultiple(selectedFiles);\n        } else if (selectedFiles.length > 0) {\n            onSelect(selectedFiles[0]);\n        }\n        onClose();\n    };\n    const handleDeleteFile = (fileId)=>{\n        if (confirm('Удалить это изображение?')) {\n            (0,_lib_media_storage__WEBPACK_IMPORTED_MODULE_2__.deleteMediaFile)(fileId);\n            loadMediaFiles();\n            // Remove from selection if selected\n            setSelectedFiles((prev)=>prev.filter((url)=>{\n                    const file = mediaFiles.find((f)=>f.id === fileId);\n                    return file ? url !== file.url : true;\n                }));\n        }\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: title || (mode === 'featured' ? 'Выбрать изображение превью' : 'Выбрать изображения для контента')\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onClose,\n                                            className: \"text-gray-400 hover:text-gray-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M6 18L18 6M6 6l12 12\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-2 border-dashed rounded-lg p-6 text-center mb-6 transition-colors \".concat(dragOver ? 'border-primary-500 bg-primary-50' : 'border-gray-300'),\n                                    onDrop: handleDrop,\n                                    onDragOver: (e)=>{\n                                        e.preventDefault();\n                                        setDragOver(true);\n                                    },\n                                    onDragLeave: ()=>setDragOver(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"mx-auto h-12 w-12 text-gray-400\",\n                                            stroke: \"currentColor\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 48 48\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\",\n                                                strokeWidth: 2,\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"file-upload\",\n                                                    className: \"cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"mt-2 block text-sm font-medium text-gray-900\",\n                                                            children: [\n                                                                \"Перетащите изображения сюда или\",\n                                                                ' ',\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-primary-600 hover:text-primary-500\",\n                                                                    children: \"выберите файлы\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                                    lineNumber: 191,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"file-upload\",\n                                                            name: \"file-upload\",\n                                                            type: \"file\",\n                                                            className: \"sr-only\",\n                                                            multiple: true,\n                                                            accept: \"image/*\",\n                                                            onChange: handleFileSelect\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-xs text-gray-500\",\n                                                    children: \"PNG, JPG, GIF до 10MB\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, undefined),\n                                uploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Загрузка...\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 max-h-96 overflow-y-auto\",\n                                    children: mediaFiles.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all \".concat(selectedFiles.includes(file.url) ? 'border-primary-500 ring-2 ring-primary-200' : 'border-gray-200 hover:border-gray-300'),\n                                            onClick: ()=>handleImageSelect(file.url),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-square\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: file.url,\n                                                        alt: file.name,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                selectedFiles.includes(file.url) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-2 right-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-5 h-5 bg-primary-600 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-3 h-3 text-white\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        handleDeleteFile(file.id);\n                                                    },\n                                                    className: \"absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity bg-red-600 text-white rounded-full p-1 hover:bg-red-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white p-2 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs truncate\",\n                                                            children: file.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-300\",\n                                                            children: formatFileSize(file.size)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, file.id, true, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, undefined),\n                                mediaFiles.length === 0 && !uploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"mx-auto h-12 w-12 text-gray-400\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-sm text-gray-500\",\n                                            children: \"Нет загруженных изображений\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleConfirmSelection,\n                                    disabled: selectedFiles.length === 0,\n                                    className: \"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: mode === 'featured' ? 'Установить как превью' : multiple ? \"Вставить в статью (\".concat(selectedFiles.length, \")\") : 'Вставить в статью'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm\",\n                                    children: \"Отмена\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n            lineNumber: 150,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n        lineNumber: 149,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MediaGallery, \"WU8LBYv1uTx3EGcVj6Eta3JMTIc=\");\n_c = MediaGallery;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MediaGallery);\nvar _c;\n$RefreshReg$(_c, \"MediaGallery\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2FkbWluL01lZGlhR2FsbGVyeS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUVtRDtBQUM4RDtBQWFqSCxNQUFNTSxlQUE0QztRQUFDLEVBQ2pEQyxNQUFNLEVBQ05DLE9BQU8sRUFDUEMsUUFBUSxFQUNSQyxhQUFhLEVBQ2JDLFdBQVcsS0FBSyxFQUNoQkMsZ0JBQWdCLEVBQ2hCQyxPQUFPLFNBQVMsRUFDaEJDLEtBQUssRUFDTjs7SUFDQyxNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR2YsK0NBQVFBLENBQWMsRUFBRTtJQUM1RCxNQUFNLENBQUNnQixlQUFlQyxpQkFBaUIsR0FBR2pCLCtDQUFRQSxDQUFXLEVBQUU7SUFDL0QsTUFBTSxDQUFDa0IsV0FBV0MsYUFBYSxHQUFHbkIsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDb0IsVUFBVUMsWUFBWSxHQUFHckIsK0NBQVFBLENBQUM7SUFFekNDLGdEQUFTQTtrQ0FBQztZQUNSLElBQUlLLFFBQVE7Z0JBQ1ZnQjtZQUNGO1FBQ0Y7aUNBQUc7UUFBQ2hCO0tBQU87SUFFWEwsZ0RBQVNBO2tDQUFDO1lBQ1IsMERBQTBEO1lBQzFELElBQUlXLFNBQVMsY0FBY0gsaUJBQWlCLENBQUNDLFVBQVU7Z0JBQ3JETyxpQkFBaUI7b0JBQUNSO2lCQUFjO1lBQ2xDLE9BQU8sSUFBSUcsU0FBUyxXQUFXO2dCQUM3QixnREFBZ0Q7Z0JBQ2hESyxpQkFBaUIsRUFBRTtZQUNyQjtRQUNGO2lDQUFHO1FBQUNSO1FBQWVDO1FBQVVFO1FBQU1OO0tBQU87SUFFMUMsTUFBTWdCLGlCQUFpQjtRQUNyQixJQUFJO1lBQ0YsTUFBTUMsUUFBUSxNQUFNckIsaUVBQWFBO1lBQ2pDYSxjQUFjUTtRQUNoQixFQUFFLE9BQU9DLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDhCQUE4QkE7WUFDNUNULGNBQWMsRUFBRTtRQUNsQjtJQUNGO0lBRUEsTUFBTVcsbUJBQW1CLE9BQU9IO1FBQzlCSixhQUFhO1FBRWIsSUFBSTtZQUNGLE1BQU1RLGlCQUFpQkMsTUFBTUMsSUFBSSxDQUFDTixPQUFPTyxHQUFHLENBQUMsT0FBT0M7Z0JBQ2xELElBQUksQ0FBQ0EsS0FBS0MsSUFBSSxDQUFDQyxVQUFVLENBQUMsV0FBVztvQkFDbkMsT0FBTztnQkFDVDtnQkFFQSxNQUFNQyxZQUFZLE1BQU0vQixtRUFBZUEsQ0FBQzRCO2dCQUN4QyxPQUFPRztZQUNUO1lBRUEsTUFBTUMsVUFBVSxNQUFNQyxRQUFRQyxHQUFHLENBQUNWO1lBQ2xDLE1BQU1XLG9CQUFvQkgsUUFBUUksTUFBTSxDQUFDQztZQUV6QyxJQUFJRixrQkFBa0JHLE1BQU0sR0FBRyxHQUFHO2dCQUNoQyxNQUFNbkI7WUFDUjtZQUVBLElBQUlnQixrQkFBa0JHLE1BQU0sR0FBR2xCLE1BQU1rQixNQUFNLEVBQUU7Z0JBQzNDQyxNQUFNLGFBQTRDbkIsT0FBL0JlLGtCQUFrQkcsTUFBTSxFQUFDLFFBQW1CLE9BQWJsQixNQUFNa0IsTUFBTSxFQUFDO1lBQ2pFO1FBQ0YsRUFBRSxPQUFPakIsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsMEJBQTBCQTtZQUN4Q2tCLE1BQU07UUFDUixTQUFVO1lBQ1J2QixhQUFhO1FBQ2Y7SUFDRjtJQUVBLE1BQU13QixhQUFhLENBQUNDO1FBQ2xCQSxFQUFFQyxjQUFjO1FBQ2hCeEIsWUFBWTtRQUVaLE1BQU1FLFFBQVFxQixFQUFFRSxZQUFZLENBQUN2QixLQUFLO1FBQ2xDLElBQUlBLE1BQU1rQixNQUFNLEdBQUcsR0FBRztZQUNwQmYsaUJBQWlCSDtRQUNuQjtJQUNGO0lBRUEsTUFBTXdCLG1CQUFtQixDQUFDSDtRQUN4QixNQUFNckIsUUFBUXFCLEVBQUVJLE1BQU0sQ0FBQ3pCLEtBQUs7UUFDNUIsSUFBSUEsU0FBU0EsTUFBTWtCLE1BQU0sR0FBRyxHQUFHO1lBQzdCZixpQkFBaUJIO1FBQ25CO0lBQ0Y7SUFFQSxNQUFNMEIsb0JBQW9CLENBQUNDO1FBQ3pCLElBQUl4QyxVQUFVO1lBQ1osTUFBTXlDLGVBQWVuQyxjQUFjb0MsUUFBUSxDQUFDRixZQUN4Q2xDLGNBQWN1QixNQUFNLENBQUNjLENBQUFBLE1BQU9BLFFBQVFILFlBQ3BDO21CQUFJbEM7Z0JBQWVrQzthQUFTO1lBQ2hDakMsaUJBQWlCa0M7UUFDbkIsT0FBTztZQUNMbEMsaUJBQWlCO2dCQUFDaUM7YUFBUztRQUM3QjtJQUNGO0lBRUEsTUFBTUkseUJBQXlCO1FBQzdCLElBQUk1QyxZQUFZQyxrQkFBa0I7WUFDaENBLGlCQUFpQks7UUFDbkIsT0FBTyxJQUFJQSxjQUFjeUIsTUFBTSxHQUFHLEdBQUc7WUFDbkNqQyxTQUFTUSxhQUFhLENBQUMsRUFBRTtRQUMzQjtRQUNBVDtJQUNGO0lBRUEsTUFBTWdELG1CQUFtQixDQUFDQztRQUN4QixJQUFJQyxRQUFRLDZCQUE2QjtZQUN2Q3JELG1FQUFlQSxDQUFDb0Q7WUFDaEJsQztZQUNBLG9DQUFvQztZQUNwQ0wsaUJBQWlCeUMsQ0FBQUEsT0FBUUEsS0FBS25CLE1BQU0sQ0FBQ2MsQ0FBQUE7b0JBQ25DLE1BQU10QixPQUFPakIsV0FBVzZDLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUMsRUFBRSxLQUFLTDtvQkFDM0MsT0FBT3pCLE9BQU9zQixRQUFRdEIsS0FBS3NCLEdBQUcsR0FBRztnQkFDbkM7UUFDRjtJQUNGO0lBRUEsTUFBTVMsaUJBQWlCLENBQUNDO1FBQ3RCLElBQUlBLFVBQVUsR0FBRyxPQUFPO1FBQ3hCLE1BQU1DLElBQUk7UUFDVixNQUFNQyxRQUFRO1lBQUM7WUFBUztZQUFNO1lBQU07U0FBSztRQUN6QyxNQUFNQyxJQUFJQyxLQUFLQyxLQUFLLENBQUNELEtBQUtFLEdBQUcsQ0FBQ04sU0FBU0ksS0FBS0UsR0FBRyxDQUFDTDtRQUNoRCxPQUFPTSxXQUFXLENBQUNQLFFBQVFJLEtBQUtJLEdBQUcsQ0FBQ1AsR0FBR0UsRUFBQyxFQUFHTSxPQUFPLENBQUMsTUFBTSxNQUFNUCxLQUFLLENBQUNDLEVBQUU7SUFDekU7SUFFQSxJQUFJLENBQUM1RCxRQUFRLE9BQU87SUFFcEIscUJBQ0UsOERBQUNtRTtRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7b0JBQ0NDLFdBQVU7b0JBQ1ZDLFNBQVNwRTs7Ozs7OzhCQUlYLDhEQUFDa0U7b0JBQUlDLFdBQVU7O3NDQUViLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0U7NENBQUdGLFdBQVU7c0RBQ1g3RCxTQUFVRCxDQUFBQSxTQUFTLGFBQWEsK0JBQStCLGtDQUFpQzs7Ozs7O3NEQUVuRyw4REFBQ2lFOzRDQUNDRixTQUFTcEU7NENBQ1RtRSxXQUFVO3NEQUVWLDRFQUFDSTtnREFBSUosV0FBVTtnREFBVUssTUFBSztnREFBT0MsUUFBTztnREFBZUMsU0FBUTswREFDakUsNEVBQUNDO29EQUFLQyxlQUFjO29EQUFRQyxnQkFBZTtvREFBUUMsYUFBYTtvREFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FNM0UsOERBQUNiO29DQUNDQyxXQUFXLDRFQUVWLE9BREN0RCxXQUFXLHFDQUFxQztvQ0FFbERtRSxRQUFRNUM7b0NBQ1I2QyxZQUFZLENBQUM1Qzt3Q0FBUUEsRUFBRUMsY0FBYzt3Q0FBSXhCLFlBQVk7b0NBQU87b0NBQzVEb0UsYUFBYSxJQUFNcEUsWUFBWTs7c0RBRS9CLDhEQUFDeUQ7NENBQUlKLFdBQVU7NENBQWtDTSxRQUFPOzRDQUFlRCxNQUFLOzRDQUFPRSxTQUFRO3NEQUN6Riw0RUFBQ0M7Z0RBQUtJLEdBQUU7Z0RBQXlMRCxhQUFhO2dEQUFHRixlQUFjO2dEQUFRQyxnQkFBZTs7Ozs7Ozs7Ozs7c0RBRXhQLDhEQUFDWDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNnQjtvREFBTUMsU0FBUTtvREFBY2pCLFdBQVU7O3NFQUNyQyw4REFBQ2tCOzREQUFLbEIsV0FBVTs7Z0VBQStDO2dFQUM3Qjs4RUFDaEMsOERBQUNrQjtvRUFBS2xCLFdBQVU7OEVBQTBDOzs7Ozs7Ozs7Ozs7c0VBRTVELDhEQUFDbUI7NERBQ0NoQyxJQUFHOzREQUNIaUMsTUFBSzs0REFDTDlELE1BQUs7NERBQ0wwQyxXQUFVOzREQUNWaEUsUUFBUTs0REFDUnFGLFFBQU87NERBQ1BDLFVBQVVqRDs7Ozs7Ozs7Ozs7OzhEQUdkLDhEQUFDa0Q7b0RBQUV2QixXQUFVOzhEQUE2Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dDQU03Q3hELDJCQUNDLDhEQUFDdUQ7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7Ozs7OzswREFDZiw4REFBQ2tCO2dEQUFLbEIsV0FBVTswREFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQU05Qyw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ1o1RCxXQUFXZ0IsR0FBRyxDQUFDLENBQUNDLHFCQUNmLDhEQUFDMEM7NENBRUNDLFdBQVcsb0ZBSVYsT0FIQzFELGNBQWNvQyxRQUFRLENBQUNyQixLQUFLc0IsR0FBRyxJQUMzQiwrQ0FDQTs0Q0FFTnNCLFNBQVMsSUFBTTFCLGtCQUFrQmxCLEtBQUtzQixHQUFHOzs4REFFekMsOERBQUNvQjtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ3dCO3dEQUNDQyxLQUFLcEUsS0FBS3NCLEdBQUc7d0RBQ2IrQyxLQUFLckUsS0FBSytELElBQUk7d0RBQ2RwQixXQUFVOzs7Ozs7Ozs7OztnREFLYjFELGNBQWNvQyxRQUFRLENBQUNyQixLQUFLc0IsR0FBRyxtQkFDOUIsOERBQUNvQjtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ0Q7d0RBQUlDLFdBQVU7a0VBQ2IsNEVBQUNJOzREQUFJSixXQUFVOzREQUFxQkssTUFBSzs0REFBZUUsU0FBUTtzRUFDOUQsNEVBQUNDO2dFQUFLbUIsVUFBUztnRUFBVWYsR0FBRTtnRUFBcUhnQixVQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBT2pLLDhEQUFDekI7b0RBQ0NGLFNBQVMsQ0FBQy9CO3dEQUNSQSxFQUFFMkQsZUFBZTt3REFDakJoRCxpQkFBaUJ4QixLQUFLOEIsRUFBRTtvREFDMUI7b0RBQ0FhLFdBQVU7OERBRVYsNEVBQUNJO3dEQUFJSixXQUFVO3dEQUFVSyxNQUFLO3dEQUFlRSxTQUFRO2tFQUNuRCw0RUFBQ0M7NERBQUttQixVQUFTOzREQUFVZixHQUFFOzREQUFxTWdCLFVBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7OERBSzdPLDhEQUFDN0I7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDdUI7NERBQUV2QixXQUFVO3NFQUFvQjNDLEtBQUsrRCxJQUFJOzs7Ozs7c0VBQzFDLDhEQUFDRzs0REFBRXZCLFdBQVU7c0VBQXlCWixlQUFlL0IsS0FBS3lFLElBQUk7Ozs7Ozs7Ozs7Ozs7MkNBM0MzRHpFLEtBQUs4QixFQUFFOzs7Ozs7Ozs7O2dDQWlEakIvQyxXQUFXMkIsTUFBTSxLQUFLLEtBQUssQ0FBQ3ZCLDJCQUMzQiw4REFBQ3VEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0k7NENBQUlKLFdBQVU7NENBQWtDSyxNQUFLOzRDQUFPQyxRQUFPOzRDQUFlQyxTQUFRO3NEQUN6Riw0RUFBQ0M7Z0RBQUtDLGVBQWM7Z0RBQVFDLGdCQUFlO2dEQUFRQyxhQUFhO2dEQUFHQyxHQUFFOzs7Ozs7Ozs7OztzREFFdkUsOERBQUNXOzRDQUFFdkIsV0FBVTtzREFBNkI7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNaEQsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0c7b0NBQ0M3QyxNQUFLO29DQUNMMkMsU0FBU3JCO29DQUNUbUQsVUFBVXpGLGNBQWN5QixNQUFNLEtBQUs7b0NBQ25DaUMsV0FBVTs4Q0FFVDlELFNBQVMsYUFDTiwwQkFDQUYsV0FDRSxzQkFBMkMsT0FBckJNLGNBQWN5QixNQUFNLEVBQUMsT0FDM0M7Ozs7Ozs4Q0FHUiw4REFBQ29DO29DQUNDN0MsTUFBSztvQ0FDTDJDLFNBQVNwRTtvQ0FDVG1FLFdBQVU7OENBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUWI7R0FuU01yRTtLQUFBQTtBQXFTTixpRUFBZUEsWUFBWUEsRUFBQyIsInNvdXJjZXMiOlsiL1VzZXJzL3ZpdGFsaWlsL0RvY3VtZW50cy9TZXRNZWUvc3JjL2NvbXBvbmVudHMvYWRtaW4vTWVkaWFHYWxsZXJ5LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgZ2V0TWVkaWFGaWxlcywgdXBsb2FkTWVkaWFGaWxlLCBkZWxldGVNZWRpYUZpbGUsIGdldFN0b3JhZ2VJbmZvLCBNZWRpYUZpbGUgfSBmcm9tICdAL2xpYi9tZWRpYS1zdG9yYWdlJztcblxuaW50ZXJmYWNlIE1lZGlhR2FsbGVyeVByb3BzIHtcbiAgaXNPcGVuOiBib29sZWFuO1xuICBvbkNsb3NlOiAoKSA9PiB2b2lkO1xuICBvblNlbGVjdDogKGltYWdlVXJsOiBzdHJpbmcpID0+IHZvaWQ7XG4gIHNlbGVjdGVkSW1hZ2U/OiBzdHJpbmc7XG4gIG11bHRpcGxlPzogYm9vbGVhbjtcbiAgb25TZWxlY3RNdWx0aXBsZT86IChpbWFnZVVybHM6IHN0cmluZ1tdKSA9PiB2b2lkO1xuICBtb2RlPzogJ2ZlYXR1cmVkJyB8ICdjb250ZW50JzsgLy8g0JTQvtCx0LDQstC70Y/QtdC8INGA0LXQttC40Lwg0YDQsNCx0L7RgtGLXG4gIHRpdGxlPzogc3RyaW5nOyAvLyDQmtCw0YHRgtC+0LzQvdGL0Lkg0LfQsNCz0L7Qu9C+0LLQvtC6XG59XG5cbmNvbnN0IE1lZGlhR2FsbGVyeTogUmVhY3QuRkM8TWVkaWFHYWxsZXJ5UHJvcHM+ID0gKHtcbiAgaXNPcGVuLFxuICBvbkNsb3NlLFxuICBvblNlbGVjdCxcbiAgc2VsZWN0ZWRJbWFnZSxcbiAgbXVsdGlwbGUgPSBmYWxzZSxcbiAgb25TZWxlY3RNdWx0aXBsZSxcbiAgbW9kZSA9ICdjb250ZW50JyxcbiAgdGl0bGVcbn0pID0+IHtcbiAgY29uc3QgW21lZGlhRmlsZXMsIHNldE1lZGlhRmlsZXNdID0gdXNlU3RhdGU8TWVkaWFGaWxlW10+KFtdKTtcbiAgY29uc3QgW3NlbGVjdGVkRmlsZXMsIHNldFNlbGVjdGVkRmlsZXNdID0gdXNlU3RhdGU8c3RyaW5nW10+KFtdKTtcbiAgY29uc3QgW3VwbG9hZGluZywgc2V0VXBsb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2RyYWdPdmVyLCBzZXREcmFnT3Zlcl0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoaXNPcGVuKSB7XG4gICAgICBsb2FkTWVkaWFGaWxlcygpO1xuICAgIH1cbiAgfSwgW2lzT3Blbl0pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8g0JjQvdC40YbQuNCw0LvQuNC30LjRgNGD0LXQvCDQstGL0LHRgNCw0L3QvdGL0LUg0YTQsNC50LvRiyDRgtC+0LvRjNC60L4g0LTQu9GPINGA0LXQttC40LzQsCDQv9GA0LXQstGM0Y5cbiAgICBpZiAobW9kZSA9PT0gJ2ZlYXR1cmVkJyAmJiBzZWxlY3RlZEltYWdlICYmICFtdWx0aXBsZSkge1xuICAgICAgc2V0U2VsZWN0ZWRGaWxlcyhbc2VsZWN0ZWRJbWFnZV0pO1xuICAgIH0gZWxzZSBpZiAobW9kZSA9PT0gJ2NvbnRlbnQnKSB7XG4gICAgICAvLyDQlNC70Y8g0LrQvtC90YLQtdC90YLQsCDQstGB0LXQs9C00LAg0L3QsNGH0LjQvdCw0LXQvCDRgSDQv9GD0YHRgtC+0LPQviDQstGL0LHQvtGA0LBcbiAgICAgIHNldFNlbGVjdGVkRmlsZXMoW10pO1xuICAgIH1cbiAgfSwgW3NlbGVjdGVkSW1hZ2UsIG11bHRpcGxlLCBtb2RlLCBpc09wZW5dKTtcblxuICBjb25zdCBsb2FkTWVkaWFGaWxlcyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgZmlsZXMgPSBhd2FpdCBnZXRNZWRpYUZpbGVzKCk7XG4gICAgICBzZXRNZWRpYUZpbGVzKGZpbGVzKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyBtZWRpYSBmaWxlczonLCBlcnJvcik7XG4gICAgICBzZXRNZWRpYUZpbGVzKFtdKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRmlsZVVwbG9hZCA9IGFzeW5jIChmaWxlczogRmlsZUxpc3QpID0+IHtcbiAgICBzZXRVcGxvYWRpbmcodHJ1ZSk7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgdXBsb2FkUHJvbWlzZXMgPSBBcnJheS5mcm9tKGZpbGVzKS5tYXAoYXN5bmMgKGZpbGUpID0+IHtcbiAgICAgICAgaWYgKCFmaWxlLnR5cGUuc3RhcnRzV2l0aCgnaW1hZ2UvJykpIHtcbiAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IG1lZGlhRmlsZSA9IGF3YWl0IHVwbG9hZE1lZGlhRmlsZShmaWxlKTtcbiAgICAgICAgcmV0dXJuIG1lZGlhRmlsZTtcbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCByZXN1bHRzID0gYXdhaXQgUHJvbWlzZS5hbGwodXBsb2FkUHJvbWlzZXMpO1xuICAgICAgY29uc3Qgc3VjY2Vzc2Z1bFVwbG9hZHMgPSByZXN1bHRzLmZpbHRlcihCb29sZWFuKTtcblxuICAgICAgaWYgKHN1Y2Nlc3NmdWxVcGxvYWRzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgYXdhaXQgbG9hZE1lZGlhRmlsZXMoKTtcbiAgICAgIH1cblxuICAgICAgaWYgKHN1Y2Nlc3NmdWxVcGxvYWRzLmxlbmd0aCA8IGZpbGVzLmxlbmd0aCkge1xuICAgICAgICBhbGVydChg0JfQsNCz0YDRg9C20LXQvdC+ICR7c3VjY2Vzc2Z1bFVwbG9hZHMubGVuZ3RofSDQuNC3ICR7ZmlsZXMubGVuZ3RofSDRhNCw0LnQu9C+0LIuINCd0LXQutC+0YLQvtGA0YvQtSDRhNCw0LnQu9GLINC90LUg0YPQtNCw0LvQvtGB0Ywg0LfQsNCz0YDRg9C30LjRgtGMLmApO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGxvYWRpbmcgZmlsZXM6JywgZXJyb3IpO1xuICAgICAgYWxlcnQoJ9Ce0YjQuNCx0LrQsCDQv9GA0Lgg0LfQsNCz0YDRg9C30LrQtSDRhNCw0LnQu9C+0LIuINCS0L7Qt9C80L7QttC90L4sINC90LXQtNC+0YHRgtCw0YLQvtGH0L3QviDQvNC10YHRgtCwINC00LvRjyDRhdGA0LDQvdC10L3QuNGPLicpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRVcGxvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVEcm9wID0gKGU6IFJlYWN0LkRyYWdFdmVudCkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICBzZXREcmFnT3ZlcihmYWxzZSk7XG4gICAgXG4gICAgY29uc3QgZmlsZXMgPSBlLmRhdGFUcmFuc2Zlci5maWxlcztcbiAgICBpZiAoZmlsZXMubGVuZ3RoID4gMCkge1xuICAgICAgaGFuZGxlRmlsZVVwbG9hZChmaWxlcyk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUZpbGVTZWxlY3QgPSAoZTogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IHtcbiAgICBjb25zdCBmaWxlcyA9IGUudGFyZ2V0LmZpbGVzO1xuICAgIGlmIChmaWxlcyAmJiBmaWxlcy5sZW5ndGggPiAwKSB7XG4gICAgICBoYW5kbGVGaWxlVXBsb2FkKGZpbGVzKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlSW1hZ2VTZWxlY3QgPSAoaW1hZ2VVcmw6IHN0cmluZykgPT4ge1xuICAgIGlmIChtdWx0aXBsZSkge1xuICAgICAgY29uc3QgbmV3U2VsZWN0aW9uID0gc2VsZWN0ZWRGaWxlcy5pbmNsdWRlcyhpbWFnZVVybClcbiAgICAgICAgPyBzZWxlY3RlZEZpbGVzLmZpbHRlcih1cmwgPT4gdXJsICE9PSBpbWFnZVVybClcbiAgICAgICAgOiBbLi4uc2VsZWN0ZWRGaWxlcywgaW1hZ2VVcmxdO1xuICAgICAgc2V0U2VsZWN0ZWRGaWxlcyhuZXdTZWxlY3Rpb24pO1xuICAgIH0gZWxzZSB7XG4gICAgICBzZXRTZWxlY3RlZEZpbGVzKFtpbWFnZVVybF0pO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVDb25maXJtU2VsZWN0aW9uID0gKCkgPT4ge1xuICAgIGlmIChtdWx0aXBsZSAmJiBvblNlbGVjdE11bHRpcGxlKSB7XG4gICAgICBvblNlbGVjdE11bHRpcGxlKHNlbGVjdGVkRmlsZXMpO1xuICAgIH0gZWxzZSBpZiAoc2VsZWN0ZWRGaWxlcy5sZW5ndGggPiAwKSB7XG4gICAgICBvblNlbGVjdChzZWxlY3RlZEZpbGVzWzBdKTtcbiAgICB9XG4gICAgb25DbG9zZSgpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZURlbGV0ZUZpbGUgPSAoZmlsZUlkOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoY29uZmlybSgn0KPQtNCw0LvQuNGC0Ywg0Y3RgtC+INC40LfQvtCx0YDQsNC20LXQvdC40LU/JykpIHtcbiAgICAgIGRlbGV0ZU1lZGlhRmlsZShmaWxlSWQpO1xuICAgICAgbG9hZE1lZGlhRmlsZXMoKTtcbiAgICAgIC8vIFJlbW92ZSBmcm9tIHNlbGVjdGlvbiBpZiBzZWxlY3RlZFxuICAgICAgc2V0U2VsZWN0ZWRGaWxlcyhwcmV2ID0+IHByZXYuZmlsdGVyKHVybCA9PiB7XG4gICAgICAgIGNvbnN0IGZpbGUgPSBtZWRpYUZpbGVzLmZpbmQoZiA9PiBmLmlkID09PSBmaWxlSWQpO1xuICAgICAgICByZXR1cm4gZmlsZSA/IHVybCAhPT0gZmlsZS51cmwgOiB0cnVlO1xuICAgICAgfSkpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBmb3JtYXRGaWxlU2l6ZSA9IChieXRlczogbnVtYmVyKSA9PiB7XG4gICAgaWYgKGJ5dGVzID09PSAwKSByZXR1cm4gJzAgQnl0ZXMnO1xuICAgIGNvbnN0IGsgPSAxMDI0O1xuICAgIGNvbnN0IHNpemVzID0gWydCeXRlcycsICdLQicsICdNQicsICdHQiddO1xuICAgIGNvbnN0IGkgPSBNYXRoLmZsb29yKE1hdGgubG9nKGJ5dGVzKSAvIE1hdGgubG9nKGspKTtcbiAgICByZXR1cm4gcGFyc2VGbG9hdCgoYnl0ZXMgLyBNYXRoLnBvdyhrLCBpKSkudG9GaXhlZCgyKSkgKyAnICcgKyBzaXplc1tpXTtcbiAgfTtcblxuICBpZiAoIWlzT3BlbikgcmV0dXJuIG51bGw7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgei01MCBvdmVyZmxvdy15LWF1dG9cIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWluLWgtc2NyZWVuIHB0LTQgcHgtNCBwYi0yMCB0ZXh0LWNlbnRlciBzbTpibG9jayBzbTpwLTBcIj5cbiAgICAgICAgey8qIEJhY2tncm91bmQgb3ZlcmxheSAqL31cbiAgICAgICAgPGRpdiBcbiAgICAgICAgICBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIGJnLWdyYXktNTAwIGJnLW9wYWNpdHktNzUgdHJhbnNpdGlvbi1vcGFjaXR5XCJcbiAgICAgICAgICBvbkNsaWNrPXtvbkNsb3NlfVxuICAgICAgICAvPlxuXG4gICAgICAgIHsvKiBNb2RhbCBwYW5lbCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbmxpbmUtYmxvY2sgYWxpZ24tYm90dG9tIGJnLXdoaXRlIHJvdW5kZWQtbGcgdGV4dC1sZWZ0IG92ZXJmbG93LWhpZGRlbiBzaGFkb3cteGwgdHJhbnNmb3JtIHRyYW5zaXRpb24tYWxsIHNtOm15LTggc206YWxpZ24tbWlkZGxlIHNtOm1heC13LTR4bCBzbTp3LWZ1bGxcIj5cbiAgICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcHgtNCBwdC01IHBiLTQgc206cC02IHNtOnBiLTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTRcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgIHt0aXRsZSB8fCAobW9kZSA9PT0gJ2ZlYXR1cmVkJyA/ICfQktGL0LHRgNCw0YLRjCDQuNC30L7QsdGA0LDQttC10L3QuNC1INC/0YDQtdCy0YzRjicgOiAn0JLRi9Cx0YDQsNGC0Ywg0LjQt9C+0LHRgNCw0LbQtdC90LjRjyDQtNC70Y8g0LrQvtC90YLQtdC90YLQsCcpfVxuICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17b25DbG9zZX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS02MDBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTYgaC02XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNNiAxOEwxOCA2TTYgNmwxMiAxMlwiIC8+XG4gICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBVcGxvYWQgYXJlYSAqL31cbiAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgYm9yZGVyLTIgYm9yZGVyLWRhc2hlZCByb3VuZGVkLWxnIHAtNiB0ZXh0LWNlbnRlciBtYi02IHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgZHJhZ092ZXIgPyAnYm9yZGVyLXByaW1hcnktNTAwIGJnLXByaW1hcnktNTAnIDogJ2JvcmRlci1ncmF5LTMwMCdcbiAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgIG9uRHJvcD17aGFuZGxlRHJvcH1cbiAgICAgICAgICAgICAgb25EcmFnT3Zlcj17KGUpID0+IHsgZS5wcmV2ZW50RGVmYXVsdCgpOyBzZXREcmFnT3Zlcih0cnVlKTsgfX1cbiAgICAgICAgICAgICAgb25EcmFnTGVhdmU9eygpID0+IHNldERyYWdPdmVyKGZhbHNlKX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJteC1hdXRvIGgtMTIgdy0xMiB0ZXh0LWdyYXktNDAwXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDQ4IDQ4XCI+XG4gICAgICAgICAgICAgICAgPHBhdGggZD1cIk0yOCA4SDEyYTQgNCAwIDAwLTQgNHYyMG0zMi0xMnY4bTAgMHY4YTQgNCAwIDAxLTQgNEgxMmE0IDQgMCAwMS00LTR2LTRtMzItNGwtMy4xNzItMy4xNzJhNCA0IDAgMDAtNS42NTYgMEwyOCAyOE04IDMybDkuMTcyLTkuMTcyYTQgNCAwIDAxNS42NTYgMEwyOCAyOG0wIDBsNCA0bTQtMjRoOG0tNC00djhtLTEyIDRoLjAyXCIgc3Ryb2tlV2lkdGg9ezJ9IHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiAvPlxuICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00XCI+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJmaWxlLXVwbG9hZFwiIGNsYXNzTmFtZT1cImN1cnNvci1wb2ludGVyXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtdC0yIGJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICDQn9C10YDQtdGC0LDRidC40YLQtSDQuNC30L7QsdGA0LDQttC10L3QuNGPINGB0Y7QtNCwINC40LvQuHsnICd9XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcHJpbWFyeS02MDAgaG92ZXI6dGV4dC1wcmltYXJ5LTUwMFwiPtCy0YvQsdC10YDQuNGC0LUg0YTQsNC50LvRizwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICBpZD1cImZpbGUtdXBsb2FkXCJcbiAgICAgICAgICAgICAgICAgICAgbmFtZT1cImZpbGUtdXBsb2FkXCJcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cImZpbGVcIlxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJzci1vbmx5XCJcbiAgICAgICAgICAgICAgICAgICAgbXVsdGlwbGVcbiAgICAgICAgICAgICAgICAgICAgYWNjZXB0PVwiaW1hZ2UvKlwiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVGaWxlU2VsZWN0fVxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICBQTkcsIEpQRywgR0lGINC00L4gMTBNQlxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAge3VwbG9hZGluZyAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC00IHctNCBib3JkZXItYi0yIGJvcmRlci1wcmltYXJ5LTYwMCBtci0yXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj7Ql9Cw0LPRgNGD0LfQutCwLi4uPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHsvKiBNZWRpYSBncmlkICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIHNtOmdyaWQtY29scy0zIG1kOmdyaWQtY29scy00IGxnOmdyaWQtY29scy02IGdhcC00IG1heC1oLTk2IG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICAgICAgICB7bWVkaWFGaWxlcy5tYXAoKGZpbGUpID0+IChcbiAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICBrZXk9e2ZpbGUuaWR9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2ByZWxhdGl2ZSBncm91cCBjdXJzb3ItcG9pbnRlciByb3VuZGVkLWxnIG92ZXJmbG93LWhpZGRlbiBib3JkZXItMiB0cmFuc2l0aW9uLWFsbCAke1xuICAgICAgICAgICAgICAgICAgICBzZWxlY3RlZEZpbGVzLmluY2x1ZGVzKGZpbGUudXJsKVxuICAgICAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1wcmltYXJ5LTUwMCByaW5nLTIgcmluZy1wcmltYXJ5LTIwMCdcbiAgICAgICAgICAgICAgICAgICAgICA6ICdib3JkZXItZ3JheS0yMDAgaG92ZXI6Ym9yZGVyLWdyYXktMzAwJ1xuICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVJbWFnZVNlbGVjdChmaWxlLnVybCl9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhc3BlY3Qtc3F1YXJlXCI+XG4gICAgICAgICAgICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgICAgICAgICAgICBzcmM9e2ZpbGUudXJsfVxuICAgICAgICAgICAgICAgICAgICAgIGFsdD17ZmlsZS5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgb2JqZWN0LWNvdmVyXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICB7LyogU2VsZWN0aW9uIGluZGljYXRvciAqL31cbiAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZEZpbGVzLmluY2x1ZGVzKGZpbGUudXJsKSAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTIgcmlnaHQtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy01IGgtNSBiZy1wcmltYXJ5LTYwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy0zIGgtMyB0ZXh0LXdoaXRlXCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk0xNi43MDcgNS4yOTNhMSAxIDAgMDEwIDEuNDE0bC04IDhhMSAxIDAgMDEtMS40MTQgMGwtNC00YTEgMSAwIDAxMS40MTQtMS40MTRMOCAxMi41ODZsNy4yOTMtNy4yOTNhMSAxIDAgMDExLjQxNCAwelwiIGNsaXBSdWxlPVwiZXZlbm9kZFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgICB7LyogRGVsZXRlIGJ1dHRvbiAqL31cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZURlbGV0ZUZpbGUoZmlsZS5pZCk7XG4gICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0yIGxlZnQtMiBvcGFjaXR5LTAgZ3JvdXAtaG92ZXI6b3BhY2l0eS0xMDAgdHJhbnNpdGlvbi1vcGFjaXR5IGJnLXJlZC02MDAgdGV4dC13aGl0ZSByb3VuZGVkLWZ1bGwgcC0xIGhvdmVyOmJnLXJlZC03MDBcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctMyBoLTNcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyMCAyMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGZpbGxSdWxlPVwiZXZlbm9kZFwiIGQ9XCJNNC4yOTMgNC4yOTNhMSAxIDAgMDExLjQxNCAwTDEwIDguNTg2bDQuMjkzLTQuMjkzYTEgMSAwIDExMS40MTQgMS40MTRMMTEuNDE0IDEwbDQuMjkzIDQuMjkzYTEgMSAwIDAxLTEuNDE0IDEuNDE0TDEwIDExLjQxNGwtNC4yOTMgNC4yOTNhMSAxIDAgMDEtMS40MTQtMS40MTRMOC41ODYgMTAgNC4yOTMgNS43MDdhMSAxIDAgMDEwLTEuNDE0elwiIGNsaXBSdWxlPVwiZXZlbm9kZFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBGaWxlIGluZm8gb3ZlcmxheSAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgYm90dG9tLTAgbGVmdC0wIHJpZ2h0LTAgYmctYmxhY2sgYmctb3BhY2l0eS03NSB0ZXh0LXdoaXRlIHAtMiBvcGFjaXR5LTAgZ3JvdXAtaG92ZXI6b3BhY2l0eS0xMDAgdHJhbnNpdGlvbi1vcGFjaXR5XCI+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdHJ1bmNhdGVcIj57ZmlsZS5uYW1lfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktMzAwXCI+e2Zvcm1hdEZpbGVTaXplKGZpbGUuc2l6ZSl9PC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHttZWRpYUZpbGVzLmxlbmd0aCA9PT0gMCAmJiAhdXBsb2FkaW5nICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS04XCI+XG4gICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJteC1hdXRvIGgtMTIgdy0xMiB0ZXh0LWdyYXktNDAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNNCAxNmw0LjU4Ni00LjU4NmEyIDIgMCAwMTIuODI4IDBMMTYgMTZtLTItMmwxLjU4Ni0xLjU4NmEyIDIgMCAwMTIuODI4IDBMMjAgMTRtLTYtNmguMDFNNiAyMGgxMmEyIDIgMCAwMDItMlY2YTIgMiAwIDAwLTItMkg2YTIgMiAwIDAwLTIgMnYxMmEyIDIgMCAwMDIgMnpcIiAvPlxuICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTIgdGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+0J3QtdGCINC30LDQs9GA0YPQttC10L3QvdGL0YUg0LjQt9C+0LHRgNCw0LbQtdC90LjQuTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEZvb3RlciAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktNTAgcHgtNCBweS0zIHNtOnB4LTYgc206ZmxleCBzbTpmbGV4LXJvdy1yZXZlcnNlXCI+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVDb25maXJtU2VsZWN0aW9ufVxuICAgICAgICAgICAgICBkaXNhYmxlZD17c2VsZWN0ZWRGaWxlcy5sZW5ndGggPT09IDB9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBpbmxpbmUtZmxleCBqdXN0aWZ5LWNlbnRlciByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItdHJhbnNwYXJlbnQgc2hhZG93LXNtIHB4LTQgcHktMiBiZy1wcmltYXJ5LTYwMCB0ZXh0LWJhc2UgZm9udC1tZWRpdW0gdGV4dC13aGl0ZSBob3ZlcjpiZy1wcmltYXJ5LTcwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctb2Zmc2V0LTIgZm9jdXM6cmluZy1wcmltYXJ5LTUwMCBzbTptbC0zIHNtOnctYXV0byBzbTp0ZXh0LXNtIGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge21vZGUgPT09ICdmZWF0dXJlZCdcbiAgICAgICAgICAgICAgICA/ICfQo9GB0YLQsNC90L7QstC40YLRjCDQutCw0Log0L/RgNC10LLRjNGOJ1xuICAgICAgICAgICAgICAgIDogbXVsdGlwbGVcbiAgICAgICAgICAgICAgICAgID8gYNCS0YHRgtCw0LLQuNGC0Ywg0LIg0YHRgtCw0YLRjNGOICgke3NlbGVjdGVkRmlsZXMubGVuZ3RofSlgXG4gICAgICAgICAgICAgICAgICA6ICfQktGB0YLQsNCy0LjRgtGMINCyINGB0YLQsNGC0YzRjidcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICBvbkNsaWNrPXtvbkNsb3NlfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC0zIHctZnVsbCBpbmxpbmUtZmxleCBqdXN0aWZ5LWNlbnRlciByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItZ3JheS0zMDAgc2hhZG93LXNtIHB4LTQgcHktMiBiZy13aGl0ZSB0ZXh0LWJhc2UgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTUwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1vZmZzZXQtMiBmb2N1czpyaW5nLXByaW1hcnktNTAwIHNtOm10LTAgc206bWwtMyBzbTp3LWF1dG8gc206dGV4dC1zbVwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgINCe0YLQvNC10L3QsFxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBNZWRpYUdhbGxlcnk7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsImdldE1lZGlhRmlsZXMiLCJ1cGxvYWRNZWRpYUZpbGUiLCJkZWxldGVNZWRpYUZpbGUiLCJNZWRpYUdhbGxlcnkiLCJpc09wZW4iLCJvbkNsb3NlIiwib25TZWxlY3QiLCJzZWxlY3RlZEltYWdlIiwibXVsdGlwbGUiLCJvblNlbGVjdE11bHRpcGxlIiwibW9kZSIsInRpdGxlIiwibWVkaWFGaWxlcyIsInNldE1lZGlhRmlsZXMiLCJzZWxlY3RlZEZpbGVzIiwic2V0U2VsZWN0ZWRGaWxlcyIsInVwbG9hZGluZyIsInNldFVwbG9hZGluZyIsImRyYWdPdmVyIiwic2V0RHJhZ092ZXIiLCJsb2FkTWVkaWFGaWxlcyIsImZpbGVzIiwiZXJyb3IiLCJjb25zb2xlIiwiaGFuZGxlRmlsZVVwbG9hZCIsInVwbG9hZFByb21pc2VzIiwiQXJyYXkiLCJmcm9tIiwibWFwIiwiZmlsZSIsInR5cGUiLCJzdGFydHNXaXRoIiwibWVkaWFGaWxlIiwicmVzdWx0cyIsIlByb21pc2UiLCJhbGwiLCJzdWNjZXNzZnVsVXBsb2FkcyIsImZpbHRlciIsIkJvb2xlYW4iLCJsZW5ndGgiLCJhbGVydCIsImhhbmRsZURyb3AiLCJlIiwicHJldmVudERlZmF1bHQiLCJkYXRhVHJhbnNmZXIiLCJoYW5kbGVGaWxlU2VsZWN0IiwidGFyZ2V0IiwiaGFuZGxlSW1hZ2VTZWxlY3QiLCJpbWFnZVVybCIsIm5ld1NlbGVjdGlvbiIsImluY2x1ZGVzIiwidXJsIiwiaGFuZGxlQ29uZmlybVNlbGVjdGlvbiIsImhhbmRsZURlbGV0ZUZpbGUiLCJmaWxlSWQiLCJjb25maXJtIiwicHJldiIsImZpbmQiLCJmIiwiaWQiLCJmb3JtYXRGaWxlU2l6ZSIsImJ5dGVzIiwiayIsInNpemVzIiwiaSIsIk1hdGgiLCJmbG9vciIsImxvZyIsInBhcnNlRmxvYXQiLCJwb3ciLCJ0b0ZpeGVkIiwiZGl2IiwiY2xhc3NOYW1lIiwib25DbGljayIsImgzIiwiYnV0dG9uIiwic3ZnIiwiZmlsbCIsInN0cm9rZSIsInZpZXdCb3giLCJwYXRoIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwic3Ryb2tlV2lkdGgiLCJkIiwib25Ecm9wIiwib25EcmFnT3ZlciIsIm9uRHJhZ0xlYXZlIiwibGFiZWwiLCJodG1sRm9yIiwic3BhbiIsImlucHV0IiwibmFtZSIsImFjY2VwdCIsIm9uQ2hhbmdlIiwicCIsImltZyIsInNyYyIsImFsdCIsImZpbGxSdWxlIiwiY2xpcFJ1bGUiLCJzdG9wUHJvcGFnYXRpb24iLCJzaXplIiwiZGlzYWJsZWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/MediaGallery.tsx\n"));

/***/ })

});