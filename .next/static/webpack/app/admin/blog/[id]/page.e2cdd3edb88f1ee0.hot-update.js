"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/blog/[id]/page",{

/***/ "(app-pages-browser)/./src/components/admin/RichTextEditor.tsx":
/*!*************************************************!*\
  !*** ./src/components/admin/RichTextEditor.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst RichTextEditor = (param)=>{\n    let { value, onChange, onImageInsert, placeholder = \"Начните писать...\", className = \"\" } = param;\n    _s();\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [isToolbarSticky, setIsToolbarSticky] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RichTextEditor.useEffect\": ()=>{\n            // Convert markdown to HTML for display\n            if (editorRef.current && value !== editorRef.current.innerHTML) {\n                editorRef.current.innerHTML = markdownToHtml(value);\n            }\n        }\n    }[\"RichTextEditor.useEffect\"], [\n        value\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RichTextEditor.useEffect\": ()=>{\n            // Set up global function for image insertion\n            window.insertImageIntoEditor = ({\n                \"RichTextEditor.useEffect\": (imageUrl)=>{\n                    if (editorRef.current) {\n                        const img = document.createElement('img');\n                        img.src = imageUrl;\n                        img.alt = 'Изображение';\n                        img.style.maxWidth = '100%';\n                        img.style.height = 'auto';\n                        img.style.margin = '10px 0';\n                        img.style.borderRadius = '0.5rem';\n                        // Insert at current cursor position or at the end\n                        const selection = window.getSelection();\n                        if (selection && selection.rangeCount > 0) {\n                            const range = selection.getRangeAt(0);\n                            range.insertNode(img);\n                            range.setStartAfter(img);\n                            range.setEndAfter(img);\n                            selection.removeAllRanges();\n                            selection.addRange(range);\n                        } else {\n                            editorRef.current.appendChild(img);\n                        }\n                        handleInput();\n                        editorRef.current.focus();\n                    }\n                }\n            })[\"RichTextEditor.useEffect\"];\n            return ({\n                \"RichTextEditor.useEffect\": ()=>{\n                    delete window.insertImageIntoEditor;\n                }\n            })[\"RichTextEditor.useEffect\"];\n        }\n    }[\"RichTextEditor.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RichTextEditor.useEffect\": ()=>{\n            const handleScroll = {\n                \"RichTextEditor.useEffect.handleScroll\": ()=>{\n                    if (editorRef.current) {\n                        const rect = editorRef.current.getBoundingClientRect();\n                        setIsToolbarSticky(rect.top <= 100);\n                    }\n                }\n            }[\"RichTextEditor.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"RichTextEditor.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"RichTextEditor.useEffect\"];\n        }\n    }[\"RichTextEditor.useEffect\"], []);\n    const markdownToHtml = (markdown)=>{\n        return markdown.replace(/^### (.*$)/gim, '<h3>$1</h3>').replace(/^## (.*$)/gim, '<h2>$1</h2>').replace(/^# (.*$)/gim, '<h1>$1</h1>').replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>').replace(/\\*(.*?)\\*/g, '<em>$1</em>').replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, '<img src=\"$2\" alt=\"$1\" style=\"max-width: 100%; height: auto; margin: 10px 0;\" />').replace(/\\n\\n/g, '</p><p>').replace(/^(.+)$/gm, '<p>$1</p>').replace(/<p><\\/p>/g, '').replace(/<p>(<h[1-6]>.*<\\/h[1-6]>)<\\/p>/g, '$1').replace(/<p>(<img[^>]*>)<\\/p>/g, '<div style=\"text-align: center; margin: 20px 0;\">$1</div>');\n    };\n    const htmlToMarkdown = (html)=>{\n        return html.replace(/<h1>(.*?)<\\/h1>/g, '# $1\\n\\n').replace(/<h2>(.*?)<\\/h2>/g, '## $1\\n\\n').replace(/<h3>(.*?)<\\/h3>/g, '### $1\\n\\n').replace(/<strong>(.*?)<\\/strong>/g, '**$1**').replace(/<em>(.*?)<\\/em>/g, '*$1*').replace(/<div[^>]*><img src=\"([^\"]*)\" alt=\"([^\"]*)\"[^>]*><\\/div>/g, '![${2}](${1})\\n\\n').replace(/<img src=\"([^\"]*)\" alt=\"([^\"]*)\"[^>]*>/g, '![${2}](${1})\\n\\n').replace(/<p>(.*?)<\\/p>/g, '$1\\n\\n').replace(/<br\\s*\\/?>/g, '\\n').replace(/&nbsp;/g, ' ').replace(/\\n{3,}/g, '\\n\\n').trim();\n    };\n    const handleInput = ()=>{\n        if (editorRef.current) {\n            const html = editorRef.current.innerHTML;\n            const markdown = htmlToMarkdown(html);\n            onChange(markdown);\n        }\n    };\n    const execCommand = (command, value)=>{\n        var _editorRef_current;\n        // eslint-disable-next-line deprecation/deprecation\n        document.execCommand(command, false, value);\n        handleInput();\n        (_editorRef_current = editorRef.current) === null || _editorRef_current === void 0 ? void 0 : _editorRef_current.focus();\n    };\n    const insertHeading = (level)=>{\n        const selection = window.getSelection();\n        if (selection && selection.rangeCount > 0) {\n            const range = selection.getRangeAt(0);\n            const selectedText = range.toString() || 'Заголовок';\n            const heading = document.createElement(\"h\".concat(level));\n            heading.textContent = selectedText;\n            range.deleteContents();\n            range.insertNode(heading);\n            // Move cursor after heading\n            range.setStartAfter(heading);\n            range.setEndAfter(heading);\n            selection.removeAllRanges();\n            selection.addRange(range);\n            handleInput();\n        }\n    };\n    const insertImage = ()=>{\n        if (onImageInsert) {\n            onImageInsert();\n        }\n    };\n    const insertQuote = ()=>{\n        const selection = window.getSelection();\n        if (selection && selection.rangeCount > 0) {\n            const range = selection.getRangeAt(0);\n            const selectedText = range.toString() || 'Цитата';\n            const blockquote = document.createElement('blockquote');\n            blockquote.style.borderLeft = '4px solid #e5e7eb';\n            blockquote.style.paddingLeft = '1rem';\n            blockquote.style.margin = '1rem 0';\n            blockquote.style.fontStyle = 'italic';\n            blockquote.style.color = '#6b7280';\n            blockquote.textContent = selectedText;\n            range.deleteContents();\n            range.insertNode(blockquote);\n            range.setStartAfter(blockquote);\n            range.setEndAfter(blockquote);\n            selection.removeAllRanges();\n            selection.addRange(range);\n            handleInput();\n        }\n    };\n    const insertCode = ()=>{\n        const selection = window.getSelection();\n        if (selection && selection.rangeCount > 0) {\n            const range = selection.getRangeAt(0);\n            const selectedText = range.toString() || 'код';\n            const code = document.createElement('code');\n            code.style.backgroundColor = '#f3f4f6';\n            code.style.padding = '0.25rem 0.5rem';\n            code.style.borderRadius = '0.25rem';\n            code.style.fontFamily = 'monospace';\n            code.style.fontSize = '0.875rem';\n            code.textContent = selectedText;\n            range.deleteContents();\n            range.insertNode(code);\n            range.setStartAfter(code);\n            range.setEndAfter(code);\n            selection.removeAllRanges();\n            selection.addRange(range);\n            handleInput();\n        }\n    };\n    const insertLink = ()=>{\n        const url = prompt('Введите URL ссылки:');\n        if (url) {\n            execCommand('createLink', url);\n        }\n    };\n    const toolbarButtons = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M6 2l3 6H5l3 6\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 220,\n                columnNumber: 9\n            }, undefined),\n            title: 'Заголовок 1',\n            action: ()=>insertHeading(1)\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M6 4l3 4H5l3 4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 229,\n                columnNumber: 9\n            }, undefined),\n            title: 'Заголовок 2',\n            action: ()=>insertHeading(2)\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M6 6l3 2H5l3 2\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 238,\n                columnNumber: 9\n            }, undefined),\n            title: 'Заголовок 3',\n            action: ()=>insertHeading(3)\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                children: \"B\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 247,\n                columnNumber: 13\n            }, undefined),\n            title: 'Жирный',\n            action: ()=>execCommand('bold')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                children: \"I\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 252,\n                columnNumber: 13\n            }, undefined),\n            title: 'Курсив',\n            action: ()=>execCommand('italic')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"u\", {\n                children: \"U\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 257,\n                columnNumber: 13\n            }, undefined),\n            title: 'Подчеркнутый',\n            action: ()=>execCommand('underline')\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M4 6h16M4 10h16M4 14h16M4 18h16\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 264,\n                columnNumber: 9\n            }, undefined),\n            title: 'Список',\n            action: ()=>execCommand('insertUnorderedList')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M7 20l4-16m2 16l4-16M6 9h14M4 15h14\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 273,\n                columnNumber: 9\n            }, undefined),\n            title: 'Нумерованный список',\n            action: ()=>execCommand('insertOrderedList')\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v6a2 2 0 01-2 2h-2l-4 4z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 283,\n                columnNumber: 9\n            }, undefined),\n            title: 'Цитата',\n            action: insertQuote\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 292,\n                columnNumber: 9\n            }, undefined),\n            title: 'Код',\n            action: insertCode\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 302,\n                columnNumber: 9\n            }, undefined),\n            title: 'Ссылка',\n            action: insertLink\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 311,\n                columnNumber: 9\n            }, undefined),\n            title: 'Изображение',\n            action: insertImage\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M7 16l-4-4m0 0l4-4m-4 4h18\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 321,\n                columnNumber: 9\n            }, undefined),\n            title: 'Отменить',\n            action: ()=>execCommand('undo')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M17 8l4 4m0 0l-4 4m4-4H3\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 331,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 330,\n                columnNumber: 9\n            }, undefined),\n            title: 'Повторить',\n            action: ()=>execCommand('redo')\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-378f0ccdb29f75a1\" + \" \" + \"border border-gray-300 rounded-lg overflow-hidden \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-378f0ccdb29f75a1\" + \" \" + \"bg-gray-50 border-b border-gray-300 p-2 \".concat(isToolbarSticky ? 'sticky top-0 z-10 shadow-md' : ''),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-378f0ccdb29f75a1\" + \" \" + \"flex flex-wrap items-center gap-1\",\n                    children: toolbarButtons.map((button, index)=>{\n                        if (button.type === 'separator') {\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-378f0ccdb29f75a1\" + \" \" + \"w-px h-6 bg-gray-300 mx-1\"\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 22\n                            }, undefined);\n                        }\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: button.action,\n                            title: button.title,\n                            className: \"jsx-378f0ccdb29f75a1\" + \" \" + \"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors\",\n                            children: button.icon\n                        }, index, false, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 342,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: editorRef,\n                contentEditable: true,\n                onInput: handleInput,\n                onPaste: ()=>{\n                    // Handle paste to maintain formatting\n                    setTimeout(handleInput, 0);\n                },\n                style: {\n                    lineHeight: '1.6'\n                },\n                suppressContentEditableWarning: true,\n                \"data-placeholder\": placeholder,\n                className: \"jsx-378f0ccdb29f75a1\" + \" \" + \"min-h-[400px] p-4 focus:outline-none prose prose-lg max-w-none\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 365,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"378f0ccdb29f75a1\",\n                children: '[contenteditable].jsx-378f0ccdb29f75a1:empty:before{content:attr(data-placeholder);color:#9ca3af;pointer-events:none}[contenteditable].jsx-378f0ccdb29f75a1 h1.jsx-378f0ccdb29f75a1{font-size:2rem;font-weight:bold;margin:1rem 0;line-height:1.2}[contenteditable].jsx-378f0ccdb29f75a1 h2.jsx-378f0ccdb29f75a1{font-size:1.5rem;font-weight:bold;margin:1rem 0;line-height:1.3}[contenteditable].jsx-378f0ccdb29f75a1 h3.jsx-378f0ccdb29f75a1{font-size:1.25rem;font-weight:bold;margin:1rem 0;line-height:1.4}[contenteditable].jsx-378f0ccdb29f75a1 p.jsx-378f0ccdb29f75a1{margin:.5rem 0;line-height:1.6}[contenteditable].jsx-378f0ccdb29f75a1 ul.jsx-378f0ccdb29f75a1,[contenteditable].jsx-378f0ccdb29f75a1 ol.jsx-378f0ccdb29f75a1{margin:1rem 0;padding-left:2rem}[contenteditable].jsx-378f0ccdb29f75a1 li.jsx-378f0ccdb29f75a1{margin:.25rem 0}[contenteditable].jsx-378f0ccdb29f75a1 img.jsx-378f0ccdb29f75a1{max-width:100%;height:auto;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;margin:1rem 0}[contenteditable].jsx-378f0ccdb29f75a1 a.jsx-378f0ccdb29f75a1{color:#3b82f6;text-decoration:underline}[contenteditable].jsx-378f0ccdb29f75a1 a.jsx-378f0ccdb29f75a1:hover{color:#1d4ed8}[contenteditable].jsx-378f0ccdb29f75a1 blockquote.jsx-378f0ccdb29f75a1{border-left:4px solid#e5e7eb;padding-left:1rem;margin:1rem 0;font-style:italic;color:#6b7280;background-color:#f9fafb;padding:1rem;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem}[contenteditable].jsx-378f0ccdb29f75a1 code.jsx-378f0ccdb29f75a1{background-color:#f3f4f6;padding:.25rem .5rem;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;font-family:\"Monaco\",\"Menlo\",\"Ubuntu Mono\",monospace;font-size:.875rem;color:#1f2937}[contenteditable].jsx-378f0ccdb29f75a1 pre.jsx-378f0ccdb29f75a1{background-color:#1f2937;color:#f9fafb;padding:1rem;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;overflow-x:auto;margin:1rem 0}[contenteditable].jsx-378f0ccdb29f75a1 pre.jsx-378f0ccdb29f75a1 code.jsx-378f0ccdb29f75a1{background-color:transparent;padding:0;color:inherit}'\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n        lineNumber: 340,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RichTextEditor, \"pnr2pskr+HJRoIWqvsH/WvBtvkk=\");\n_c = RichTextEditor;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RichTextEditor);\nvar _c;\n$RefreshReg$(_c, \"RichTextEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/RichTextEditor.tsx\n"));

/***/ })

});