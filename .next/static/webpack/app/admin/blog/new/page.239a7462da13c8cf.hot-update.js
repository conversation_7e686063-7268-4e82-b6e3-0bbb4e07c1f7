"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/blog/new/page",{

/***/ "(app-pages-browser)/./src/components/admin/MediaGallery.tsx":
/*!***********************************************!*\
  !*** ./src/components/admin/MediaGallery.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_media_storage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/media-storage */ \"(app-pages-browser)/./src/lib/media-storage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst MediaGallery = (param)=>{\n    let { isOpen, onClose, onSelect, selectedImage, multiple = false, onSelectMultiple, mode = 'content', title } = param;\n    _s();\n    const [mediaFiles, setMediaFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedFiles, setSelectedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOver, setDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaGallery.useEffect\": ()=>{\n            if (isOpen) {\n                loadMediaFiles();\n            }\n        }\n    }[\"MediaGallery.useEffect\"], [\n        isOpen\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaGallery.useEffect\": ()=>{\n            // Инициализируем выбранные файлы только для режима превью\n            if (mode === 'featured' && selectedImage && !multiple) {\n                setSelectedFiles([\n                    selectedImage\n                ]);\n            } else if (mode === 'content') {\n                // Для контента всегда начинаем с пустого выбора\n                setSelectedFiles([]);\n            }\n        }\n    }[\"MediaGallery.useEffect\"], [\n        selectedImage,\n        multiple,\n        mode,\n        isOpen\n    ]);\n    const loadMediaFiles = async ()=>{\n        try {\n            const files = await (0,_lib_media_storage__WEBPACK_IMPORTED_MODULE_2__.getMediaFiles)();\n            setMediaFiles(files);\n        } catch (error) {\n            console.error('Error loading media files:', error);\n            setMediaFiles([]);\n        }\n    };\n    const handleFileUpload = async (files)=>{\n        setUploading(true);\n        try {\n            const uploadPromises = Array.from(files).map(async (file)=>{\n                if (!file.type.startsWith('image/')) {\n                    return null;\n                }\n                const mediaFile = await (0,_lib_media_storage__WEBPACK_IMPORTED_MODULE_2__.uploadMediaFile)(file);\n                return mediaFile;\n            });\n            const results = await Promise.all(uploadPromises);\n            const successfulUploads = results.filter(Boolean);\n            if (successfulUploads.length > 0) {\n                await loadMediaFiles();\n            }\n            if (successfulUploads.length < files.length) {\n                alert(\"Загружено \".concat(successfulUploads.length, \" из \").concat(files.length, \" файлов. Некоторые файлы не удалось загрузить.\"));\n            }\n        } catch (error) {\n            console.error('Error uploading files:', error);\n            alert('Ошибка при загрузке файлов. Возможно, недостаточно места для хранения.');\n        } finally{\n            setUploading(false);\n        }\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        setDragOver(false);\n        const files = e.dataTransfer.files;\n        if (files.length > 0) {\n            handleFileUpload(files);\n        }\n    };\n    const handleFileSelect = (e)=>{\n        const files = e.target.files;\n        if (files && files.length > 0) {\n            handleFileUpload(files);\n        }\n    };\n    const handleImageSelect = (imageUrl)=>{\n        if (multiple) {\n            const newSelection = selectedFiles.includes(imageUrl) ? selectedFiles.filter((url)=>url !== imageUrl) : [\n                ...selectedFiles,\n                imageUrl\n            ];\n            setSelectedFiles(newSelection);\n        } else {\n            setSelectedFiles([\n                imageUrl\n            ]);\n        }\n    };\n    const handleConfirmSelection = ()=>{\n        if (multiple && onSelectMultiple) {\n            onSelectMultiple(selectedFiles);\n        } else if (selectedFiles.length > 0) {\n            onSelect(selectedFiles[0]);\n        }\n        onClose();\n    };\n    const handleDeleteFile = async (fileId)=>{\n        if (confirm('Удалить это изображение?')) {\n            try {\n                await (0,_lib_media_storage__WEBPACK_IMPORTED_MODULE_2__.deleteMediaFile)(fileId);\n                await loadMediaFiles();\n                // Remove from selection if selected\n                setSelectedFiles((prev)=>prev.filter((url)=>{\n                        const file = mediaFiles.find((f)=>f.id === fileId);\n                        return file ? url !== file.url : true;\n                    }));\n            } catch (error) {\n                console.error('Error deleting file:', error);\n                alert('Ошибка при удалении файла');\n            }\n        }\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: title || (mode === 'featured' ? 'Выбрать изображение превью' : 'Выбрать изображения для контента')\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onClose,\n                                            className: \"text-gray-400 hover:text-gray-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M6 18L18 6M6 6l12 12\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-2 border-dashed rounded-lg p-6 text-center mb-6 transition-colors \".concat(dragOver ? 'border-primary-500 bg-primary-50' : 'border-gray-300'),\n                                    onDrop: handleDrop,\n                                    onDragOver: (e)=>{\n                                        e.preventDefault();\n                                        setDragOver(true);\n                                    },\n                                    onDragLeave: ()=>setDragOver(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"mx-auto h-12 w-12 text-gray-400\",\n                                            stroke: \"currentColor\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 48 48\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\",\n                                                strokeWidth: 2,\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"file-upload\",\n                                                    className: \"cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"mt-2 block text-sm font-medium text-gray-900\",\n                                                            children: [\n                                                                \"Перетащите изображения сюда или\",\n                                                                ' ',\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-primary-600 hover:text-primary-500\",\n                                                                    children: \"выберите файлы\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                                    lineNumber: 196,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"file-upload\",\n                                                            name: \"file-upload\",\n                                                            type: \"file\",\n                                                            className: \"sr-only\",\n                                                            multiple: true,\n                                                            accept: \"image/*\",\n                                                            onChange: handleFileSelect\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-xs text-gray-500\",\n                                                    children: \"PNG, JPG, GIF до 10MB\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, undefined),\n                                uploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Загрузка...\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 max-h-96 overflow-y-auto\",\n                                    children: mediaFiles.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all \".concat(selectedFiles.includes(file.url) ? 'border-primary-500 ring-2 ring-primary-200' : 'border-gray-200 hover:border-gray-300'),\n                                            onClick: ()=>handleImageSelect(file.url),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-square\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: file.url,\n                                                        alt: file.name,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                selectedFiles.includes(file.url) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-2 right-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-5 h-5 bg-primary-600 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-3 h-3 text-white\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        handleDeleteFile(file.id);\n                                                    },\n                                                    className: \"absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity bg-red-600 text-white rounded-full p-1 hover:bg-red-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white p-2 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs truncate\",\n                                                            children: file.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-300\",\n                                                            children: formatFileSize(file.size)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, file.id, true, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, undefined),\n                                mediaFiles.length === 0 && !uploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"mx-auto h-12 w-12 text-gray-400\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-sm text-gray-500\",\n                                            children: \"Нет загруженных изображений\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleConfirmSelection,\n                                    disabled: selectedFiles.length === 0,\n                                    className: \"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: mode === 'featured' ? 'Установить как превью' : multiple ? \"Вставить в статью (\".concat(selectedFiles.length, \")\") : 'Вставить в статью'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm\",\n                                    children: \"Отмена\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n            lineNumber: 155,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/MediaGallery.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MediaGallery, \"WU8LBYv1uTx3EGcVj6Eta3JMTIc=\");\n_c = MediaGallery;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MediaGallery);\nvar _c;\n$RefreshReg$(_c, \"MediaGallery\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/MediaGallery.tsx\n"));

/***/ })

});