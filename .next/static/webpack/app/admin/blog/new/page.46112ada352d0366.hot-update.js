"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/blog/new/page",{

/***/ "(app-pages-browser)/./src/components/admin/RichTextEditor.tsx":
/*!*************************************************!*\
  !*** ./src/components/admin/RichTextEditor.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst RichTextEditor = (param)=>{\n    let { value, onChange, onImageInsert, placeholder = \"Начните писать...\", className = \"\", insertImageUrl, onCursorPositionChange } = param;\n    _s();\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [isToolbarSticky, setIsToolbarSticky] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [cursorPosition, setCursorPosition] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RichTextEditor.useEffect\": ()=>{\n            // Convert markdown to HTML for display\n            if (editorRef.current && value !== editorRef.current.innerHTML) {\n                editorRef.current.innerHTML = markdownToHtml(value);\n            }\n        }\n    }[\"RichTextEditor.useEffect\"], [\n        value\n    ]);\n    // Функция для вставки изображения\n    const insertImageIntoEditor = (imageUrl)=>{\n        if (editorRef.current) {\n            // Фокусируемся на редакторе\n            editorRef.current.focus();\n            // Создаем изображение\n            const img = document.createElement('img');\n            img.src = imageUrl;\n            img.alt = 'Изображение';\n            img.style.maxWidth = '100%';\n            img.style.height = 'auto';\n            img.style.margin = '10px 0';\n            img.style.borderRadius = '0.5rem';\n            // Получаем текущую позицию курсора\n            const selection = window.getSelection();\n            if (selection && selection.rangeCount > 0) {\n                const range = selection.getRangeAt(0);\n                // Вставляем изображение в позицию курсора\n                range.deleteContents(); // Удаляем выделенный текст, если есть\n                range.insertNode(img);\n                // Добавляем перенос строки после изображения\n                const br = document.createElement('br');\n                range.setStartAfter(img);\n                range.insertNode(br);\n                // Устанавливаем курсор после изображения\n                range.setStartAfter(br);\n                range.setEndAfter(br);\n                selection.removeAllRanges();\n                selection.addRange(range);\n            } else {\n                // Если нет выделения, добавляем в конец\n                editorRef.current.appendChild(img);\n                const br = document.createElement('br');\n                editorRef.current.appendChild(br);\n            }\n            // Обновляем содержимое\n            handleInput();\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RichTextEditor.useEffect\": ()=>{\n            // Set up global function for image insertion\n            window.insertImageIntoEditor = insertImageIntoEditor;\n            return ({\n                \"RichTextEditor.useEffect\": ()=>{\n                    delete window.insertImageIntoEditor;\n                }\n            })[\"RichTextEditor.useEffect\"];\n        }\n    }[\"RichTextEditor.useEffect\"], []);\n    // Обработка вставки изображения через prop\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RichTextEditor.useEffect\": ()=>{\n            if (insertImageUrl) {\n                insertImageIntoEditor(insertImageUrl);\n            }\n        }\n    }[\"RichTextEditor.useEffect\"], [\n        insertImageUrl\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RichTextEditor.useEffect\": ()=>{\n            const handleScroll = {\n                \"RichTextEditor.useEffect.handleScroll\": ()=>{\n                    if (editorRef.current) {\n                        const rect = editorRef.current.getBoundingClientRect();\n                        setIsToolbarSticky(rect.top <= 100);\n                    }\n                }\n            }[\"RichTextEditor.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"RichTextEditor.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"RichTextEditor.useEffect\"];\n        }\n    }[\"RichTextEditor.useEffect\"], []);\n    const markdownToHtml = (markdown)=>{\n        return markdown.replace(/^### (.*$)/gim, '<h3>$1</h3>').replace(/^## (.*$)/gim, '<h2>$1</h2>').replace(/^# (.*$)/gim, '<h1>$1</h1>').replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>').replace(/\\*(.*?)\\*/g, '<em>$1</em>').replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, '<img src=\"$2\" alt=\"$1\" style=\"max-width: 100%; height: auto; margin: 10px 0;\" />').replace(/\\n\\n/g, '</p><p>').replace(/^(.+)$/gm, '<p>$1</p>').replace(/<p><\\/p>/g, '').replace(/<p>(<h[1-6]>.*<\\/h[1-6]>)<\\/p>/g, '$1').replace(/<p>(<img[^>]*>)<\\/p>/g, '<div style=\"text-align: center; margin: 20px 0;\">$1</div>');\n    };\n    const htmlToMarkdown = (html)=>{\n        return html.replace(/<h1>(.*?)<\\/h1>/g, '# $1\\n\\n').replace(/<h2>(.*?)<\\/h2>/g, '## $1\\n\\n').replace(/<h3>(.*?)<\\/h3>/g, '### $1\\n\\n').replace(/<strong>(.*?)<\\/strong>/g, '**$1**').replace(/<em>(.*?)<\\/em>/g, '*$1*').replace(/<div[^>]*><img src=\"([^\"]*)\" alt=\"([^\"]*)\"[^>]*><\\/div>/g, '![${2}](${1})\\n\\n').replace(/<img src=\"([^\"]*)\" alt=\"([^\"]*)\"[^>]*>/g, '![${2}](${1})\\n\\n').replace(/<p>(.*?)<\\/p>/g, '$1\\n\\n').replace(/<br\\s*\\/?>/g, '\\n').replace(/&nbsp;/g, ' ').replace(/\\n{3,}/g, '\\n\\n').trim();\n    };\n    const handleInput = ()=>{\n        if (editorRef.current) {\n            const html = editorRef.current.innerHTML;\n            const markdown = htmlToMarkdown(html);\n            onChange(markdown);\n        }\n    };\n    const execCommand = (command, value)=>{\n        var _editorRef_current;\n        // eslint-disable-next-line deprecation/deprecation\n        document.execCommand(command, false, value);\n        handleInput();\n        (_editorRef_current = editorRef.current) === null || _editorRef_current === void 0 ? void 0 : _editorRef_current.focus();\n    };\n    const insertHeading = (level)=>{\n        const selection = window.getSelection();\n        if (selection && selection.rangeCount > 0) {\n            const range = selection.getRangeAt(0);\n            const selectedText = range.toString() || 'Заголовок';\n            const heading = document.createElement(\"h\".concat(level));\n            heading.textContent = selectedText;\n            range.deleteContents();\n            range.insertNode(heading);\n            // Move cursor after heading\n            range.setStartAfter(heading);\n            range.setEndAfter(heading);\n            selection.removeAllRanges();\n            selection.addRange(range);\n            handleInput();\n        }\n    };\n    const insertImage = ()=>{\n        if (onImageInsert) {\n            onImageInsert();\n        }\n    };\n    const insertQuote = ()=>{\n        const selection = window.getSelection();\n        if (selection && selection.rangeCount > 0) {\n            const range = selection.getRangeAt(0);\n            const selectedText = range.toString() || 'Цитата';\n            const blockquote = document.createElement('blockquote');\n            blockquote.style.borderLeft = '4px solid #e5e7eb';\n            blockquote.style.paddingLeft = '1rem';\n            blockquote.style.margin = '1rem 0';\n            blockquote.style.fontStyle = 'italic';\n            blockquote.style.color = '#6b7280';\n            blockquote.textContent = selectedText;\n            range.deleteContents();\n            range.insertNode(blockquote);\n            range.setStartAfter(blockquote);\n            range.setEndAfter(blockquote);\n            selection.removeAllRanges();\n            selection.addRange(range);\n            handleInput();\n        }\n    };\n    const insertCode = ()=>{\n        const selection = window.getSelection();\n        if (selection && selection.rangeCount > 0) {\n            const range = selection.getRangeAt(0);\n            const selectedText = range.toString() || 'код';\n            const code = document.createElement('code');\n            code.style.backgroundColor = '#f3f4f6';\n            code.style.padding = '0.25rem 0.5rem';\n            code.style.borderRadius = '0.25rem';\n            code.style.fontFamily = 'monospace';\n            code.style.fontSize = '0.875rem';\n            code.textContent = selectedText;\n            range.deleteContents();\n            range.insertNode(code);\n            range.setStartAfter(code);\n            range.setEndAfter(code);\n            selection.removeAllRanges();\n            selection.addRange(range);\n            handleInput();\n        }\n    };\n    const insertLink = ()=>{\n        const url = prompt('Введите URL ссылки:');\n        if (url) {\n            execCommand('createLink', url);\n        }\n    };\n    const toolbarButtons = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M6 2l3 6H5l3 6\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 252,\n                columnNumber: 9\n            }, undefined),\n            title: 'Заголовок 1',\n            action: ()=>insertHeading(1)\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M6 4l3 4H5l3 4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 261,\n                columnNumber: 9\n            }, undefined),\n            title: 'Заголовок 2',\n            action: ()=>insertHeading(2)\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M6 6l3 2H5l3 2\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 270,\n                columnNumber: 9\n            }, undefined),\n            title: 'Заголовок 3',\n            action: ()=>insertHeading(3)\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                children: \"B\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 279,\n                columnNumber: 13\n            }, undefined),\n            title: 'Жирный',\n            action: ()=>execCommand('bold')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                children: \"I\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 284,\n                columnNumber: 13\n            }, undefined),\n            title: 'Курсив',\n            action: ()=>execCommand('italic')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"u\", {\n                children: \"U\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 289,\n                columnNumber: 13\n            }, undefined),\n            title: 'Подчеркнутый',\n            action: ()=>execCommand('underline')\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M4 6h16M4 12h8m-8 6h16\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 296,\n                columnNumber: 9\n            }, undefined),\n            title: 'По левому краю',\n            action: ()=>execCommand('justifyLeft')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M4 6h16M4 12h16M4 18h16\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 305,\n                columnNumber: 9\n            }, undefined),\n            title: 'По центру',\n            action: ()=>execCommand('justifyCenter')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M4 6h16M4 12h16M4 18h12\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 315,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 314,\n                columnNumber: 9\n            }, undefined),\n            title: 'По правому краю',\n            action: ()=>execCommand('justifyRight')\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M4 6h16M4 10h16M4 14h16M4 18h16\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 325,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 324,\n                columnNumber: 9\n            }, undefined),\n            title: 'Список',\n            action: ()=>execCommand('insertUnorderedList')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M7 20l4-16m2 16l4-16M6 9h14M4 15h14\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 333,\n                columnNumber: 9\n            }, undefined),\n            title: 'Нумерованный список',\n            action: ()=>execCommand('insertOrderedList')\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v6a2 2 0 01-2 2h-2l-4 4z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 344,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 343,\n                columnNumber: 9\n            }, undefined),\n            title: 'Цитата',\n            action: insertQuote\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 353,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 352,\n                columnNumber: 9\n            }, undefined),\n            title: 'Код',\n            action: insertCode\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 362,\n                columnNumber: 9\n            }, undefined),\n            title: 'Ссылка',\n            action: insertLink\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 372,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 371,\n                columnNumber: 9\n            }, undefined),\n            title: 'Изображение',\n            action: insertImage\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M7 16l-4-4m0 0l4-4m-4 4h18\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 382,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 381,\n                columnNumber: 9\n            }, undefined),\n            title: 'Отменить',\n            action: ()=>execCommand('undo')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M17 8l4 4m0 0l-4 4m4-4H3\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 391,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 390,\n                columnNumber: 9\n            }, undefined),\n            title: 'Повторить',\n            action: ()=>execCommand('redo')\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-378f0ccdb29f75a1\" + \" \" + \"border border-gray-300 rounded-lg overflow-hidden \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-378f0ccdb29f75a1\" + \" \" + \"bg-gray-50 border-b border-gray-300 p-2 \".concat(isToolbarSticky ? 'sticky top-0 z-10 shadow-md' : ''),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-378f0ccdb29f75a1\" + \" \" + \"flex flex-wrap items-center gap-1\",\n                    children: toolbarButtons.map((button, index)=>{\n                        if (button.type === 'separator') {\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-378f0ccdb29f75a1\" + \" \" + \"w-px h-6 bg-gray-300 mx-1\"\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 22\n                            }, undefined);\n                        }\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: button.action,\n                            title: button.title,\n                            className: \"jsx-378f0ccdb29f75a1\" + \" \" + \"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors\",\n                            children: button.icon\n                        }, index, false, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                    lineNumber: 403,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 402,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: editorRef,\n                contentEditable: true,\n                onInput: handleInput,\n                onPaste: ()=>{\n                    // Handle paste to maintain formatting\n                    setTimeout(handleInput, 0);\n                },\n                style: {\n                    lineHeight: '1.6'\n                },\n                suppressContentEditableWarning: true,\n                \"data-placeholder\": placeholder,\n                className: \"jsx-378f0ccdb29f75a1\" + \" \" + \"min-h-[400px] p-4 focus:outline-none prose prose-lg max-w-none\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n                lineNumber: 425,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"378f0ccdb29f75a1\",\n                children: '[contenteditable].jsx-378f0ccdb29f75a1:empty:before{content:attr(data-placeholder);color:#9ca3af;pointer-events:none}[contenteditable].jsx-378f0ccdb29f75a1 h1.jsx-378f0ccdb29f75a1{font-size:2rem;font-weight:bold;margin:1rem 0;line-height:1.2}[contenteditable].jsx-378f0ccdb29f75a1 h2.jsx-378f0ccdb29f75a1{font-size:1.5rem;font-weight:bold;margin:1rem 0;line-height:1.3}[contenteditable].jsx-378f0ccdb29f75a1 h3.jsx-378f0ccdb29f75a1{font-size:1.25rem;font-weight:bold;margin:1rem 0;line-height:1.4}[contenteditable].jsx-378f0ccdb29f75a1 p.jsx-378f0ccdb29f75a1{margin:.5rem 0;line-height:1.6}[contenteditable].jsx-378f0ccdb29f75a1 ul.jsx-378f0ccdb29f75a1,[contenteditable].jsx-378f0ccdb29f75a1 ol.jsx-378f0ccdb29f75a1{margin:1rem 0;padding-left:2rem}[contenteditable].jsx-378f0ccdb29f75a1 li.jsx-378f0ccdb29f75a1{margin:.25rem 0}[contenteditable].jsx-378f0ccdb29f75a1 img.jsx-378f0ccdb29f75a1{max-width:100%;height:auto;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;margin:1rem 0}[contenteditable].jsx-378f0ccdb29f75a1 a.jsx-378f0ccdb29f75a1{color:#3b82f6;text-decoration:underline}[contenteditable].jsx-378f0ccdb29f75a1 a.jsx-378f0ccdb29f75a1:hover{color:#1d4ed8}[contenteditable].jsx-378f0ccdb29f75a1 blockquote.jsx-378f0ccdb29f75a1{border-left:4px solid#e5e7eb;padding-left:1rem;margin:1rem 0;font-style:italic;color:#6b7280;background-color:#f9fafb;padding:1rem;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem}[contenteditable].jsx-378f0ccdb29f75a1 code.jsx-378f0ccdb29f75a1{background-color:#f3f4f6;padding:.25rem .5rem;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;font-family:\"Monaco\",\"Menlo\",\"Ubuntu Mono\",monospace;font-size:.875rem;color:#1f2937}[contenteditable].jsx-378f0ccdb29f75a1 pre.jsx-378f0ccdb29f75a1{background-color:#1f2937;color:#f9fafb;padding:1rem;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;overflow-x:auto;margin:1rem 0}[contenteditable].jsx-378f0ccdb29f75a1 pre.jsx-378f0ccdb29f75a1 code.jsx-378f0ccdb29f75a1{background-color:transparent;padding:0;color:inherit}'\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/RichTextEditor.tsx\",\n        lineNumber: 400,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RichTextEditor, \"e5nDGZRBIbUDc83vSPtyXasEyrs=\");\n_c = RichTextEditor;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RichTextEditor);\nvar _c;\n$RefreshReg$(_c, \"RichTextEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/RichTextEditor.tsx\n"));

/***/ })

});