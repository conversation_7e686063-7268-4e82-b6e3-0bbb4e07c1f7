"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/blog/new/page",{

/***/ "(app-pages-browser)/./src/components/admin/SimpleMarkdownEditor.tsx":
/*!*******************************************************!*\
  !*** ./src/components/admin/SimpleMarkdownEditor.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst SimpleMarkdownEditor = (param)=>{\n    let { value, onChange, onImageInsert, placeholder = \"Начните писать...\", className = \"\" } = param;\n    _s();\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isPreview, setIsPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Функция для создания короткого ID изображения\n    const createImageShortcode = (imageUrl)=>{\n        // Создаем короткий ID из URL\n        const urlParts = imageUrl.split('/');\n        const filename = urlParts[urlParts.length - 1];\n        const shortId = filename.split('.')[0].slice(-8); // Последние 8 символов имени файла\n        return \"[img:\".concat(shortId, \"]\");\n    };\n    // Функция для вставки изображения в позицию курсора\n    const insertImageAtCursor = (imageUrl)=>{\n        if (textareaRef.current) {\n            const textarea = textareaRef.current;\n            const start = textarea.selectionStart;\n            const end = textarea.selectionEnd;\n            // Создаем шорткод вместо полного markdown\n            const imageShortcode = createImageShortcode(imageUrl);\n            // Сохраняем соответствие шорткода и URL в комментарии\n            const imageComment = \"<!-- \".concat(imageShortcode, \" = \").concat(imageUrl, \" -->\");\n            const beforeCursor = value.substring(0, start);\n            const afterCursor = value.substring(end);\n            // Добавляем переносы строк для красивого форматирования\n            const needsNewlineBefore = start > 0 && !beforeCursor.endsWith('\\n');\n            const needsNewlineAfter = afterCursor.length > 0 && !afterCursor.startsWith('\\n');\n            const newContent = beforeCursor + (needsNewlineBefore ? '\\n' : '') + imageShortcode + (needsNewlineAfter ? '\\n' : '') + afterCursor;\n            // Добавляем комментарий с соответствием в конец документа, если его еще нет\n            const finalContent = newContent.includes(imageComment) ? newContent : newContent + '\\n\\n' + imageComment;\n            onChange(finalContent);\n            // Устанавливаем курсор после вставленного шорткода\n            const newCursorPosition = start + (needsNewlineBefore ? 1 : 0) + imageShortcode.length;\n            setTimeout(()=>{\n                textarea.focus();\n                textarea.setSelectionRange(newCursorPosition, newCursorPosition);\n            }, 0);\n        }\n    };\n    // Глобальная функция для вставки изображений\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleMarkdownEditor.useEffect\": ()=>{\n            window.insertImageIntoEditor = insertImageAtCursor;\n            return ({\n                \"SimpleMarkdownEditor.useEffect\": ()=>{\n                    delete window.insertImageIntoEditor;\n                }\n            })[\"SimpleMarkdownEditor.useEffect\"];\n        }\n    }[\"SimpleMarkdownEditor.useEffect\"], [\n        value\n    ]);\n    // Функция для извлечения соответствий шорткодов и URL из комментариев\n    const extractImageMappings = (markdown)=>{\n        const mappings = {};\n        const commentRegex = /<!-- \\[img:([^\\]]+)\\] = ([^>]+) -->/g;\n        let match;\n        while((match = commentRegex.exec(markdown)) !== null){\n            mappings[\"[img:\".concat(match[1], \"]\")] = match[2];\n        }\n        return mappings;\n    };\n    // Функция для конвертации markdown в HTML для превью\n    const markdownToHtml = (markdown)=>{\n        const imageMappings = extractImageMappings(markdown);\n        let html = markdown// Убираем комментарии из отображения\n        .replace(/<!-- \\[img:[^\\]]+\\] = [^>]+ -->/g, '')// Обрабатываем шорткоды изображений\n        .replace(/\\[img:([^\\]]+)\\]/g, (match)=>{\n            const imageUrl = imageMappings[match];\n            return imageUrl ? '<div class=\"my-6\"><img src=\"'.concat(imageUrl, '\" alt=\"Изображение\" class=\"w-full max-w-2xl mx-auto rounded-lg shadow-md\" /></div>') : match;\n        })// Обрабатываем обычные markdown изображения\n        .replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, '<div class=\"my-6\"><img src=\"$2\" alt=\"$1\" class=\"w-full max-w-2xl mx-auto rounded-lg shadow-md\" /></div>')// Обрабатываем заголовки\n        .replace(/^### (.*$)/gim, '<h3 class=\"text-xl font-semibold text-gray-900 mb-3 mt-6\">$1</h3>').replace(/^## (.*$)/gim, '<h2 class=\"text-2xl font-semibold text-gray-900 mb-4 mt-8\">$1</h2>').replace(/^# (.*$)/gim, '<h1 class=\"text-3xl font-bold text-gray-900 mb-6\">$1</h1>')// Обрабатываем форматирование\n        .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>').replace(/\\*(.*?)\\*/g, '<em>$1</em>')// Обрабатываем параграфы\n        .replace(/\\n\\n/g, '</p><p class=\"text-gray-700 mb-4 leading-relaxed\">').replace(/^(.+)$/gm, '<p class=\"text-gray-700 mb-4 leading-relaxed\">$1</p>').replace(/<p class=\"text-gray-700 mb-4 leading-relaxed\"><\\/p>/g, '').replace(/<p class=\"text-gray-700 mb-4 leading-relaxed\">(<h[1-6][^>]*>.*<\\/h[1-6]>)<\\/p>/g, '$1').replace(/<p class=\"text-gray-700 mb-4 leading-relaxed\">(<div[^>]*>.*<\\/div>)<\\/p>/g, '$1');\n        return html;\n    };\n    const toolbarButtons = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                children: \"H1\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 130,\n                columnNumber: 13\n            }, undefined),\n            title: 'Заголовок 1',\n            action: ()=>insertText('# ')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                children: \"H2\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 135,\n                columnNumber: 13\n            }, undefined),\n            title: 'Заголовок 2',\n            action: ()=>insertText('## ')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                children: \"H3\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 140,\n                columnNumber: 13\n            }, undefined),\n            title: 'Заголовок 3',\n            action: ()=>insertText('### ')\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                children: \"B\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 146,\n                columnNumber: 13\n            }, undefined),\n            title: 'Жирный',\n            action: ()=>wrapText('**', '**')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                children: \"I\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 151,\n                columnNumber: 13\n            }, undefined),\n            title: 'Курсив',\n            action: ()=>wrapText('*', '*')\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, undefined),\n            title: 'Изображение',\n            action: onImageInsert || (()=>{})\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, undefined),\n            title: isPreview ? 'Редактировать' : 'Превью',\n            action: ()=>setIsPreview(!isPreview)\n        }\n    ];\n    const insertText = (text)=>{\n        if (textareaRef.current) {\n            const textarea = textareaRef.current;\n            const start = textarea.selectionStart;\n            const end = textarea.selectionEnd;\n            const beforeCursor = value.substring(0, start);\n            const afterCursor = value.substring(end);\n            const newContent = beforeCursor + text + afterCursor;\n            onChange(newContent);\n            setTimeout(()=>{\n                textarea.focus();\n                textarea.setSelectionRange(start + text.length, start + text.length);\n            }, 0);\n        }\n    };\n    const wrapText = (before, after)=>{\n        if (textareaRef.current) {\n            const textarea = textareaRef.current;\n            const start = textarea.selectionStart;\n            const end = textarea.selectionEnd;\n            const selectedText = value.substring(start, end);\n            const beforeCursor = value.substring(0, start);\n            const afterCursor = value.substring(end);\n            const newContent = beforeCursor + before + selectedText + after + afterCursor;\n            onChange(newContent);\n            setTimeout(()=>{\n                textarea.focus();\n                if (selectedText) {\n                    textarea.setSelectionRange(start + before.length, end + before.length);\n                } else {\n                    textarea.setSelectionRange(start + before.length, start + before.length);\n                }\n            }, 0);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border border-gray-300 rounded-lg overflow-hidden \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 border-b border-gray-300 p-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap items-center gap-1\",\n                    children: toolbarButtons.map((button, index)=>{\n                        if (button.type === 'separator') {\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-px h-6 bg-gray-300 mx-1\"\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 22\n                            }, undefined);\n                        }\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: button.action,\n                            title: button.title,\n                            className: \"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors\",\n                            children: button.icon\n                        }, index, false, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, undefined),\n            isPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-[400px] p-4 prose prose-lg max-w-none bg-white\",\n                dangerouslySetInnerHTML: {\n                    __html: markdownToHtml(value)\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 248,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                ref: textareaRef,\n                value: value,\n                onChange: (e)=>onChange(e.target.value),\n                placeholder: placeholder,\n                className: \"w-full min-h-[400px] p-4 border-none resize-none focus:outline-none font-mono text-sm leading-relaxed\",\n                style: {\n                    fontFamily: 'Monaco, Menlo, \"Ubuntu Mono\", monospace'\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 253,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SimpleMarkdownEditor, \"2Khj9zGOxvkdc2HjTdSVtR+iHPI=\");\n_c = SimpleMarkdownEditor;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SimpleMarkdownEditor);\nvar _c;\n$RefreshReg$(_c, \"SimpleMarkdownEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/SimpleMarkdownEditor.tsx\n"));

/***/ })

});