"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/blog/new/page",{

/***/ "(app-pages-browser)/./src/app/admin/blog/new/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/admin/blog/new/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/admin/AdminLayout */ \"(app-pages-browser)/./src/components/admin/AdminLayout.tsx\");\n/* harmony import */ var _components_admin_MediaGallery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/admin/MediaGallery */ \"(app-pages-browser)/./src/components/admin/MediaGallery.tsx\");\n/* harmony import */ var _components_admin_SimpleMarkdownEditor__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/admin/SimpleMarkdownEditor */ \"(app-pages-browser)/./src/components/admin/SimpleMarkdownEditor.tsx\");\n/* harmony import */ var _components_admin_SEOSettings__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/admin/SEOSettings */ \"(app-pages-browser)/./src/components/admin/SEOSettings.tsx\");\n/* harmony import */ var _lib_cms_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/cms-utils */ \"(app-pages-browser)/./src/lib/cms-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst NewBlogPostPage = ()=>{\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMediaGallery, setShowMediaGallery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mediaGalleryMode, setMediaGalleryMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('featured');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        slug: '',\n        excerpt: '',\n        content: '',\n        status: 'draft',\n        tags: '',\n        featuredImage: '',\n        contentImages: [],\n        seo: {\n            metaTitle: '',\n            metaDescription: '',\n            keywords: [],\n            ogTitle: '',\n            ogDescription: '',\n            ogImage: '',\n            ogType: 'article',\n            twitterTitle: '',\n            twitterDescription: '',\n            twitterImage: '',\n            twitterCard: 'summary_large_image',\n            canonicalUrl: '',\n            noIndex: false,\n            noFollow: false,\n            noArchive: false,\n            noSnippet: false,\n            schemaType: 'BlogPosting',\n            focusKeyword: '',\n            breadcrumbs: true\n        }\n    });\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Auto-generate slug from title\n        if (name === 'title' && !formData.slug) {\n            const slug = value.toLowerCase().replace(/[^a-z0-9а-я]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '');\n            setFormData((prev)=>({\n                    ...prev,\n                    slug\n                }));\n        }\n    };\n    const handleFeaturedImageSelect = (imageUrl)=>{\n        setFormData((prev)=>({\n                ...prev,\n                featuredImage: imageUrl\n            }));\n        setShowMediaGallery(false);\n    };\n    const handleContentImagesSelect = (imageUrls)=>{\n        if (mediaGalleryMode === 'content') {\n            // Вставляем изображения через глобальную функцию\n            imageUrls.forEach((imageUrl)=>{\n                if (window.insertImageIntoEditor) {\n                    window.insertImageIntoEditor(imageUrl);\n                }\n            });\n        } else {\n            // Legacy behavior for contentImages array\n            setFormData((prev)=>({\n                    ...prev,\n                    contentImages: imageUrls\n                }));\n        }\n        setShowMediaGallery(false);\n    };\n    const openMediaGallery = (mode)=>{\n        setMediaGalleryMode(mode);\n        setShowMediaGallery(true);\n    };\n    const removeContentImage = (imageUrl)=>{\n        setFormData((prev)=>({\n                ...prev,\n                contentImages: prev.contentImages.filter((url)=>url !== imageUrl)\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        try {\n            const postData = {\n                title: formData.title,\n                slug: formData.slug,\n                excerpt: formData.excerpt,\n                content: formData.content,\n                status: formData.status,\n                featuredImage: formData.featuredImage || undefined,\n                contentImages: formData.contentImages.length > 0 ? formData.contentImages : undefined,\n                tags: formData.tags.split(',').map((tag)=>tag.trim()).filter(Boolean),\n                type: 'blog',\n                category: 'general',\n                author: 'Admin',\n                seo: {\n                    ...formData.seo,\n                    metaTitle: formData.seo.metaTitle || formData.title,\n                    metaDescription: formData.seo.metaDescription || formData.excerpt,\n                    keywords: formData.seo.keywords.length > 0 ? formData.seo.keywords : formData.tags.split(',').map((tag)=>tag.trim()).filter(Boolean),\n                    ogTitle: formData.seo.ogTitle || formData.title,\n                    ogDescription: formData.seo.ogDescription || formData.excerpt,\n                    ogImage: formData.seo.ogImage || formData.featuredImage,\n                    twitterTitle: formData.seo.twitterTitle || formData.title,\n                    twitterDescription: formData.seo.twitterDescription || formData.excerpt,\n                    twitterImage: formData.seo.twitterImage || formData.featuredImage\n                }\n            };\n            const newPost = (0,_lib_cms_utils__WEBPACK_IMPORTED_MODULE_7__.createBlogPost)(postData);\n            router.push(\"/admin/blog/\".concat(newPost.id));\n        } catch (error) {\n            console.error('Ошибка при создании статьи:', error);\n            alert('Ошибка при создании статьи');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSaveDraft = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                status: 'draft'\n            }));\n        setTimeout(()=>{\n            var _document_getElementById;\n            (_document_getElementById = document.getElementById('submit-form')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n        }, 0);\n    };\n    const handlePublish = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                status: 'published'\n            }));\n        setTimeout(()=>{\n            var _document_getElementById;\n            (_document_getElementById = document.getElementById('submit-form')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n        }, 0);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 min-h-screen bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold text-gray-900\",\n                                            children: \"Новая статья\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-gray-600\",\n                                            children: \"Создание новой статьи для блога\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: handleSaveDraft,\n                                            disabled: isLoading,\n                                            className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\",\n                                            children: \"Сохранить черновик\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: handlePublish,\n                                            disabled: isLoading,\n                                            className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\",\n                                            children: \"Опубликовать\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:col-span-2 space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"title\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Заголовок статьи *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"title\",\n                                                            name: \"title\",\n                                                            required: true,\n                                                            value: formData.title,\n                                                            onChange: handleInputChange,\n                                                            className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                            placeholder: \"Введите заголовок статьи\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-4\",\n                                                            children: \"Содержание статьи *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_SimpleMarkdownEditor__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            value: formData.content,\n                                                            onChange: (content)=>setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        content\n                                                                    })),\n                                                            onImageInsert: ()=>openMediaGallery('content'),\n                                                            placeholder: \"Начните писать содержание статьи...\",\n                                                            className: \"min-h-[500px]\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"URL статьи\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"slug\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"URL (slug) *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"slug\",\n                                                                name: \"slug\",\n                                                                required: true,\n                                                                value: formData.slug,\n                                                                onChange: handleInputChange,\n                                                                className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                placeholder: \"url-статьи\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: [\n                                                                            \"URL статьи: /blog/\",\n                                                                            formData.slug || 'url-статьи'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 262,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    formData.status === 'published' && formData.slug && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                        href: \"/blog/\".concat(formData.slug),\n                                                                        target: \"_blank\",\n                                                                        rel: \"noopener noreferrer\",\n                                                                        className: \"inline-flex items-center px-3 py-1 mt-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-4 h-4 mr-1\",\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 2,\n                                                                                    d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                    lineNumber: 273,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                lineNumber: 272,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            \"Посмотреть статью\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 266,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"Краткое описание\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"excerpt\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Краткое описание *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                id: \"excerpt\",\n                                                                name: \"excerpt\",\n                                                                required: true,\n                                                                rows: 3,\n                                                                value: formData.excerpt,\n                                                                onChange: handleInputChange,\n                                                                className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                placeholder: \"Краткое описание статьи для превью\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"Изображение превью\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                formData.featuredImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative inline-block\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: formData.featuredImage,\n                                                                            alt: \"Featured\",\n                                                                            className: \"w-full h-32 object-cover rounded-lg border border-gray-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                            lineNumber: 309,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        featuredImage: ''\n                                                                                    })),\n                                                                            className: \"absolute -top-2 -right-2 bg-red-600 text-white rounded-full p-1 hover:bg-red-700\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-4 h-4\",\n                                                                                fill: \"currentColor\",\n                                                                                viewBox: \"0 0 20 20\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    fillRule: \"evenodd\",\n                                                                                    d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                                                                    clipRule: \"evenodd\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                    lineNumber: 320,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                lineNumber: 319,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                            lineNumber: 314,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                    lineNumber: 308,\n                                                                    columnNumber: 23\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"border-2 border-dashed border-gray-300 rounded-lg p-4 text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"mx-auto h-8 w-8 text-gray-400\",\n                                                                            stroke: \"currentColor\",\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 48 48\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\",\n                                                                                strokeWidth: 2,\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                lineNumber: 327,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                            lineNumber: 326,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"mt-2 text-sm text-gray-600\",\n                                                                            children: \"Изображение не выбрано\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                            lineNumber: 329,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                    lineNumber: 325,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>openMediaGallery('featured'),\n                                                                    className: \"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4 mr-2\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                lineNumber: 338,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                            lineNumber: 337,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        \"Выбрать изображение\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                    lineNumber: 332,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"Публикация\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"status\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Статус\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                id: \"status\",\n                                                                name: \"status\",\n                                                                value: formData.status,\n                                                                onChange: handleInputChange,\n                                                                className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"draft\",\n                                                                        children: \"Черновик\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 360,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"published\",\n                                                                        children: \"Опубликовано\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 361,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"Теги\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"tags\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Теги (через запятую)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"tags\",\n                                                                name: \"tags\",\n                                                                value: formData.tags,\n                                                                onChange: handleInputChange,\n                                                                className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                placeholder: \"тег1, тег2, тег3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 373,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_SEOSettings__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                seoData: formData.seo,\n                                                onChange: (seoData)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            seo: seoData\n                                                        })),\n                                                title: formData.title,\n                                                excerpt: formData.excerpt\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                id: \"submit-form\",\n                                className: \"hidden\",\n                                disabled: isLoading,\n                                children: \"Submit\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_MediaGallery__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showMediaGallery,\n                onClose: ()=>setShowMediaGallery(false),\n                onSelect: mediaGalleryMode === 'featured' ? handleFeaturedImageSelect : (imageUrl)=>handleContentImagesSelect([\n                        imageUrl\n                    ]),\n                onSelectMultiple: mediaGalleryMode === 'content' ? handleContentImagesSelect : undefined,\n                multiple: mediaGalleryMode === 'content',\n                selectedImage: mediaGalleryMode === 'featured' ? formData.featuredImage : undefined,\n                mode: mediaGalleryMode\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                lineNumber: 408,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, undefined);\n};\n_s(NewBlogPostPage, \"9Y9JZpjXB2vXVjXtvKlyOuOMxPw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = NewBlogPostPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NewBlogPostPage);\nvar _c;\n$RefreshReg$(_c, \"NewBlogPostPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/blog/new/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/admin/SEOSettings.tsx":
/*!**********************************************!*\
  !*** ./src/components/admin/SEOSettings.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst SEOSettings = (param)=>{\n    let { seoData, onChange, title, excerpt = '', className = '' } = param;\n    var _seoData_metaTitle, _seoData_metaDescription, _seoData_keywords;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('general');\n    const handleChange = (field, value)=>{\n        onChange({\n            ...seoData,\n            [field]: value\n        });\n    };\n    // Автоматическое заполнение полей на основе заголовка и описания\n    const autoFillSEO = ()=>{\n        const updates = {};\n        if (!seoData.metaTitle && title) {\n            updates.metaTitle = title;\n        }\n        if (!seoData.metaDescription && excerpt) {\n            updates.metaDescription = excerpt.substring(0, 160);\n        }\n        if (!seoData.ogTitle && title) {\n            updates.ogTitle = title;\n        }\n        if (!seoData.ogDescription && excerpt) {\n            updates.ogDescription = excerpt.substring(0, 300);\n        }\n        if (!seoData.twitterTitle && title) {\n            updates.twitterTitle = title;\n        }\n        if (!seoData.twitterDescription && excerpt) {\n            updates.twitterDescription = excerpt.substring(0, 200);\n        }\n        onChange({\n            ...seoData,\n            ...updates\n        });\n    };\n    // Анализ SEO (базовый)\n    const getSEOScore = ()=>{\n        let score = 0;\n        let issues = [];\n        // Проверка мета-заголовка\n        if (seoData.metaTitle) {\n            if (seoData.metaTitle.length >= 30 && seoData.metaTitle.length <= 60) {\n                score += 20;\n            } else {\n                issues.push('Мета-заголовок должен быть 30-60 символов');\n            }\n        } else {\n            issues.push('Мета-заголовок не заполнен');\n        }\n        // Проверка мета-описания\n        if (seoData.metaDescription) {\n            if (seoData.metaDescription.length >= 120 && seoData.metaDescription.length <= 160) {\n                score += 20;\n            } else {\n                issues.push('Мета-описание должно быть 120-160 символов');\n            }\n        } else {\n            issues.push('Мета-описание не заполнено');\n        }\n        // Проверка ключевых слов\n        if (seoData.keywords && seoData.keywords.length > 0) {\n            score += 15;\n        } else {\n            issues.push('Ключевые слова не заданы');\n        }\n        // Проверка фокусного ключевого слова\n        if (seoData.focusKeyword) {\n            score += 15;\n            // Проверка вхождения в заголовок\n            if (title.toLowerCase().includes(seoData.focusKeyword.toLowerCase())) {\n                score += 10;\n            } else {\n                issues.push('Фокусное ключевое слово не найдено в заголовке');\n            }\n        } else {\n            issues.push('Фокусное ключевое слово не задано');\n        }\n        // Проверка Open Graph\n        if (seoData.ogTitle && seoData.ogDescription) {\n            score += 10;\n        } else {\n            issues.push('Open Graph данные не полные');\n        }\n        // Проверка Twitter Card\n        if (seoData.twitterTitle && seoData.twitterDescription) {\n            score += 10;\n        } else {\n            issues.push('Twitter Card данные не полные');\n        }\n        return {\n            score,\n            issues\n        };\n    };\n    const { score, issues } = getSEOScore();\n    const getScoreColor = (score)=>{\n        if (score >= 80) return 'text-green-600';\n        if (score >= 60) return 'text-yellow-600';\n        return 'text-red-600';\n    };\n    const getScoreLabel = (score)=>{\n        if (score >= 80) return 'Отлично';\n        if (score >= 60) return 'Хорошо';\n        if (score >= 40) return 'Удовлетворительно';\n        return 'Требует улучшения';\n    };\n    const tabs = [\n        {\n            id: 'general',\n            label: 'Основные',\n            icon: '🎯'\n        },\n        {\n            id: 'social',\n            label: 'Соцсети',\n            icon: '📱'\n        },\n        {\n            id: 'advanced',\n            label: 'Дополнительно',\n            icon: '⚙️'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow p-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900\",\n                        children: \"SEO настройки\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: autoFillSEO,\n                        className: \"text-sm text-primary-600 hover:text-primary-700 font-medium\",\n                        children: \"Автозаполнение\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-gray-50 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-gray-700\",\n                                children: \"SEO оценка\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg font-bold \".concat(getScoreColor(score)),\n                                children: [\n                                    score,\n                                    \"/100 - \",\n                                    getScoreLabel(score)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-2 rounded-full transition-all duration-300 \".concat(score >= 80 ? 'bg-green-500' : score >= 60 ? 'bg-yellow-500' : 'bg-red-500'),\n                            style: {\n                                width: \"\".concat(score, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, undefined),\n                    issues.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-700 mb-1\",\n                                children: \"Рекомендации:\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-sm text-gray-600 space-y-1\",\n                                children: issues.slice(0, 3).map((issue, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-yellow-500 mr-1\",\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            issue\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-200 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"-mb-px flex space-x-8\",\n                    children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: ()=>setActiveTab(tab.id),\n                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mr-2\",\n                                    children: tab.icon\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 15\n                                }, undefined),\n                                tab.label\n                            ]\n                        }, tab.id, true, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    activeTab === 'general' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Фокусное ключевое слово\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: seoData.focusKeyword || '',\n                                        onChange: (e)=>handleChange('focusKeyword', e.target.value),\n                                        className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                        placeholder: \"основное ключевое слово\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-gray-500\",\n                                        children: \"Основное ключевое слово для этой страницы\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: [\n                                            \"Мета-заголовок (\",\n                                            ((_seoData_metaTitle = seoData.metaTitle) === null || _seoData_metaTitle === void 0 ? void 0 : _seoData_metaTitle.length) || 0,\n                                            \"/60)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: seoData.metaTitle,\n                                        onChange: (e)=>handleChange('metaTitle', e.target.value),\n                                        className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                        placeholder: \"SEO заголовок страницы\",\n                                        maxLength: 60\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-gray-500\",\n                                        children: \"Рекомендуется 30-60 символов\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: [\n                                            \"Мета-описание (\",\n                                            ((_seoData_metaDescription = seoData.metaDescription) === null || _seoData_metaDescription === void 0 ? void 0 : _seoData_metaDescription.length) || 0,\n                                            \"/160)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        rows: 3,\n                                        value: seoData.metaDescription,\n                                        onChange: (e)=>handleChange('metaDescription', e.target.value),\n                                        className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                        placeholder: \"Краткое описание страницы для поисковых систем\",\n                                        maxLength: 160\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-gray-500\",\n                                        children: \"Рекомендуется 120-160 символов\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Ключевые слова\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: ((_seoData_keywords = seoData.keywords) === null || _seoData_keywords === void 0 ? void 0 : _seoData_keywords.join(', ')) || '',\n                                        onChange: (e)=>handleChange('keywords', e.target.value.split(',').map((k)=>k.trim()).filter(Boolean)),\n                                        className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                        placeholder: \"ключевое слово 1, ключевое слово 2, ключевое слово 3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-gray-500\",\n                                        children: \"Разделяйте ключевые слова запятыми\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    activeTab === 'social' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 pb-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-md font-medium text-gray-900 mb-3\",\n                                        children: \"Open Graph (Facebook, LinkedIn)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"OG заголовок\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: seoData.ogTitle || '',\n                                                        onChange: (e)=>handleChange('ogTitle', e.target.value),\n                                                        className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                        placeholder: \"Заголовок для социальных сетей\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"OG описание\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        rows: 2,\n                                                        value: seoData.ogDescription || '',\n                                                        onChange: (e)=>handleChange('ogDescription', e.target.value),\n                                                        className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                        placeholder: \"Описание для социальных сетей\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Тип контента\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: seoData.ogType || 'article',\n                                                        onChange: (e)=>handleChange('ogType', e.target.value),\n                                                        className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"website\",\n                                                                children: \"Веб-сайт\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"article\",\n                                                                children: \"Статья\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"product\",\n                                                                children: \"Товар\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-md font-medium text-gray-900 mb-3\",\n                                        children: \"Twitter Card\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Twitter заголовок\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: seoData.twitterTitle || '',\n                                                        onChange: (e)=>handleChange('twitterTitle', e.target.value),\n                                                        className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                        placeholder: \"Заголовок для Twitter\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Twitter описание\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        rows: 2,\n                                                        value: seoData.twitterDescription || '',\n                                                        onChange: (e)=>handleChange('twitterDescription', e.target.value),\n                                                        className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                        placeholder: \"Описание для Twitter\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Тип карточки\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: seoData.twitterCard || 'summary_large_image',\n                                                        onChange: (e)=>handleChange('twitterCard', e.target.value),\n                                                        className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"summary\",\n                                                                children: \"Краткая\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"summary_large_image\",\n                                                                children: \"С большим изображением\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    activeTab === 'advanced' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Канонический URL\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"url\",\n                                        value: seoData.canonicalUrl || '',\n                                        onChange: (e)=>handleChange('canonicalUrl', e.target.value),\n                                        className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                        placeholder: \"https://example.com/canonical-url\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-gray-500\",\n                                        children: \"Оставьте пустым для автоматического определения\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Тип структурированных данных\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: seoData.schemaType || 'Article',\n                                        onChange: (e)=>handleChange('schemaType', e.target.value),\n                                        className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"Article\",\n                                                children: \"Статья\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"BlogPosting\",\n                                                children: \"Блог пост\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"WebPage\",\n                                                children: \"Веб-страница\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"Organization\",\n                                                children: \"Организация\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"Person\",\n                                                children: \"Персона\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-3\",\n                                        children: \"Настройки индексации\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: seoData.noIndex || false,\n                                                        onChange: (e)=>handleChange('noIndex', e.target.checked),\n                                                        className: \"rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-sm text-gray-700\",\n                                                        children: \"Не индексировать (noindex)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: seoData.noFollow || false,\n                                                        onChange: (e)=>handleChange('noFollow', e.target.checked),\n                                                        className: \"rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-sm text-gray-700\",\n                                                        children: \"Не следовать ссылкам (nofollow)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: seoData.noArchive || false,\n                                                        onChange: (e)=>handleChange('noArchive', e.target.checked),\n                                                        className: \"rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-sm text-gray-700\",\n                                                        children: \"Не архивировать (noarchive)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: seoData.noSnippet || false,\n                                                        onChange: (e)=>handleChange('noSnippet', e.target.checked),\n                                                        className: \"rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-sm text-gray-700\",\n                                                        children: \"Не показывать сниппет (nosnippet)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SEOSettings.tsx\",\n        lineNumber: 150,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SEOSettings, \"U6FFcHOAbEKdygLxE/3d+MQW8MA=\");\n_c = SEOSettings;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SEOSettings);\nvar _c;\n$RefreshReg$(_c, \"SEOSettings\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/SEOSettings.tsx\n"));

/***/ })

});