"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/blog/new/page",{

/***/ "(app-pages-browser)/./src/app/admin/blog/new/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/admin/blog/new/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/admin/AdminLayout */ \"(app-pages-browser)/./src/components/admin/AdminLayout.tsx\");\n/* harmony import */ var _components_admin_MediaGallery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/admin/MediaGallery */ \"(app-pages-browser)/./src/components/admin/MediaGallery.tsx\");\n/* harmony import */ var _lib_cms_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/cms-utils */ \"(app-pages-browser)/./src/lib/cms-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst NewBlogPostPage = ()=>{\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMediaGallery, setShowMediaGallery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mediaGalleryMode, setMediaGalleryMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('featured');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        slug: '',\n        excerpt: '',\n        content: '',\n        status: 'draft',\n        tags: '',\n        featuredImage: '',\n        contentImages: [],\n        metaTitle: '',\n        metaDescription: ''\n    });\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Auto-generate slug from title\n        if (name === 'title' && !formData.slug) {\n            const slug = value.toLowerCase().replace(/[^a-z0-9а-я]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '');\n            setFormData((prev)=>({\n                    ...prev,\n                    slug\n                }));\n        }\n    };\n    const handleFeaturedImageSelect = (imageUrl)=>{\n        setFormData((prev)=>({\n                ...prev,\n                featuredImage: imageUrl\n            }));\n        setShowMediaGallery(false);\n    };\n    const handleContentImagesSelect = (imageUrls)=>{\n        if (mediaGalleryMode === 'content') {\n            // Insert images into content at cursor position\n            const textarea = document.getElementById('content');\n            if (textarea) {\n                const start = textarea.selectionStart;\n                const end = textarea.selectionEnd;\n                const currentContent = formData.content;\n                // Create markdown for selected images\n                const imageMarkdown = imageUrls.map((url)=>\"![Описание изображения](\".concat(url, \")\")).join('\\n\\n');\n                // Insert at cursor position\n                const newContent = currentContent.substring(0, start) + (start > 0 && currentContent[start - 1] !== '\\n' ? '\\n\\n' : '') + imageMarkdown + (end < currentContent.length && currentContent[end] !== '\\n' ? '\\n\\n' : '') + currentContent.substring(end);\n                setFormData((prev)=>({\n                        ...prev,\n                        content: newContent\n                    }));\n                // Set cursor position after inserted content\n                setTimeout(()=>{\n                    const newPosition = start + imageMarkdown.length + 4; // +4 for newlines\n                    textarea.setSelectionRange(newPosition, newPosition);\n                    textarea.focus();\n                }, 0);\n            }\n        } else {\n            // Legacy behavior for contentImages array\n            setFormData((prev)=>({\n                    ...prev,\n                    contentImages: imageUrls\n                }));\n        }\n        setShowMediaGallery(false);\n    };\n    const openMediaGallery = (mode)=>{\n        setMediaGalleryMode(mode);\n        setShowMediaGallery(true);\n    };\n    const removeContentImage = (imageUrl)=>{\n        setFormData((prev)=>({\n                ...prev,\n                contentImages: prev.contentImages.filter((url)=>url !== imageUrl)\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        try {\n            const postData = {\n                title: formData.title,\n                slug: formData.slug,\n                excerpt: formData.excerpt,\n                content: formData.content,\n                status: formData.status,\n                featuredImage: formData.featuredImage || undefined,\n                contentImages: formData.contentImages.length > 0 ? formData.contentImages : undefined,\n                tags: formData.tags.split(',').map((tag)=>tag.trim()).filter(Boolean),\n                type: 'blog',\n                category: 'general',\n                author: 'Admin',\n                seo: {\n                    metaTitle: formData.metaTitle || formData.title,\n                    metaDescription: formData.metaDescription || formData.excerpt,\n                    keywords: formData.tags.split(',').map((tag)=>tag.trim()).filter(Boolean)\n                }\n            };\n            const newPost = (0,_lib_cms_utils__WEBPACK_IMPORTED_MODULE_5__.createBlogPost)(postData);\n            router.push(\"/admin/blog/\".concat(newPost.id));\n        } catch (error) {\n            console.error('Ошибка при создании статьи:', error);\n            alert('Ошибка при создании статьи');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSaveDraft = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                status: 'draft'\n            }));\n        setTimeout(()=>{\n            var _document_getElementById;\n            (_document_getElementById = document.getElementById('submit-form')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n        }, 0);\n    };\n    const handlePublish = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                status: 'published'\n            }));\n        setTimeout(()=>{\n            var _document_getElementById;\n            (_document_getElementById = document.getElementById('submit-form')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n        }, 0);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 min-h-screen bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold text-gray-900\",\n                                            children: \"Новая статья\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-gray-600\",\n                                            children: \"Создание новой статьи для блога\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: handleSaveDraft,\n                                            disabled: isLoading,\n                                            className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\",\n                                            children: \"Сохранить черновик\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: handlePublish,\n                                            disabled: isLoading,\n                                            className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\",\n                                            children: \"Опубликовать\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:col-span-2 space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"title\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Заголовок статьи *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"title\",\n                                                            name: \"title\",\n                                                            required: true,\n                                                            value: formData.title,\n                                                            onChange: handleInputChange,\n                                                            className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                            placeholder: \"Введите заголовок статьи\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"slug\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"URL (slug) *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"slug\",\n                                                            name: \"slug\",\n                                                            required: true,\n                                                            value: formData.slug,\n                                                            onChange: handleInputChange,\n                                                            className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                            placeholder: \"url-статьи\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2 flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        \"URL статьи: /blog/\",\n                                                                        formData.slug || 'url-статьи'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                    lineNumber: 232,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                formData.status === 'published' && formData.slug && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"/blog/\".concat(formData.slug),\n                                                                    target: \"_blank\",\n                                                                    rel: \"noopener noreferrer\",\n                                                                    className: \"inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4 mr-1\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                lineNumber: 243,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                            lineNumber: 242,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        \"Посмотреть статью\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                    lineNumber: 236,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"excerpt\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Краткое описание *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            id: \"excerpt\",\n                                                            name: \"excerpt\",\n                                                            required: true,\n                                                            rows: 3,\n                                                            value: formData.excerpt,\n                                                            onChange: handleInputChange,\n                                                            className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                            placeholder: \"Краткое описание статьи для превью\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Изображение превью\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                formData.featuredImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative inline-block\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: formData.featuredImage,\n                                                                            alt: \"Featured\",\n                                                                            className: \"w-32 h-32 object-cover rounded-lg border border-gray-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                            lineNumber: 280,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        featuredImage: ''\n                                                                                    })),\n                                                                            className: \"absolute -top-2 -right-2 bg-red-600 text-white rounded-full p-1 hover:bg-red-700\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-4 h-4\",\n                                                                                fill: \"currentColor\",\n                                                                                viewBox: \"0 0 20 20\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    fillRule: \"evenodd\",\n                                                                                    d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                                                                    clipRule: \"evenodd\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                    lineNumber: 291,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                lineNumber: 290,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                            lineNumber: 285,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                    lineNumber: 279,\n                                                                    columnNumber: 23\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"mx-auto h-12 w-12 text-gray-400\",\n                                                                            stroke: \"currentColor\",\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 48 48\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\",\n                                                                                strokeWidth: 2,\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                lineNumber: 298,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                            lineNumber: 297,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"mt-2 text-sm text-gray-600\",\n                                                                            children: \"Изображение не выбрано\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                            lineNumber: 300,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                    lineNumber: 296,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>openMediaGallery('featured'),\n                                                                    className: \"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4 mr-2\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                lineNumber: 309,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                            lineNumber: 308,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        \"Выбрать изображение\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                    lineNumber: 303,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"content\",\n                                                                    className: \"block text-sm font-medium text-gray-700\",\n                                                                    children: \"Содержание статьи *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>openMediaGallery('content'),\n                                                                    className: \"inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4 mr-1\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                lineNumber: 331,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                            lineNumber: 330,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        \"Вставить изображение\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                    lineNumber: 325,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            id: \"content\",\n                                                            name: \"content\",\n                                                            required: true,\n                                                            rows: 20,\n                                                            value: formData.content,\n                                                            onChange: handleInputChange,\n                                                            className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                            placeholder: \"Содержание статьи в формате Markdown  Для добавления изображений используйте: ![Описание изображения](URL)  Или нажмите кнопку 'Вставить изображение' выше\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2 text-xs text-gray-500\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                                                className: \"cursor-pointer\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Справка по Markdown\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 350,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-2 space-y-1 pl-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                    children: \"# Заголовок 1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                    lineNumber: 352,\n                                                                                    columnNumber: 30\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                lineNumber: 352,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                    children: \"## Заголовок 2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                    lineNumber: 353,\n                                                                                    columnNumber: 30\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                lineNumber: 353,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                    children: \"### Заголовок 3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                    lineNumber: 354,\n                                                                                    columnNumber: 30\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                lineNumber: 354,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                        children: \"![Описание](URL_изображения)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                        lineNumber: 355,\n                                                                                        columnNumber: 30\n                                                                                    }, undefined),\n                                                                                    \" - изображение\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                lineNumber: 355,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                    children: \"**жирный текст**\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                    lineNumber: 356,\n                                                                                    columnNumber: 30\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                lineNumber: 356,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                    children: \"*курсив*\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                    lineNumber: 357,\n                                                                                    columnNumber: 30\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                lineNumber: 357,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 351,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"Публикация\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"status\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Статус\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                id: \"status\",\n                                                                name: \"status\",\n                                                                value: formData.status,\n                                                                onChange: handleInputChange,\n                                                                className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"draft\",\n                                                                        children: \"Черновик\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 381,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"published\",\n                                                                        children: \"Опубликовано\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 382,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"Теги\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"tags\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Теги (через запятую)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"tags\",\n                                                                name: \"tags\",\n                                                                value: formData.tags,\n                                                                onChange: handleInputChange,\n                                                                className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                placeholder: \"тег1, тег2, тег3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"SEO\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"metaTitle\",\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Meta Title\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 411,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        id: \"metaTitle\",\n                                                                        name: \"metaTitle\",\n                                                                        value: formData.metaTitle,\n                                                                        onChange: handleInputChange,\n                                                                        className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                        placeholder: \"Заголовок для поисковых систем\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 414,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 410,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"metaDescription\",\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Meta Description\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 425,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                        id: \"metaDescription\",\n                                                                        name: \"metaDescription\",\n                                                                        rows: 3,\n                                                                        value: formData.metaDescription,\n                                                                        onChange: handleInputChange,\n                                                                        className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                        placeholder: \"Описание для поисковых систем\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                id: \"submit-form\",\n                                className: \"hidden\",\n                                disabled: isLoading,\n                                children: \"Submit\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_MediaGallery__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showMediaGallery,\n                onClose: ()=>setShowMediaGallery(false),\n                onSelect: mediaGalleryMode === 'featured' ? handleFeaturedImageSelect : (imageUrl)=>handleContentImagesSelect([\n                        imageUrl\n                    ]),\n                onSelectMultiple: mediaGalleryMode === 'content' ? handleContentImagesSelect : undefined,\n                multiple: mediaGalleryMode === 'content',\n                selectedImage: mediaGalleryMode === 'featured' ? formData.featuredImage : undefined\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                lineNumber: 456,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, undefined);\n};\n_s(NewBlogPostPage, \"S6N1fUdVJgKd/xIb+sPj6NU/aQI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = NewBlogPostPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NewBlogPostPage);\nvar _c;\n$RefreshReg$(_c, \"NewBlogPostPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/blog/new/page.tsx\n"));

/***/ })

});