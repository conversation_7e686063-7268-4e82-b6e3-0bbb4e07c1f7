"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/blog/new/page",{

/***/ "(app-pages-browser)/./src/components/admin/SimpleMarkdownEditor.tsx":
/*!*******************************************************!*\
  !*** ./src/components/admin/SimpleMarkdownEditor.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst SimpleMarkdownEditor = (param)=>{\n    let { value, onChange, onImageInsert, placeholder = \"Начните писать...\", className = \"\" } = param;\n    _s();\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isPreview, setIsPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Функция для создания короткого ID изображения\n    const createImageShortcode = (imageUrl)=>{\n        // Создаем короткий ID из URL\n        const urlParts = imageUrl.split('/');\n        const filename = urlParts[urlParts.length - 1];\n        const shortId = filename.split('.')[0].slice(-8); // Последние 8 символов имени файла\n        return \"[img:\".concat(shortId, \"]\");\n    };\n    // Функция для вставки изображения в позицию курсора\n    const insertImageAtCursor = (imageUrl)=>{\n        if (textareaRef.current) {\n            const textarea = textareaRef.current;\n            const start = textarea.selectionStart;\n            const end = textarea.selectionEnd;\n            // Создаем шорткод вместо полного markdown\n            const imageShortcode = createImageShortcode(imageUrl);\n            // Сохраняем соответствие шорткода и URL в комментарии\n            const imageComment = \"<!-- \".concat(imageShortcode, \" = \").concat(imageUrl, \" -->\");\n            const beforeCursor = value.substring(0, start);\n            const afterCursor = value.substring(end);\n            // Добавляем переносы строк для красивого форматирования\n            const needsNewlineBefore = start > 0 && !beforeCursor.endsWith('\\n');\n            const needsNewlineAfter = afterCursor.length > 0 && !afterCursor.startsWith('\\n');\n            const newContent = beforeCursor + (needsNewlineBefore ? '\\n' : '') + imageShortcode + (needsNewlineAfter ? '\\n' : '') + afterCursor;\n            // Добавляем комментарий с соответствием в конец документа, если его еще нет\n            const finalContent = newContent.includes(imageComment) ? newContent : newContent + '\\n\\n' + imageComment;\n            onChange(finalContent);\n            // Устанавливаем курсор после вставленного шорткода\n            const newCursorPosition = start + (needsNewlineBefore ? 1 : 0) + imageShortcode.length;\n            setTimeout(()=>{\n                textarea.focus();\n                textarea.setSelectionRange(newCursorPosition, newCursorPosition);\n            }, 0);\n        }\n    };\n    // Глобальная функция для вставки изображений\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleMarkdownEditor.useEffect\": ()=>{\n            window.insertImageIntoEditor = insertImageAtCursor;\n            return ({\n                \"SimpleMarkdownEditor.useEffect\": ()=>{\n                    delete window.insertImageIntoEditor;\n                }\n            })[\"SimpleMarkdownEditor.useEffect\"];\n        }\n    }[\"SimpleMarkdownEditor.useEffect\"], [\n        value\n    ]);\n    // Функция для извлечения соответствий шорткодов и URL из комментариев\n    const extractImageMappings = (markdown)=>{\n        const mappings = {};\n        const commentRegex = /<!-- \\[img:([^\\]]+)\\] = ([^>]+) -->/g;\n        let match;\n        while((match = commentRegex.exec(markdown)) !== null){\n            mappings[\"[img:\".concat(match[1], \"]\")] = match[2];\n        }\n        return mappings;\n    };\n    // Функция для конвертации markdown в HTML для превью\n    const markdownToHtml = (markdown)=>{\n        const imageMappings = extractImageMappings(markdown);\n        let html = markdown// Убираем комментарии из отображения\n        .replace(/<!-- \\[img:[^\\]]+\\] = [^>]+ -->/g, '')// Обрабатываем шорткоды изображений\n        .replace(/\\[img:([^\\]]+)\\]/g, (match)=>{\n            const imageUrl = imageMappings[match];\n            return imageUrl ? '<div class=\"my-6\"><img src=\"'.concat(imageUrl, '\" alt=\"Изображение\" class=\"w-full max-w-2xl mx-auto rounded-lg shadow-md\" /></div>') : match;\n        })// Обрабатываем обычные markdown изображения\n        .replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, '<div class=\"my-6\"><img src=\"$2\" alt=\"$1\" class=\"w-full max-w-2xl mx-auto rounded-lg shadow-md\" /></div>')// Обрабатываем заголовки\n        .replace(/^### (.*$)/gim, '<h3 class=\"text-xl font-semibold text-gray-900 mb-3 mt-6\">$1</h3>').replace(/^## (.*$)/gim, '<h2 class=\"text-2xl font-semibold text-gray-900 mb-4 mt-8\">$1</h2>').replace(/^# (.*$)/gim, '<h1 class=\"text-3xl font-bold text-gray-900 mb-6\">$1</h1>')// Обрабатываем форматирование\n        .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>').replace(/\\*(.*?)\\*/g, '<em>$1</em>')// Обрабатываем параграфы\n        .replace(/\\n\\n/g, '</p><p class=\"text-gray-700 mb-4 leading-relaxed\">').replace(/^(.+)$/gm, '<p class=\"text-gray-700 mb-4 leading-relaxed\">$1</p>').replace(/<p class=\"text-gray-700 mb-4 leading-relaxed\"><\\/p>/g, '').replace(/<p class=\"text-gray-700 mb-4 leading-relaxed\">(<h[1-6][^>]*>.*<\\/h[1-6]>)<\\/p>/g, '$1').replace(/<p class=\"text-gray-700 mb-4 leading-relaxed\">(<div[^>]*>.*<\\/div>)<\\/p>/g, '$1');\n        return html;\n    };\n    const toolbarButtons = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                children: \"H1\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 130,\n                columnNumber: 13\n            }, undefined),\n            title: 'Заголовок 1',\n            action: ()=>insertText('# ')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                children: \"H2\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 135,\n                columnNumber: 13\n            }, undefined),\n            title: 'Заголовок 2',\n            action: ()=>insertText('## ')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                children: \"H3\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 140,\n                columnNumber: 13\n            }, undefined),\n            title: 'Заголовок 3',\n            action: ()=>insertText('### ')\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                children: \"B\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 146,\n                columnNumber: 13\n            }, undefined),\n            title: 'Жирный',\n            action: ()=>wrapText('**', '**')\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                children: \"I\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 151,\n                columnNumber: 13\n            }, undefined),\n            title: 'Курсив',\n            action: ()=>wrapText('*', '*')\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, undefined),\n            title: 'Изображение',\n            action: onImageInsert || (()=>{})\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, undefined),\n            title: isPreview ? 'Редактировать' : 'Превью',\n            action: ()=>setIsPreview(!isPreview)\n        }\n    ];\n    const insertText = (text)=>{\n        if (textareaRef.current) {\n            const textarea = textareaRef.current;\n            const start = textarea.selectionStart;\n            const end = textarea.selectionEnd;\n            const beforeCursor = value.substring(0, start);\n            const afterCursor = value.substring(end);\n            const newContent = beforeCursor + text + afterCursor;\n            onChange(newContent);\n            setTimeout(()=>{\n                textarea.focus();\n                textarea.setSelectionRange(start + text.length, start + text.length);\n            }, 0);\n        }\n    };\n    const wrapText = (before, after)=>{\n        if (textareaRef.current) {\n            const textarea = textareaRef.current;\n            const start = textarea.selectionStart;\n            const end = textarea.selectionEnd;\n            const selectedText = value.substring(start, end);\n            const beforeCursor = value.substring(0, start);\n            const afterCursor = value.substring(end);\n            const newContent = beforeCursor + before + selectedText + after + afterCursor;\n            onChange(newContent);\n            setTimeout(()=>{\n                textarea.focus();\n                if (selectedText) {\n                    textarea.setSelectionRange(start + before.length, end + before.length);\n                } else {\n                    textarea.setSelectionRange(start + before.length, start + before.length);\n                }\n            }, 0);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border border-gray-300 rounded-lg overflow-hidden \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 border-b border-gray-300 p-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap items-center gap-1\",\n                    children: toolbarButtons.map((button, index)=>{\n                        if (button.type === 'separator') {\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-px h-6 bg-gray-300 mx-1\"\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 22\n                            }, undefined);\n                        }\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: button.action,\n                            title: button.title,\n                            className: \"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors\",\n                            children: button.icon\n                        }, index, false, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, undefined),\n            isPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-[400px] p-4 prose prose-lg max-w-none bg-white\",\n                dangerouslySetInnerHTML: {\n                    __html: markdownToHtml(value)\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 248,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        ref: textareaRef,\n                        value: value,\n                        onChange: (e)=>onChange(e.target.value),\n                        placeholder: placeholder,\n                        className: \"w-full min-h-[400px] p-4 border-none resize-none focus:outline-none font-mono text-sm leading-relaxed relative z-10 bg-transparent\",\n                        style: {\n                            fontFamily: 'Monaco, Menlo, \"Ubuntu Mono\", monospace'\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 p-4 pointer-events-none font-mono text-sm leading-relaxed whitespace-pre-wrap break-words\",\n                        style: {\n                            fontFamily: 'Monaco, Menlo, \"Ubuntu Mono\", monospace',\n                            color: 'transparent'\n                        },\n                        children: value.replace(/\\[img:([^\\]]+)\\]/g, (match)=>{\n                            return \"\\uD83D\\uDDBC️ \".concat(match);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n                lineNumber: 253,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/components/admin/SimpleMarkdownEditor.tsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SimpleMarkdownEditor, \"2Khj9zGOxvkdc2HjTdSVtR+iHPI=\");\n_c = SimpleMarkdownEditor;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SimpleMarkdownEditor);\nvar _c;\n$RefreshReg$(_c, \"SimpleMarkdownEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/SimpleMarkdownEditor.tsx\n"));

/***/ })

});