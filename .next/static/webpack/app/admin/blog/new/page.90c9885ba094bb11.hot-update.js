"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/blog/new/page",{

/***/ "(app-pages-browser)/./src/app/admin/blog/new/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/admin/blog/new/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/admin/AdminLayout */ \"(app-pages-browser)/./src/components/admin/AdminLayout.tsx\");\n/* harmony import */ var _components_admin_MediaGallery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/admin/MediaGallery */ \"(app-pages-browser)/./src/components/admin/MediaGallery.tsx\");\n/* harmony import */ var _components_admin_SimpleMarkdownEditor__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/admin/SimpleMarkdownEditor */ \"(app-pages-browser)/./src/components/admin/SimpleMarkdownEditor.tsx\");\n/* harmony import */ var _lib_cms_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/cms-utils */ \"(app-pages-browser)/./src/lib/cms-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst NewBlogPostPage = ()=>{\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMediaGallery, setShowMediaGallery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mediaGalleryMode, setMediaGalleryMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('featured');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        slug: '',\n        excerpt: '',\n        content: '',\n        status: 'draft',\n        tags: '',\n        featuredImage: '',\n        contentImages: [],\n        seo: {\n            metaTitle: '',\n            metaDescription: '',\n            keywords: [],\n            ogTitle: '',\n            ogDescription: '',\n            ogImage: '',\n            ogType: 'article',\n            twitterTitle: '',\n            twitterDescription: '',\n            twitterImage: '',\n            twitterCard: 'summary_large_image',\n            canonicalUrl: '',\n            noIndex: false,\n            noFollow: false,\n            noArchive: false,\n            noSnippet: false,\n            schemaType: 'BlogPosting',\n            focusKeyword: '',\n            breadcrumbs: true\n        }\n    });\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Auto-generate slug from title\n        if (name === 'title' && !formData.slug) {\n            const slug = value.toLowerCase().replace(/[^a-z0-9а-я]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '');\n            setFormData((prev)=>({\n                    ...prev,\n                    slug\n                }));\n        }\n    };\n    const handleFeaturedImageSelect = (imageUrl)=>{\n        setFormData((prev)=>({\n                ...prev,\n                featuredImage: imageUrl\n            }));\n        setShowMediaGallery(false);\n    };\n    const handleContentImagesSelect = (imageUrls)=>{\n        if (mediaGalleryMode === 'content') {\n            // Вставляем изображения через глобальную функцию\n            imageUrls.forEach((imageUrl)=>{\n                if (window.insertImageIntoEditor) {\n                    window.insertImageIntoEditor(imageUrl);\n                }\n            });\n        } else {\n            // Legacy behavior for contentImages array\n            setFormData((prev)=>({\n                    ...prev,\n                    contentImages: imageUrls\n                }));\n        }\n        setShowMediaGallery(false);\n    };\n    const openMediaGallery = (mode)=>{\n        setMediaGalleryMode(mode);\n        setShowMediaGallery(true);\n    };\n    const removeContentImage = (imageUrl)=>{\n        setFormData((prev)=>({\n                ...prev,\n                contentImages: prev.contentImages.filter((url)=>url !== imageUrl)\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        try {\n            const postData = {\n                title: formData.title,\n                slug: formData.slug,\n                excerpt: formData.excerpt,\n                content: formData.content,\n                status: formData.status,\n                featuredImage: formData.featuredImage || undefined,\n                contentImages: formData.contentImages.length > 0 ? formData.contentImages : undefined,\n                tags: formData.tags.split(',').map((tag)=>tag.trim()).filter(Boolean),\n                type: 'blog',\n                category: 'general',\n                author: 'Admin',\n                seo: {\n                    ...formData.seo,\n                    metaTitle: formData.seo.metaTitle || formData.title,\n                    metaDescription: formData.seo.metaDescription || formData.excerpt,\n                    keywords: formData.seo.keywords.length > 0 ? formData.seo.keywords : formData.tags.split(',').map((tag)=>tag.trim()).filter(Boolean),\n                    ogTitle: formData.seo.ogTitle || formData.title,\n                    ogDescription: formData.seo.ogDescription || formData.excerpt,\n                    ogImage: formData.seo.ogImage || formData.featuredImage,\n                    twitterTitle: formData.seo.twitterTitle || formData.title,\n                    twitterDescription: formData.seo.twitterDescription || formData.excerpt,\n                    twitterImage: formData.seo.twitterImage || formData.featuredImage\n                }\n            };\n            const newPost = (0,_lib_cms_utils__WEBPACK_IMPORTED_MODULE_6__.createBlogPost)(postData);\n            router.push(\"/admin/blog/\".concat(newPost.id));\n        } catch (error) {\n            console.error('Ошибка при создании статьи:', error);\n            alert('Ошибка при создании статьи');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSaveDraft = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                status: 'draft'\n            }));\n        setTimeout(()=>{\n            var _document_getElementById;\n            (_document_getElementById = document.getElementById('submit-form')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n        }, 0);\n    };\n    const handlePublish = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                status: 'published'\n            }));\n        setTimeout(()=>{\n            var _document_getElementById;\n            (_document_getElementById = document.getElementById('submit-form')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n        }, 0);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 min-h-screen bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold text-gray-900\",\n                                            children: \"Новая статья\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-gray-600\",\n                                            children: \"Создание новой статьи для блога\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: handleSaveDraft,\n                                            disabled: isLoading,\n                                            className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\",\n                                            children: \"Сохранить черновик\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: handlePublish,\n                                            disabled: isLoading,\n                                            className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\",\n                                            children: \"Опубликовать\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:col-span-2 space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"title\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Заголовок статьи *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"title\",\n                                                            name: \"title\",\n                                                            required: true,\n                                                            value: formData.title,\n                                                            onChange: handleInputChange,\n                                                            className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                            placeholder: \"Введите заголовок статьи\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-4\",\n                                                            children: \"Содержание статьи *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_SimpleMarkdownEditor__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            value: formData.content,\n                                                            onChange: (content)=>setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        content\n                                                                    })),\n                                                            onImageInsert: ()=>openMediaGallery('content'),\n                                                            placeholder: \"Начните писать содержание статьи...\",\n                                                            className: \"min-h-[500px]\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"URL статьи\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"slug\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"URL (slug) *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"slug\",\n                                                                name: \"slug\",\n                                                                required: true,\n                                                                value: formData.slug,\n                                                                onChange: handleInputChange,\n                                                                className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                placeholder: \"url-статьи\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: [\n                                                                            \"URL статьи: /blog/\",\n                                                                            formData.slug || 'url-статьи'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 262,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    formData.status === 'published' && formData.slug && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                        href: \"/blog/\".concat(formData.slug),\n                                                                        target: \"_blank\",\n                                                                        rel: \"noopener noreferrer\",\n                                                                        className: \"inline-flex items-center px-3 py-1 mt-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-4 h-4 mr-1\",\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 2,\n                                                                                    d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                    lineNumber: 273,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                lineNumber: 272,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            \"Посмотреть статью\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 266,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"Краткое описание\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"excerpt\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Краткое описание *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                id: \"excerpt\",\n                                                                name: \"excerpt\",\n                                                                required: true,\n                                                                rows: 3,\n                                                                value: formData.excerpt,\n                                                                onChange: handleInputChange,\n                                                                className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                placeholder: \"Краткое описание статьи для превью\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"Изображение превью\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                formData.featuredImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative inline-block\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: formData.featuredImage,\n                                                                            alt: \"Featured\",\n                                                                            className: \"w-full h-32 object-cover rounded-lg border border-gray-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                            lineNumber: 309,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        featuredImage: ''\n                                                                                    })),\n                                                                            className: \"absolute -top-2 -right-2 bg-red-600 text-white rounded-full p-1 hover:bg-red-700\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-4 h-4\",\n                                                                                fill: \"currentColor\",\n                                                                                viewBox: \"0 0 20 20\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    fillRule: \"evenodd\",\n                                                                                    d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                                                                    clipRule: \"evenodd\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                    lineNumber: 320,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                lineNumber: 319,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                            lineNumber: 314,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                    lineNumber: 308,\n                                                                    columnNumber: 23\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"border-2 border-dashed border-gray-300 rounded-lg p-4 text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"mx-auto h-8 w-8 text-gray-400\",\n                                                                            stroke: \"currentColor\",\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 48 48\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\",\n                                                                                strokeWidth: 2,\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                lineNumber: 327,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                            lineNumber: 326,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"mt-2 text-sm text-gray-600\",\n                                                                            children: \"Изображение не выбрано\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                            lineNumber: 329,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                    lineNumber: 325,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>openMediaGallery('featured'),\n                                                                    className: \"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4 mr-2\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                lineNumber: 338,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                            lineNumber: 337,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        \"Выбрать изображение\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                    lineNumber: 332,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"Публикация\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"status\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Статус\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                id: \"status\",\n                                                                name: \"status\",\n                                                                value: formData.status,\n                                                                onChange: handleInputChange,\n                                                                className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"draft\",\n                                                                        children: \"Черновик\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 360,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"published\",\n                                                                        children: \"Опубликовано\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 361,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"Теги\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"tags\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Теги (через запятую)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"tags\",\n                                                                name: \"tags\",\n                                                                value: formData.tags,\n                                                                onChange: handleInputChange,\n                                                                className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                placeholder: \"тег1, тег2, тег3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 373,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"SEO\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"metaTitle\",\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Meta Title\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 390,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        id: \"metaTitle\",\n                                                                        name: \"metaTitle\",\n                                                                        value: formData.metaTitle,\n                                                                        onChange: handleInputChange,\n                                                                        className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                        placeholder: \"Заголовок для поисковых систем\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 393,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"metaDescription\",\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Meta Description\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 404,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                        id: \"metaDescription\",\n                                                                        name: \"metaDescription\",\n                                                                        rows: 3,\n                                                                        value: formData.metaDescription,\n                                                                        onChange: handleInputChange,\n                                                                        className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                        placeholder: \"Описание для поисковых систем\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 407,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                id: \"submit-form\",\n                                className: \"hidden\",\n                                disabled: isLoading,\n                                children: \"Submit\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_MediaGallery__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showMediaGallery,\n                onClose: ()=>setShowMediaGallery(false),\n                onSelect: mediaGalleryMode === 'featured' ? handleFeaturedImageSelect : (imageUrl)=>handleContentImagesSelect([\n                        imageUrl\n                    ]),\n                onSelectMultiple: mediaGalleryMode === 'content' ? handleContentImagesSelect : undefined,\n                multiple: mediaGalleryMode === 'content',\n                selectedImage: mediaGalleryMode === 'featured' ? formData.featuredImage : undefined,\n                mode: mediaGalleryMode\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                lineNumber: 435,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, undefined);\n};\n_s(NewBlogPostPage, \"9Y9JZpjXB2vXVjXtvKlyOuOMxPw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = NewBlogPostPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NewBlogPostPage);\nvar _c;\n$RefreshReg$(_c, \"NewBlogPostPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/blog/new/page.tsx\n"));

/***/ })

});