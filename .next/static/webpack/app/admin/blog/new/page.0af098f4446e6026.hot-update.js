"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/blog/new/page",{

/***/ "(app-pages-browser)/./src/app/admin/blog/new/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/admin/blog/new/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/admin/AdminLayout */ \"(app-pages-browser)/./src/components/admin/AdminLayout.tsx\");\n/* harmony import */ var _components_admin_MediaGallery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/admin/MediaGallery */ \"(app-pages-browser)/./src/components/admin/MediaGallery.tsx\");\n/* harmony import */ var _components_admin_SimpleMarkdownEditor__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/admin/SimpleMarkdownEditor */ \"(app-pages-browser)/./src/components/admin/SimpleMarkdownEditor.tsx\");\n/* harmony import */ var _lib_cms_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/cms-utils */ \"(app-pages-browser)/./src/lib/cms-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst NewBlogPostPage = ()=>{\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMediaGallery, setShowMediaGallery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mediaGalleryMode, setMediaGalleryMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('featured');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        slug: '',\n        excerpt: '',\n        content: '',\n        status: 'draft',\n        tags: '',\n        featuredImage: '',\n        contentImages: [],\n        metaTitle: '',\n        metaDescription: ''\n    });\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Auto-generate slug from title\n        if (name === 'title' && !formData.slug) {\n            const slug = value.toLowerCase().replace(/[^a-z0-9а-я]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '');\n            setFormData((prev)=>({\n                    ...prev,\n                    slug\n                }));\n        }\n    };\n    const handleFeaturedImageSelect = (imageUrl)=>{\n        setFormData((prev)=>({\n                ...prev,\n                featuredImage: imageUrl\n            }));\n        setShowMediaGallery(false);\n    };\n    const handleContentImagesSelect = (imageUrls)=>{\n        if (mediaGalleryMode === 'content') {\n            // Вставляем изображения через глобальную функцию\n            imageUrls.forEach((imageUrl)=>{\n                if (window.insertImageIntoEditor) {\n                    window.insertImageIntoEditor(imageUrl);\n                }\n            });\n        } else {\n            // Legacy behavior for contentImages array\n            setFormData((prev)=>({\n                    ...prev,\n                    contentImages: imageUrls\n                }));\n        }\n        setShowMediaGallery(false);\n    };\n    const openMediaGallery = (mode)=>{\n        setMediaGalleryMode(mode);\n        setShowMediaGallery(true);\n    };\n    const removeContentImage = (imageUrl)=>{\n        setFormData((prev)=>({\n                ...prev,\n                contentImages: prev.contentImages.filter((url)=>url !== imageUrl)\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        try {\n            const postData = {\n                title: formData.title,\n                slug: formData.slug,\n                excerpt: formData.excerpt,\n                content: formData.content,\n                status: formData.status,\n                featuredImage: formData.featuredImage || undefined,\n                contentImages: formData.contentImages.length > 0 ? formData.contentImages : undefined,\n                tags: formData.tags.split(',').map((tag)=>tag.trim()).filter(Boolean),\n                type: 'blog',\n                category: 'general',\n                author: 'Admin',\n                seo: {\n                    metaTitle: formData.metaTitle || formData.title,\n                    metaDescription: formData.metaDescription || formData.excerpt,\n                    keywords: formData.tags.split(',').map((tag)=>tag.trim()).filter(Boolean)\n                }\n            };\n            const newPost = (0,_lib_cms_utils__WEBPACK_IMPORTED_MODULE_6__.createBlogPost)(postData);\n            router.push(\"/admin/blog/\".concat(newPost.id));\n        } catch (error) {\n            console.error('Ошибка при создании статьи:', error);\n            alert('Ошибка при создании статьи');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSaveDraft = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                status: 'draft'\n            }));\n        setTimeout(()=>{\n            var _document_getElementById;\n            (_document_getElementById = document.getElementById('submit-form')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n        }, 0);\n    };\n    const handlePublish = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                status: 'published'\n            }));\n        setTimeout(()=>{\n            var _document_getElementById;\n            (_document_getElementById = document.getElementById('submit-form')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n        }, 0);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 min-h-screen bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold text-gray-900\",\n                                            children: \"Новая статья\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-gray-600\",\n                                            children: \"Создание новой статьи для блога\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: handleSaveDraft,\n                                            disabled: isLoading,\n                                            className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\",\n                                            children: \"Сохранить черновик\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: handlePublish,\n                                            disabled: isLoading,\n                                            className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\",\n                                            children: \"Опубликовать\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:col-span-2 space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"title\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Заголовок статьи *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"title\",\n                                                            name: \"title\",\n                                                            required: true,\n                                                            value: formData.title,\n                                                            onChange: handleInputChange,\n                                                            className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                            placeholder: \"Введите заголовок статьи\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-4\",\n                                                            children: \"Содержание статьи *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_SimpleMarkdownEditor__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            value: formData.content,\n                                                            onChange: (content)=>setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        content\n                                                                    })),\n                                                            onImageInsert: ()=>openMediaGallery('content'),\n                                                            placeholder: \"Начните писать содержание статьи...\",\n                                                            className: \"min-h-[500px]\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"URL статьи\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"slug\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"URL (slug) *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"slug\",\n                                                                name: \"slug\",\n                                                                required: true,\n                                                                value: formData.slug,\n                                                                onChange: handleInputChange,\n                                                                className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                placeholder: \"url-статьи\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: [\n                                                                            \"URL статьи: /blog/\",\n                                                                            formData.slug || 'url-статьи'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 235,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    formData.status === 'published' && formData.slug && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                        href: \"/blog/\".concat(formData.slug),\n                                                                        target: \"_blank\",\n                                                                        rel: \"noopener noreferrer\",\n                                                                        className: \"inline-flex items-center px-3 py-1 mt-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-4 h-4 mr-1\",\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 2,\n                                                                                    d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                    lineNumber: 246,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                lineNumber: 245,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            \"Посмотреть статью\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 239,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"Краткое описание\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"excerpt\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Краткое описание *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                id: \"excerpt\",\n                                                                name: \"excerpt\",\n                                                                required: true,\n                                                                rows: 3,\n                                                                value: formData.excerpt,\n                                                                onChange: handleInputChange,\n                                                                className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                placeholder: \"Краткое описание статьи для превью\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"Изображение превью\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                formData.featuredImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative inline-block\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: formData.featuredImage,\n                                                                            alt: \"Featured\",\n                                                                            className: \"w-full h-32 object-cover rounded-lg border border-gray-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                            lineNumber: 282,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        featuredImage: ''\n                                                                                    })),\n                                                                            className: \"absolute -top-2 -right-2 bg-red-600 text-white rounded-full p-1 hover:bg-red-700\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-4 h-4\",\n                                                                                fill: \"currentColor\",\n                                                                                viewBox: \"0 0 20 20\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    fillRule: \"evenodd\",\n                                                                                    d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                                                                    clipRule: \"evenodd\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                    lineNumber: 293,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                lineNumber: 292,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                            lineNumber: 287,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                    lineNumber: 281,\n                                                                    columnNumber: 23\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"border-2 border-dashed border-gray-300 rounded-lg p-4 text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"mx-auto h-8 w-8 text-gray-400\",\n                                                                            stroke: \"currentColor\",\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 48 48\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\",\n                                                                                strokeWidth: 2,\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                lineNumber: 300,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                            lineNumber: 299,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"mt-2 text-sm text-gray-600\",\n                                                                            children: \"Изображение не выбрано\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                            lineNumber: 302,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                    lineNumber: 298,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>openMediaGallery('featured'),\n                                                                    className: \"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4 mr-2\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                                lineNumber: 311,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                            lineNumber: 310,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        \"Выбрать изображение\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"Публикация\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"status\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Статус\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                id: \"status\",\n                                                                name: \"status\",\n                                                                value: formData.status,\n                                                                onChange: handleInputChange,\n                                                                className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"draft\",\n                                                                        children: \"Черновик\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 333,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"published\",\n                                                                        children: \"Опубликовано\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 334,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"Теги\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"tags\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Теги (через запятую)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"tags\",\n                                                                name: \"tags\",\n                                                                value: formData.tags,\n                                                                onChange: handleInputChange,\n                                                                className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                placeholder: \"тег1, тег2, тег3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"SEO\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"metaTitle\",\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Meta Title\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 363,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        id: \"metaTitle\",\n                                                                        name: \"metaTitle\",\n                                                                        value: formData.metaTitle,\n                                                                        onChange: handleInputChange,\n                                                                        className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                        placeholder: \"Заголовок для поисковых систем\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 366,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"metaDescription\",\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Meta Description\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 377,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                        id: \"metaDescription\",\n                                                                        name: \"metaDescription\",\n                                                                        rows: 3,\n                                                                        value: formData.metaDescription,\n                                                                        onChange: handleInputChange,\n                                                                        className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                                                                        placeholder: \"Описание для поисковых систем\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                        lineNumber: 380,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                id: \"submit-form\",\n                                className: \"hidden\",\n                                disabled: isLoading,\n                                children: \"Submit\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_MediaGallery__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showMediaGallery,\n                onClose: ()=>setShowMediaGallery(false),\n                onSelect: mediaGalleryMode === 'featured' ? handleFeaturedImageSelect : (imageUrl)=>handleContentImagesSelect([\n                        imageUrl\n                    ]),\n                onSelectMultiple: mediaGalleryMode === 'content' ? handleContentImagesSelect : undefined,\n                multiple: mediaGalleryMode === 'content',\n                selectedImage: mediaGalleryMode === 'featured' ? formData.featuredImage : undefined,\n                mode: mediaGalleryMode\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n                lineNumber: 408,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/app/admin/blog/new/page.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, undefined);\n};\n_s(NewBlogPostPage, \"S6N1fUdVJgKd/xIb+sPj6NU/aQI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = NewBlogPostPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NewBlogPostPage);\nvar _c;\n$RefreshReg$(_c, \"NewBlogPostPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/blog/new/page.tsx\n"));

/***/ })

});