"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/lib/cms-utils.ts":
/*!******************************!*\
  !*** ./src/lib/cms-utils.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateReadingTime: () => (/* binding */ calculateReadingTime),\n/* harmony export */   createBlogPost: () => (/* binding */ createBlogPost),\n/* harmony export */   createDefaultSEO: () => (/* binding */ createDefaultSEO),\n/* harmony export */   deleteBlogPost: () => (/* binding */ deleteBlogPost),\n/* harmony export */   deleteContent: () => (/* binding */ deleteContent),\n/* harmony export */   deleteMediaFile: () => (/* binding */ deleteMediaFile),\n/* harmony export */   exportContent: () => (/* binding */ exportContent),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   generateSlug: () => (/* binding */ generateSlug),\n/* harmony export */   getAllContent: () => (/* binding */ getAllContent),\n/* harmony export */   getBlogPost: () => (/* binding */ getBlogPost),\n/* harmony export */   getBlogPosts: () => (/* binding */ getBlogPosts),\n/* harmony export */   getContentById: () => (/* binding */ getContentById),\n/* harmony export */   getContentBySlug: () => (/* binding */ getContentBySlug),\n/* harmony export */   getMediaFiles: () => (/* binding */ getMediaFiles),\n/* harmony export */   getPages: () => (/* binding */ getPages),\n/* harmony export */   getPublishedContent: () => (/* binding */ getPublishedContent),\n/* harmony export */   getSettings: () => (/* binding */ getSettings),\n/* harmony export */   importContent: () => (/* binding */ importContent),\n/* harmony export */   isSlugUnique: () => (/* binding */ isSlugUnique),\n/* harmony export */   saveContent: () => (/* binding */ saveContent),\n/* harmony export */   saveMediaFile: () => (/* binding */ saveMediaFile),\n/* harmony export */   saveSettings: () => (/* binding */ saveSettings),\n/* harmony export */   searchContent: () => (/* binding */ searchContent),\n/* harmony export */   updateBlogPost: () => (/* binding */ updateBlogPost),\n/* harmony export */   uploadMediaFile: () => (/* binding */ uploadMediaFile),\n/* harmony export */   validateContent: () => (/* binding */ validateContent)\n/* harmony export */ });\n// CMS Utilities for Content Management\n// Generate slug from title\nconst generateSlug = (title)=>{\n    return title.toLowerCase().trim().replace(/[^\\w\\s-]/g, '') // Remove special characters\n    .replace(/[\\s_-]+/g, '-') // Replace spaces and underscores with hyphens\n    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens\n};\n// Generate unique ID\nconst generateId = ()=>{\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n};\n// Format date for display\nconst formatDate = (dateString)=>{\n    const date = new Date(dateString);\n    return date.toLocaleDateString('ru-RU', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n    });\n};\n// Calculate reading time for blog posts\nconst calculateReadingTime = (content)=>{\n    const wordsPerMinute = 200;\n    const words = content.trim().split(/\\s+/).length;\n    return Math.ceil(words / wordsPerMinute);\n};\n// Validate slug uniqueness\nconst isSlugUnique = async (slug, excludeId)=>{\n    // In a real implementation, this would check against a database\n    // For now, we'll simulate with localStorage\n    const existingContent = getAllContent();\n    return !existingContent.some((item)=>item.slug === slug && item.id !== excludeId);\n};\n// Get all content from storage (localStorage for now)\nconst getAllContent = ()=>{\n    if (false) {}\n    try {\n        const stored = localStorage.getItem('cms_content');\n        return stored ? JSON.parse(stored) : [];\n    } catch (e) {\n        return [];\n    }\n};\n// Save content to storage\nconst saveContent = (content)=>{\n    if (false) {}\n    const allContent = getAllContent();\n    const existingIndex = allContent.findIndex((item)=>item.id === content.id);\n    if (existingIndex >= 0) {\n        allContent[existingIndex] = content;\n    } else {\n        allContent.push(content);\n    }\n    localStorage.setItem('cms_content', JSON.stringify(allContent));\n};\n// Delete content from storage\nconst deleteContent = (id)=>{\n    if (false) {}\n    const allContent = getAllContent();\n    const filtered = allContent.filter((item)=>item.id !== id);\n    localStorage.setItem('cms_content', JSON.stringify(filtered));\n};\n// Get content by ID\nconst getContentById = (id)=>{\n    const allContent = getAllContent();\n    return allContent.find((item)=>item.id === id) || null;\n};\n// Get content by slug\nconst getContentBySlug = (slug)=>{\n    const allContent = getAllContent();\n    return allContent.find((item)=>item.slug === slug) || null;\n};\n// Get published content only\nconst getPublishedContent = ()=>{\n    return getAllContent().filter((item)=>item.status === 'published');\n};\n// Get blog posts only\nconst getBlogPosts = (status)=>{\n    const allContent = getAllContent();\n    let posts = allContent.filter((item)=>item.type === 'blog');\n    if (status) {\n        posts = posts.filter((post)=>post.status === status);\n    }\n    return posts.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());\n};\n// Get pages only\nconst getPages = (status)=>{\n    const allContent = getAllContent();\n    let pages = allContent.filter((item)=>item.type === 'page');\n    if (status) {\n        pages = pages.filter((page)=>page.status === status);\n    }\n    return pages.sort((a, b)=>new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());\n};\n// Create default SEO data\nconst createDefaultSEO = (title, description)=>{\n    return {\n        metaTitle: title,\n        metaDescription: description || \"\".concat(title, \" - Setmee\"),\n        keywords: [\n            'Kommo',\n            'CRM',\n            'автоматизация',\n            'бизнес'\n        ],\n        ogTitle: title,\n        ogDescription: description || \"\".concat(title, \" - Setmee\"),\n        noIndex: false,\n        noFollow: false\n    };\n};\n// Validate content data\nconst validateContent = (content)=>{\n    var _content_title, _content_slug;\n    const errors = [];\n    if (!((_content_title = content.title) === null || _content_title === void 0 ? void 0 : _content_title.trim())) {\n        errors.push('Заголовок обязателен');\n    }\n    if (!((_content_slug = content.slug) === null || _content_slug === void 0 ? void 0 : _content_slug.trim())) {\n        errors.push('URL slug обязателен');\n    } else if (!/^[a-z0-9-]+$/.test(content.slug)) {\n        errors.push('URL slug может содержать только строчные буквы, цифры и дефисы');\n    }\n    if (content.type === 'blog') {\n        var _blogPost_content, _blogPost_excerpt, _blogPost_category;\n        const blogPost = content;\n        if (!((_blogPost_content = blogPost.content) === null || _blogPost_content === void 0 ? void 0 : _blogPost_content.trim())) {\n            errors.push('Содержание статьи обязательно');\n        }\n        if (!((_blogPost_excerpt = blogPost.excerpt) === null || _blogPost_excerpt === void 0 ? void 0 : _blogPost_excerpt.trim())) {\n            errors.push('Краткое описание обязательно');\n        }\n        if (!((_blogPost_category = blogPost.category) === null || _blogPost_category === void 0 ? void 0 : _blogPost_category.trim())) {\n            errors.push('Категория обязательна');\n        }\n    }\n    if (content.seo) {\n        var _content_seo_metaTitle, _content_seo_metaDescription;\n        if (!((_content_seo_metaTitle = content.seo.metaTitle) === null || _content_seo_metaTitle === void 0 ? void 0 : _content_seo_metaTitle.trim())) {\n            errors.push('Meta title обязателен');\n        }\n        if (!((_content_seo_metaDescription = content.seo.metaDescription) === null || _content_seo_metaDescription === void 0 ? void 0 : _content_seo_metaDescription.trim())) {\n            errors.push('Meta description обязательно');\n        }\n        if (content.seo.metaDescription && content.seo.metaDescription.length > 160) {\n            errors.push('Meta description не должно превышать 160 символов');\n        }\n    }\n    return errors;\n};\n// Export content as JSON\nconst exportContent = ()=>{\n    const allContent = getAllContent();\n    return JSON.stringify(allContent, null, 2);\n};\n// Import content from JSON\nconst importContent = (jsonData)=>{\n    try {\n        const content = JSON.parse(jsonData);\n        if (Array.isArray(content)) {\n            localStorage.setItem('cms_content', JSON.stringify(content));\n            return true;\n        }\n        return false;\n    } catch (e) {\n        return false;\n    }\n};\n// Search content\nconst searchContent = (query)=>{\n    if (!query.trim()) return [];\n    const allContent = getAllContent();\n    const searchTerm = query.toLowerCase();\n    return allContent.filter((item)=>item.title.toLowerCase().includes(searchTerm) || item.seo.metaDescription.toLowerCase().includes(searchTerm) || item.type === 'blog' && item.content.toLowerCase().includes(searchTerm));\n};\n// Get single blog post\nconst getBlogPost = (id)=>{\n    const posts = getBlogPosts();\n    return posts.find((post)=>post.id === id) || null;\n};\n// Create new blog post\nconst createBlogPost = (postData)=>{\n    const newPost = {\n        ...postData,\n        id: generateId(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    };\n    saveContent(newPost);\n    return newPost;\n};\n// Update blog post\nconst updateBlogPost = (id, updates)=>{\n    const post = getBlogPost(id);\n    if (!post) return null;\n    const updatedPost = {\n        ...post,\n        ...updates,\n        updatedAt: new Date().toISOString()\n    };\n    saveContent(updatedPost);\n    return updatedPost;\n};\n// Delete blog post\nconst deleteBlogPost = (id)=>{\n    const post = getBlogPost(id);\n    if (!post) return false;\n    deleteContent(id);\n    return true;\n};\n// Settings management\nconst getSettings = ()=>{\n    if (false) {}\n    try {\n        const settings = localStorage.getItem('site_settings');\n        return settings ? JSON.parse(settings) : {\n            siteName: 'Setmee',\n            siteDescription: 'Профессиональная интеграция Kommo CRM',\n            siteUrl: 'https://setmee.ru',\n            contactEmail: '<EMAIL>',\n            blogEnabled: true,\n            commentsEnabled: false,\n            postsPerPage: 10,\n            metaTitle: 'Setmee - Kommo Partner',\n            metaDescription: 'Профессиональная интеграция и настройка Kommo CRM для вашего бизнеса',\n            googleAnalytics: '',\n            yandexMetrica: '',\n            blogHeroTitle: 'Блог Setmee',\n            blogHeroDescription: 'Полезные статьи о Kommo CRM, интеграциях, автоматизации бизнеса и лучших практиках',\n            blogHeroBackgroundColor: '#1e40af'\n        };\n    } catch (error) {\n        console.error('Error loading settings:', error);\n        return {};\n    }\n};\nconst saveSettings = (settings)=>{\n    if (false) {}\n    try {\n        localStorage.setItem('site_settings', JSON.stringify(settings));\n        return true;\n    } catch (error) {\n        console.error('Error saving settings:', error);\n        return false;\n    }\n};\n// Media management\nconst getMediaFiles = ()=>{\n    if (false) {}\n    try {\n        const files = localStorage.getItem('media_files');\n        return files ? JSON.parse(files) : [];\n    } catch (error) {\n        console.error('Error loading media files:', error);\n        return [];\n    }\n};\nconst uploadMediaFile = (file)=>{\n    return new Promise((resolve, reject)=>{\n        if (false) {}\n        const reader = new FileReader();\n        reader.onload = ()=>{\n            try {\n                const mediaFile = {\n                    id: generateId(),\n                    name: file.name.replace(/[^a-zA-Z0-9.-]/g, '_'),\n                    originalName: file.name,\n                    size: file.size,\n                    type: file.type,\n                    url: reader.result,\n                    uploadedAt: new Date().toISOString()\n                };\n                const existingFiles = getMediaFiles();\n                existingFiles.push(mediaFile);\n                localStorage.setItem('media_files', JSON.stringify(existingFiles));\n                resolve(mediaFile);\n            } catch (error) {\n                reject(error);\n            }\n        };\n        reader.onerror = ()=>{\n            reject(new Error('Failed to read file'));\n        };\n        reader.readAsDataURL(file);\n    });\n};\nconst saveMediaFile = (mediaFile)=>{\n    if (false) {}\n    try {\n        const existingFiles = getMediaFiles();\n        existingFiles.push(mediaFile);\n        localStorage.setItem('media_files', JSON.stringify(existingFiles));\n        return true;\n    } catch (error) {\n        console.error('Error saving media file:', error);\n        return false;\n    }\n};\nconst deleteMediaFile = (id)=>{\n    if (false) {}\n    try {\n        const files = getMediaFiles();\n        const filteredFiles = files.filter((file)=>file.id !== id);\n        if (filteredFiles.length === files.length) return false;\n        localStorage.setItem('media_files', JSON.stringify(filteredFiles));\n        return true;\n    } catch (error) {\n        console.error('Error deleting media file:', error);\n        return false;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/cms-utils.ts\n"));

/***/ })

});