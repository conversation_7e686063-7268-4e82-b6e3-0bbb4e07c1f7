"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/form-service.ts":
/*!*********************************!*\
  !*** ./src/lib/form-service.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   submitContactForm: () => (/* binding */ submitContactForm),\n/* harmony export */   submitEmailSubscription: () => (/* binding */ submitEmailSubscription)\n/* harmony export */ });\n// Simulated form submission service\n// In production, replace with actual email service (Formspree, EmailJS, etc.)\nconst submitContactForm = async (data)=>{\n    try {\n        // Simulate API call delay\n        await new Promise((resolve)=>setTimeout(resolve, 1500));\n        // Log form data (in production, send to email service)\n        console.log('Contact form submitted:', data);\n        // Simulate success response\n        return {\n            success: true,\n            message: 'Thank you for your message! We will get back to you within 24 hours.'\n        };\n    } catch (error) {\n        console.error('Form submission error:', error);\n        return {\n            success: false,\n            message: 'Sorry, there was an error sending your message. Please try again.'\n        };\n    }\n};\nconst submitEmailSubscription = async (data)=>{\n    try {\n        // Simulate API call delay\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        // Log subscription data (in production, send to email service)\n        console.log('Email subscription:', data);\n        // Simulate success response\n        return {\n            success: true,\n            message: 'Thank you for subscribing! We will send you the presentation shortly.'\n        };\n    } catch (error) {\n        console.error('Subscription error:', error);\n        return {\n            success: false,\n            message: 'Sorry, there was an error with your subscription. Please try again.'\n        };\n    }\n}; // Example integration with Formspree (commented out)\n /*\nexport const submitContactFormFormspree = async (data: ContactFormData) => {\n  const response = await fetch('https://formspree.io/f/YOUR_FORM_ID', {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify(data),\n  });\n  \n  if (!response.ok) {\n    throw new Error('Failed to submit form');\n  }\n  \n  return response.json();\n};\n*/  // Example integration with EmailJS (commented out)\n /*\nimport emailjs from '@emailjs/browser';\n\nexport const submitContactFormEmailJS = async (data: ContactFormData) => {\n  const result = await emailjs.send(\n    'YOUR_SERVICE_ID',\n    'YOUR_TEMPLATE_ID',\n    {\n      from_name: data.name,\n      from_email: data.email,\n      phone: data.phone,\n      company: data.company,\n      message: data.message,\n    },\n    'YOUR_PUBLIC_KEY'\n  );\n  \n  return result;\n};\n*/ \n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/form-service.ts\n"));

/***/ })

});