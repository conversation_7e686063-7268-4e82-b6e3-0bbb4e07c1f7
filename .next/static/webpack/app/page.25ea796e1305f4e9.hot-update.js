"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/config/forms.ts":
/*!*****************************!*\
  !*** ./src/config/forms.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FORM_CONFIGS: () => (/* binding */ FORM_CONFIGS),\n/* harmony export */   getFormConfig: () => (/* binding */ getFormConfig),\n/* harmony export */   isWebhookConfigured: () => (/* binding */ isWebhookConfigured)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Form configuration for different types of forms and their Make.com webhooks\nconst FORM_CONFIGS = {\n    // Основная контактная форма\n    contact: {\n        webhookUrl: process.env.NEXT_PUBLIC_MAKE_CONTACT_WEBHOOK,\n        successMessage: 'Thank you for your message! We will get back to you within 24 hours.',\n        errorMessage: 'Sorry, there was an error sending your message. Please try again.',\n        fields: [\n            'name',\n            'email',\n            'phone',\n            'company',\n            'message'\n        ],\n        type: 'contact'\n    },\n    // Подписка на новости/презентацию\n    newsletter: {\n        webhookUrl: process.env.NEXT_PUBLIC_MAKE_NEWSLETTER_WEBHOOK,\n        successMessage: 'Thank you for subscribing! We will send you the presentation shortly.',\n        errorMessage: 'Sorry, there was an error with your subscription. Please try again.',\n        fields: [\n            'email'\n        ],\n        type: 'newsletter'\n    },\n    // Запрос демо\n    demo: {\n        webhookUrl: process.env.NEXT_PUBLIC_MAKE_DEMO_WEBHOOK,\n        successMessage: 'Demo request sent! We will contact you soon to schedule a presentation.',\n        errorMessage: 'Sorry, there was an error sending your demo request. Please try again.',\n        fields: [\n            'name',\n            'email',\n            'company',\n            'phone'\n        ],\n        type: 'demo'\n    },\n    // Запрос аудита\n    audit: {\n        webhookUrl: process.env.NEXT_PUBLIC_MAKE_AUDIT_WEBHOOK,\n        successMessage: 'Audit request received! We will prepare your report and contact you within 2 business days.',\n        errorMessage: 'Sorry, there was an error sending your audit request. Please try again.',\n        fields: [\n            'name',\n            'email',\n            'website',\n            'company',\n            'currentCrm'\n        ],\n        type: 'audit'\n    },\n    // Консультация эксперта\n    consultation: {\n        webhookUrl: process.env.NEXT_PUBLIC_MAKE_CONSULTATION_WEBHOOK,\n        successMessage: 'Consultation request sent! Our expert will contact you within 24 hours.',\n        errorMessage: 'Sorry, there was an error sending your consultation request. Please try again.',\n        fields: [\n            'name',\n            'email',\n            'phone',\n            'company',\n            'challenge'\n        ],\n        type: 'consultation'\n    }\n};\n// Получить конфигурацию формы по типу\nconst getFormConfig = (formType)=>{\n    const config = FORM_CONFIGS[formType];\n    if (!config) {\n        throw new Error(\"Form configuration not found for type: \".concat(formType));\n    }\n    return config;\n};\n// Проверить, настроен ли webhook для формы\nconst isWebhookConfigured = (formType)=>{\n    const config = getFormConfig(formType);\n    return !!config.webhookUrl;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/config/forms.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/form-service.ts":
/*!*********************************!*\
  !*** ./src/lib/form-service.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   submitContactForm: () => (/* binding */ submitContactForm),\n/* harmony export */   submitEmailSubscription: () => (/* binding */ submitEmailSubscription),\n/* harmony export */   submitForm: () => (/* binding */ submitForm)\n/* harmony export */ });\n/* harmony import */ var _config_forms__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config/forms */ \"(app-pages-browser)/./src/config/forms.ts\");\n\n// Universal form submission service with Make.com webhook integration\n// Supports multiple form types with different webhook endpoints\n// Generic webhook sender\nconst sendToMakeWebhook = async (webhookUrl, data)=>{\n    const response = await fetch(webhookUrl, {\n        method: 'POST',\n        headers: {\n            'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n            ...data,\n            timestamp: new Date().toISOString(),\n            source: 'setmee-website',\n            userAgent:  true ? window.navigator.userAgent : 0,\n            referrer:  true ? window.document.referrer : 0\n        })\n    });\n    if (!response.ok) {\n        throw new Error(\"Webhook failed: \".concat(response.status, \" \").concat(response.statusText));\n    }\n    return response;\n};\n// Universal form submission function\nconst submitForm = async (formType, data)=>{\n    try {\n        const config = (0,_config_forms__WEBPACK_IMPORTED_MODULE_0__.getFormConfig)(formType);\n        if (config.webhookUrl && (0,_config_forms__WEBPACK_IMPORTED_MODULE_0__.isWebhookConfigured)(formType)) {\n            // Send to Make.com webhook\n            await sendToMakeWebhook(config.webhookUrl, {\n                formType,\n                ...data\n            });\n            console.log(\"\".concat(formType, \" form sent to Make webhook:\"), data.email || data.name);\n        } else {\n            // Fallback: log to console if no webhook configured\n            console.log(\"\".concat(formType, \" form submitted (no webhook configured):\"), data);\n        }\n        return {\n            success: true,\n            message: config.successMessage\n        };\n    } catch (error) {\n        console.error(\"\".concat(formType, \" form submission error:\"), error);\n        const config = (0,_config_forms__WEBPACK_IMPORTED_MODULE_0__.getFormConfig)(formType);\n        return {\n            success: false,\n            message: config.errorMessage\n        };\n    }\n};\n// Specific form submission functions (backward compatibility)\nconst submitContactForm = async (data)=>{\n    return submitForm('contact', data);\n};\nconst submitEmailSubscription = async (data)=>{\n    try {\n        // Simulate API call delay\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        // Log subscription data (in production, send to email service)\n        console.log('Email subscription:', data);\n        // Simulate success response\n        return {\n            success: true,\n            message: 'Thank you for subscribing! We will send you the presentation shortly.'\n        };\n    } catch (error) {\n        console.error('Subscription error:', error);\n        return {\n            success: false,\n            message: 'Sorry, there was an error with your subscription. Please try again.'\n        };\n    }\n}; // Example integration with Formspree (commented out)\n /*\nexport const submitContactFormFormspree = async (data: ContactFormData) => {\n  const response = await fetch('https://formspree.io/f/YOUR_FORM_ID', {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify(data),\n  });\n  \n  if (!response.ok) {\n    throw new Error('Failed to submit form');\n  }\n  \n  return response.json();\n};\n*/  // Example integration with EmailJS (commented out)\n /*\nimport emailjs from '@emailjs/browser';\n\nexport const submitContactFormEmailJS = async (data: ContactFormData) => {\n  const result = await emailjs.send(\n    'YOUR_SERVICE_ID',\n    'YOUR_TEMPLATE_ID',\n    {\n      from_name: data.name,\n      from_email: data.email,\n      phone: data.phone,\n      company: data.company,\n      message: data.message,\n    },\n    'YOUR_PUBLIC_KEY'\n  );\n  \n  return result;\n};\n*/ \n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/form-service.ts\n"));

/***/ })

});