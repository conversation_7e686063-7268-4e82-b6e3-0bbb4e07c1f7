"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/form-service.ts":
/*!*********************************!*\
  !*** ./src/lib/form-service.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   submitAuditRequest: () => (/* binding */ submitAuditRequest),\n/* harmony export */   submitConsultationRequest: () => (/* binding */ submitConsultationRequest),\n/* harmony export */   submitContactForm: () => (/* binding */ submitContactForm),\n/* harmony export */   submitDemoRequest: () => (/* binding */ submitDemoRequest),\n/* harmony export */   submitEmailSubscription: () => (/* binding */ submitEmailSubscription),\n/* harmony export */   submitForm: () => (/* binding */ submitForm)\n/* harmony export */ });\n/* harmony import */ var _config_forms__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config/forms */ \"(app-pages-browser)/./src/config/forms.ts\");\n\n// Universal form submission service with Make.com webhook integration\n// Supports multiple form types with different webhook endpoints\n// Generic webhook sender\nconst sendToMakeWebhook = async (webhookUrl, data)=>{\n    console.log('🚀 Sending to webhook:', webhookUrl);\n    console.log('📦 Payload:', JSON.stringify(data, null, 2));\n    const response = await fetch(webhookUrl, {\n        method: 'POST',\n        headers: {\n            'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(data)\n    });\n    console.log('📡 Response status:', response.status);\n    console.log('📡 Response ok:', response.ok);\n    if (!response.ok) {\n        const errorText = await response.text();\n        console.error('❌ Webhook error response:', errorText);\n        throw new Error(\"Webhook failed: \".concat(response.status, \" \").concat(response.statusText, \" - \").concat(errorText));\n    }\n    return response;\n};\n// Universal form submission function\nconst submitForm = async (formType, data)=>{\n    try {\n        const config = (0,_config_forms__WEBPACK_IMPORTED_MODULE_0__.getFormConfig)(formType);\n        // Debug logging\n        console.log('🔍 Form submission debug:', {\n            formType,\n            webhookUrl: config.webhookUrl,\n            isConfigured: (0,_config_forms__WEBPACK_IMPORTED_MODULE_0__.isWebhookConfigured)(formType),\n            data: data\n        });\n        if (config.webhookUrl && (0,_config_forms__WEBPACK_IMPORTED_MODULE_0__.isWebhookConfigured)(formType)) {\n            // Send to Make.com webhook\n            await sendToMakeWebhook(config.webhookUrl, {\n                formType,\n                timestamp: new Date().toISOString(),\n                source: 'setmee-website',\n                userAgent:  true ? window.navigator.userAgent : 0,\n                referrer:  true ? window.document.referrer : 0,\n                ...data\n            });\n            console.log(\"\".concat(formType, \" form sent to Make webhook:\"), data.email || data.name);\n        } else {\n            // Fallback: log to console if no webhook configured\n            console.log(\"\".concat(formType, \" form submitted (no webhook configured):\"), data);\n        }\n        return {\n            success: true,\n            message: config.successMessage\n        };\n    } catch (error) {\n        console.error(\"\".concat(formType, \" form submission error:\"), error);\n        const config = (0,_config_forms__WEBPACK_IMPORTED_MODULE_0__.getFormConfig)(formType);\n        return {\n            success: false,\n            message: config.errorMessage\n        };\n    }\n};\n// Specific form submission functions (backward compatibility)\nconst submitContactForm = async (data)=>{\n    return submitForm('contact', data);\n};\nconst submitEmailSubscription = async (data)=>{\n    return submitForm('newsletter', data);\n};\n// Additional form submission functions for different types\nconst submitDemoRequest = async (data)=>{\n    return submitForm('demo', data);\n};\nconst submitAuditRequest = async (data)=>{\n    return submitForm('audit', data);\n};\nconst submitConsultationRequest = async (data)=>{\n    return submitForm('consultation', data);\n}; // Example integration with Formspree (commented out)\n /*\nexport const submitContactFormFormspree = async (data: ContactFormData) => {\n  const response = await fetch('https://formspree.io/f/YOUR_FORM_ID', {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify(data),\n  });\n  \n  if (!response.ok) {\n    throw new Error('Failed to submit form');\n  }\n  \n  return response.json();\n};\n*/  // Example integration with EmailJS (commented out)\n /*\nimport emailjs from '@emailjs/browser';\n\nexport const submitContactFormEmailJS = async (data: ContactFormData) => {\n  const result = await emailjs.send(\n    'YOUR_SERVICE_ID',\n    'YOUR_TEMPLATE_ID',\n    {\n      from_name: data.name,\n      from_email: data.email,\n      phone: data.phone,\n      company: data.company,\n      message: data.message,\n    },\n    'YOUR_PUBLIC_KEY'\n  );\n  \n  return result;\n};\n*/ \n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvZm9ybS1zZXJ2aWNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFRb0U7QUFFcEUsc0VBQXNFO0FBQ3RFLGdFQUFnRTtBQUVoRSx5QkFBeUI7QUFDekIsTUFBTUUsb0JBQW9CLE9BQU9DLFlBQW9CQztJQUNuREMsUUFBUUMsR0FBRyxDQUFDLDBCQUEwQkg7SUFDdENFLFFBQVFDLEdBQUcsQ0FBQyxlQUFlQyxLQUFLQyxTQUFTLENBQUNKLE1BQU0sTUFBTTtJQUV0RCxNQUFNSyxXQUFXLE1BQU1DLE1BQU1QLFlBQVk7UUFDdkNRLFFBQVE7UUFDUkMsU0FBUztZQUNQLGdCQUFnQjtRQUNsQjtRQUNBQyxNQUFNTixLQUFLQyxTQUFTLENBQUNKO0lBQ3ZCO0lBRUFDLFFBQVFDLEdBQUcsQ0FBQyx1QkFBdUJHLFNBQVNLLE1BQU07SUFDbERULFFBQVFDLEdBQUcsQ0FBQyxtQkFBbUJHLFNBQVNNLEVBQUU7SUFFMUMsSUFBSSxDQUFDTixTQUFTTSxFQUFFLEVBQUU7UUFDaEIsTUFBTUMsWUFBWSxNQUFNUCxTQUFTUSxJQUFJO1FBQ3JDWixRQUFRYSxLQUFLLENBQUMsNkJBQTZCRjtRQUMzQyxNQUFNLElBQUlHLE1BQU0sbUJBQXNDVixPQUFuQkEsU0FBU0ssTUFBTSxFQUFDLEtBQTRCRSxPQUF6QlAsU0FBU1csVUFBVSxFQUFDLE9BQWUsT0FBVko7SUFDakY7SUFFQSxPQUFPUDtBQUNUO0FBRUEscUNBQXFDO0FBQzlCLE1BQU1ZLGFBQWEsT0FDeEJDLFVBQ0FsQjtJQUVBLElBQUk7UUFDRixNQUFNbUIsU0FBU3ZCLDREQUFhQSxDQUFDc0I7UUFFN0IsZ0JBQWdCO1FBQ2hCakIsUUFBUUMsR0FBRyxDQUFDLDZCQUE2QjtZQUN2Q2dCO1lBQ0FuQixZQUFZb0IsT0FBT3BCLFVBQVU7WUFDN0JxQixjQUFjdkIsa0VBQW1CQSxDQUFDcUI7WUFDbENsQixNQUFNQTtRQUNSO1FBRUEsSUFBSW1CLE9BQU9wQixVQUFVLElBQUlGLGtFQUFtQkEsQ0FBQ3FCLFdBQVc7WUFDdEQsMkJBQTJCO1lBQzNCLE1BQU1wQixrQkFBa0JxQixPQUFPcEIsVUFBVSxFQUFFO2dCQUN6Q21CO2dCQUNBRyxXQUFXLElBQUlDLE9BQU9DLFdBQVc7Z0JBQ2pDQyxRQUFRO2dCQUNSQyxXQUFXLEtBQTZCLEdBQUdDLE9BQU9DLFNBQVMsQ0FBQ0YsU0FBUyxHQUFHLENBQVE7Z0JBQ2hGRyxVQUFVLEtBQTZCLEdBQUdGLE9BQU9HLFFBQVEsQ0FBQ0QsUUFBUSxHQUFHLENBQUU7Z0JBQ3ZFLEdBQUc1QixJQUFJO1lBQ1Q7WUFFQUMsUUFBUUMsR0FBRyxDQUFDLEdBQVksT0FBVGdCLFVBQVMsZ0NBQThCbEIsS0FBSzhCLEtBQUssSUFBSTlCLEtBQUsrQixJQUFJO1FBQy9FLE9BQU87WUFDTCxvREFBb0Q7WUFDcEQ5QixRQUFRQyxHQUFHLENBQUMsR0FBWSxPQUFUZ0IsVUFBUyw2Q0FBMkNsQjtRQUNyRTtRQUVBLE9BQU87WUFDTGdDLFNBQVM7WUFDVEMsU0FBU2QsT0FBT2UsY0FBYztRQUNoQztJQUNGLEVBQUUsT0FBT3BCLE9BQU87UUFDZGIsUUFBUWEsS0FBSyxDQUFDLEdBQVksT0FBVEksVUFBUyw0QkFBMEJKO1FBQ3BELE1BQU1LLFNBQVN2Qiw0REFBYUEsQ0FBQ3NCO1FBQzdCLE9BQU87WUFDTGMsU0FBUztZQUNUQyxTQUFTZCxPQUFPZ0IsWUFBWTtRQUM5QjtJQUNGO0FBQ0YsRUFBRTtBQUVGLDhEQUE4RDtBQUN2RCxNQUFNQyxvQkFBb0IsT0FBT3BDO0lBQ3RDLE9BQU9pQixXQUFXLFdBQVdqQjtBQUMvQixFQUFFO0FBRUssTUFBTXFDLDBCQUEwQixPQUFPckM7SUFDNUMsT0FBT2lCLFdBQVcsY0FBY2pCO0FBQ2xDLEVBQUU7QUFFRiwyREFBMkQ7QUFDcEQsTUFBTXNDLG9CQUFvQixPQUFPdEM7SUFDdEMsT0FBT2lCLFdBQVcsUUFBUWpCO0FBQzVCLEVBQUU7QUFFSyxNQUFNdUMscUJBQXFCLE9BQU92QztJQUN2QyxPQUFPaUIsV0FBVyxTQUFTakI7QUFDN0IsRUFBRTtBQUVLLE1BQU13Qyw0QkFBNEIsT0FBT3hDO0lBQzlDLE9BQU9pQixXQUFXLGdCQUFnQmpCO0FBQ3BDLEVBQUUsQ0FFRixxREFBcUQ7Q0FDckQ7Ozs7Ozs7Ozs7Ozs7Ozs7QUFnQkEsSUFFQSxtREFBbUQ7Q0FDbkQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFtQkEiLCJzb3VyY2VzIjpbIi9Vc2Vycy92aXRhbGlpbC9Eb2N1bWVudHMvU2V0TWVlL3NyYy9saWIvZm9ybS1zZXJ2aWNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7XG4gIENvbnRhY3RGb3JtRGF0YSxcbiAgRW1haWxTdWJzY3JpcHRpb25EYXRhLFxuICBEZW1vUmVxdWVzdERhdGEsXG4gIEF1ZGl0UmVxdWVzdERhdGEsXG4gIENvbnN1bHRhdGlvblJlcXVlc3REYXRhLFxuICBXZWJob29rUGF5bG9hZFxufSBmcm9tICcuL3ZhbGlkYXRpb25zJztcbmltcG9ydCB7IGdldEZvcm1Db25maWcsIGlzV2ViaG9va0NvbmZpZ3VyZWQgfSBmcm9tICdAL2NvbmZpZy9mb3Jtcyc7XG5cbi8vIFVuaXZlcnNhbCBmb3JtIHN1Ym1pc3Npb24gc2VydmljZSB3aXRoIE1ha2UuY29tIHdlYmhvb2sgaW50ZWdyYXRpb25cbi8vIFN1cHBvcnRzIG11bHRpcGxlIGZvcm0gdHlwZXMgd2l0aCBkaWZmZXJlbnQgd2ViaG9vayBlbmRwb2ludHNcblxuLy8gR2VuZXJpYyB3ZWJob29rIHNlbmRlclxuY29uc3Qgc2VuZFRvTWFrZVdlYmhvb2sgPSBhc3luYyAod2ViaG9va1VybDogc3RyaW5nLCBkYXRhOiBXZWJob29rUGF5bG9hZCk6IFByb21pc2U8UmVzcG9uc2U+ID0+IHtcbiAgY29uc29sZS5sb2coJ/CfmoAgU2VuZGluZyB0byB3ZWJob29rOicsIHdlYmhvb2tVcmwpO1xuICBjb25zb2xlLmxvZygn8J+TpiBQYXlsb2FkOicsIEpTT04uc3RyaW5naWZ5KGRhdGEsIG51bGwsIDIpKTtcblxuICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKHdlYmhvb2tVcmwsIHtcbiAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICBoZWFkZXJzOiB7XG4gICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgIH0sXG4gICAgYm9keTogSlNPTi5zdHJpbmdpZnkoZGF0YSksXG4gIH0pO1xuXG4gIGNvbnNvbGUubG9nKCfwn5OhIFJlc3BvbnNlIHN0YXR1czonLCByZXNwb25zZS5zdGF0dXMpO1xuICBjb25zb2xlLmxvZygn8J+ToSBSZXNwb25zZSBvazonLCByZXNwb25zZS5vayk7XG5cbiAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgIGNvbnN0IGVycm9yVGV4dCA9IGF3YWl0IHJlc3BvbnNlLnRleHQoKTtcbiAgICBjb25zb2xlLmVycm9yKCfinYwgV2ViaG9vayBlcnJvciByZXNwb25zZTonLCBlcnJvclRleHQpO1xuICAgIHRocm93IG5ldyBFcnJvcihgV2ViaG9vayBmYWlsZWQ6ICR7cmVzcG9uc2Uuc3RhdHVzfSAke3Jlc3BvbnNlLnN0YXR1c1RleHR9IC0gJHtlcnJvclRleHR9YCk7XG4gIH1cblxuICByZXR1cm4gcmVzcG9uc2U7XG59O1xuXG4vLyBVbml2ZXJzYWwgZm9ybSBzdWJtaXNzaW9uIGZ1bmN0aW9uXG5leHBvcnQgY29uc3Qgc3VibWl0Rm9ybSA9IGFzeW5jIChcbiAgZm9ybVR5cGU6IHN0cmluZyxcbiAgZGF0YTogUmVjb3JkPHN0cmluZywgdW5rbm93bj5cbik6IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBtZXNzYWdlOiBzdHJpbmcgfT4gPT4ge1xuICB0cnkge1xuICAgIGNvbnN0IGNvbmZpZyA9IGdldEZvcm1Db25maWcoZm9ybVR5cGUpO1xuXG4gICAgLy8gRGVidWcgbG9nZ2luZ1xuICAgIGNvbnNvbGUubG9nKCfwn5SNIEZvcm0gc3VibWlzc2lvbiBkZWJ1ZzonLCB7XG4gICAgICBmb3JtVHlwZSxcbiAgICAgIHdlYmhvb2tVcmw6IGNvbmZpZy53ZWJob29rVXJsLFxuICAgICAgaXNDb25maWd1cmVkOiBpc1dlYmhvb2tDb25maWd1cmVkKGZvcm1UeXBlKSxcbiAgICAgIGRhdGE6IGRhdGFcbiAgICB9KTtcblxuICAgIGlmIChjb25maWcud2ViaG9va1VybCAmJiBpc1dlYmhvb2tDb25maWd1cmVkKGZvcm1UeXBlKSkge1xuICAgICAgLy8gU2VuZCB0byBNYWtlLmNvbSB3ZWJob29rXG4gICAgICBhd2FpdCBzZW5kVG9NYWtlV2ViaG9vayhjb25maWcud2ViaG9va1VybCwge1xuICAgICAgICBmb3JtVHlwZSxcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgIHNvdXJjZTogJ3NldG1lZS13ZWJzaXRlJyxcbiAgICAgICAgdXNlckFnZW50OiB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyA/IHdpbmRvdy5uYXZpZ2F0b3IudXNlckFnZW50IDogJ3NlcnZlcicsXG4gICAgICAgIHJlZmVycmVyOiB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyA/IHdpbmRvdy5kb2N1bWVudC5yZWZlcnJlciA6ICcnLFxuICAgICAgICAuLi5kYXRhLFxuICAgICAgfSk7XG5cbiAgICAgIGNvbnNvbGUubG9nKGAke2Zvcm1UeXBlfSBmb3JtIHNlbnQgdG8gTWFrZSB3ZWJob29rOmAsIGRhdGEuZW1haWwgfHwgZGF0YS5uYW1lKTtcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gRmFsbGJhY2s6IGxvZyB0byBjb25zb2xlIGlmIG5vIHdlYmhvb2sgY29uZmlndXJlZFxuICAgICAgY29uc29sZS5sb2coYCR7Zm9ybVR5cGV9IGZvcm0gc3VibWl0dGVkIChubyB3ZWJob29rIGNvbmZpZ3VyZWQpOmAsIGRhdGEpO1xuICAgIH1cblxuICAgIHJldHVybiB7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgbWVzc2FnZTogY29uZmlnLnN1Y2Nlc3NNZXNzYWdlLFxuICAgIH07XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcihgJHtmb3JtVHlwZX0gZm9ybSBzdWJtaXNzaW9uIGVycm9yOmAsIGVycm9yKTtcbiAgICBjb25zdCBjb25maWcgPSBnZXRGb3JtQ29uZmlnKGZvcm1UeXBlKTtcbiAgICByZXR1cm4ge1xuICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICBtZXNzYWdlOiBjb25maWcuZXJyb3JNZXNzYWdlLFxuICAgIH07XG4gIH1cbn07XG5cbi8vIFNwZWNpZmljIGZvcm0gc3VibWlzc2lvbiBmdW5jdGlvbnMgKGJhY2t3YXJkIGNvbXBhdGliaWxpdHkpXG5leHBvcnQgY29uc3Qgc3VibWl0Q29udGFjdEZvcm0gPSBhc3luYyAoZGF0YTogQ29udGFjdEZvcm1EYXRhKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IG1lc3NhZ2U6IHN0cmluZyB9PiA9PiB7XG4gIHJldHVybiBzdWJtaXRGb3JtKCdjb250YWN0JywgZGF0YSk7XG59O1xuXG5leHBvcnQgY29uc3Qgc3VibWl0RW1haWxTdWJzY3JpcHRpb24gPSBhc3luYyAoZGF0YTogRW1haWxTdWJzY3JpcHRpb25EYXRhKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IG1lc3NhZ2U6IHN0cmluZyB9PiA9PiB7XG4gIHJldHVybiBzdWJtaXRGb3JtKCduZXdzbGV0dGVyJywgZGF0YSk7XG59O1xuXG4vLyBBZGRpdGlvbmFsIGZvcm0gc3VibWlzc2lvbiBmdW5jdGlvbnMgZm9yIGRpZmZlcmVudCB0eXBlc1xuZXhwb3J0IGNvbnN0IHN1Ym1pdERlbW9SZXF1ZXN0ID0gYXN5bmMgKGRhdGE6IERlbW9SZXF1ZXN0RGF0YSk6IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBtZXNzYWdlOiBzdHJpbmcgfT4gPT4ge1xuICByZXR1cm4gc3VibWl0Rm9ybSgnZGVtbycsIGRhdGEpO1xufTtcblxuZXhwb3J0IGNvbnN0IHN1Ym1pdEF1ZGl0UmVxdWVzdCA9IGFzeW5jIChkYXRhOiBBdWRpdFJlcXVlc3REYXRhKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IG1lc3NhZ2U6IHN0cmluZyB9PiA9PiB7XG4gIHJldHVybiBzdWJtaXRGb3JtKCdhdWRpdCcsIGRhdGEpO1xufTtcblxuZXhwb3J0IGNvbnN0IHN1Ym1pdENvbnN1bHRhdGlvblJlcXVlc3QgPSBhc3luYyAoZGF0YTogQ29uc3VsdGF0aW9uUmVxdWVzdERhdGEpOiBQcm9taXNlPHsgc3VjY2VzczogYm9vbGVhbjsgbWVzc2FnZTogc3RyaW5nIH0+ID0+IHtcbiAgcmV0dXJuIHN1Ym1pdEZvcm0oJ2NvbnN1bHRhdGlvbicsIGRhdGEpO1xufTtcblxuLy8gRXhhbXBsZSBpbnRlZ3JhdGlvbiB3aXRoIEZvcm1zcHJlZSAoY29tbWVudGVkIG91dClcbi8qXG5leHBvcnQgY29uc3Qgc3VibWl0Q29udGFjdEZvcm1Gb3Jtc3ByZWUgPSBhc3luYyAoZGF0YTogQ29udGFjdEZvcm1EYXRhKSA9PiB7XG4gIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJ2h0dHBzOi8vZm9ybXNwcmVlLmlvL2YvWU9VUl9GT1JNX0lEJywge1xuICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgIGhlYWRlcnM6IHtcbiAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgfSxcbiAgICBib2R5OiBKU09OLnN0cmluZ2lmeShkYXRhKSxcbiAgfSk7XG4gIFxuICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gc3VibWl0IGZvcm0nKTtcbiAgfVxuICBcbiAgcmV0dXJuIHJlc3BvbnNlLmpzb24oKTtcbn07XG4qL1xuXG4vLyBFeGFtcGxlIGludGVncmF0aW9uIHdpdGggRW1haWxKUyAoY29tbWVudGVkIG91dClcbi8qXG5pbXBvcnQgZW1haWxqcyBmcm9tICdAZW1haWxqcy9icm93c2VyJztcblxuZXhwb3J0IGNvbnN0IHN1Ym1pdENvbnRhY3RGb3JtRW1haWxKUyA9IGFzeW5jIChkYXRhOiBDb250YWN0Rm9ybURhdGEpID0+IHtcbiAgY29uc3QgcmVzdWx0ID0gYXdhaXQgZW1haWxqcy5zZW5kKFxuICAgICdZT1VSX1NFUlZJQ0VfSUQnLFxuICAgICdZT1VSX1RFTVBMQVRFX0lEJyxcbiAgICB7XG4gICAgICBmcm9tX25hbWU6IGRhdGEubmFtZSxcbiAgICAgIGZyb21fZW1haWw6IGRhdGEuZW1haWwsXG4gICAgICBwaG9uZTogZGF0YS5waG9uZSxcbiAgICAgIGNvbXBhbnk6IGRhdGEuY29tcGFueSxcbiAgICAgIG1lc3NhZ2U6IGRhdGEubWVzc2FnZSxcbiAgICB9LFxuICAgICdZT1VSX1BVQkxJQ19LRVknXG4gICk7XG4gIFxuICByZXR1cm4gcmVzdWx0O1xufTtcbiovXG4iXSwibmFtZXMiOlsiZ2V0Rm9ybUNvbmZpZyIsImlzV2ViaG9va0NvbmZpZ3VyZWQiLCJzZW5kVG9NYWtlV2ViaG9vayIsIndlYmhvb2tVcmwiLCJkYXRhIiwiY29uc29sZSIsImxvZyIsIkpTT04iLCJzdHJpbmdpZnkiLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJzdGF0dXMiLCJvayIsImVycm9yVGV4dCIsInRleHQiLCJlcnJvciIsIkVycm9yIiwic3RhdHVzVGV4dCIsInN1Ym1pdEZvcm0iLCJmb3JtVHlwZSIsImNvbmZpZyIsImlzQ29uZmlndXJlZCIsInRpbWVzdGFtcCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsInNvdXJjZSIsInVzZXJBZ2VudCIsIndpbmRvdyIsIm5hdmlnYXRvciIsInJlZmVycmVyIiwiZG9jdW1lbnQiLCJlbWFpbCIsIm5hbWUiLCJzdWNjZXNzIiwibWVzc2FnZSIsInN1Y2Nlc3NNZXNzYWdlIiwiZXJyb3JNZXNzYWdlIiwic3VibWl0Q29udGFjdEZvcm0iLCJzdWJtaXRFbWFpbFN1YnNjcmlwdGlvbiIsInN1Ym1pdERlbW9SZXF1ZXN0Iiwic3VibWl0QXVkaXRSZXF1ZXN0Iiwic3VibWl0Q29uc3VsdGF0aW9uUmVxdWVzdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/form-service.ts\n"));

/***/ })

});