"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contact/page",{

/***/ "(app-pages-browser)/./src/lib/form-service.ts":
/*!*********************************!*\
  !*** ./src/lib/form-service.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   submitAuditRequest: () => (/* binding */ submitAuditRequest),\n/* harmony export */   submitConsultationRequest: () => (/* binding */ submitConsultationRequest),\n/* harmony export */   submitContactForm: () => (/* binding */ submitContactForm),\n/* harmony export */   submitDemoRequest: () => (/* binding */ submitDemoRequest),\n/* harmony export */   submitEmailSubscription: () => (/* binding */ submitEmailSubscription),\n/* harmony export */   submitForm: () => (/* binding */ submitForm)\n/* harmony export */ });\n/* harmony import */ var _config_forms__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config/forms */ \"(app-pages-browser)/./src/config/forms.ts\");\n\n// Universal form submission service with Make.com webhook integration\n// Supports multiple form types with different webhook endpoints\n// Generic webhook sender\nconst sendToMakeWebhook = async (webhookUrl, data)=>{\n    const response = await fetch(webhookUrl, {\n        method: 'POST',\n        headers: {\n            'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(data)\n    });\n    if (!response.ok) {\n        const errorText = await response.text();\n        console.error('❌ Webhook error response:', errorText);\n        throw new Error(\"Webhook failed: \".concat(response.status, \" \").concat(response.statusText, \" - \").concat(errorText));\n    }\n    return response;\n};\n// Universal form submission function\nconst submitForm = async (formType, data)=>{\n    try {\n        const config = (0,_config_forms__WEBPACK_IMPORTED_MODULE_0__.getFormConfig)(formType);\n        if (config.webhookUrl && (0,_config_forms__WEBPACK_IMPORTED_MODULE_0__.isWebhookConfigured)(formType)) {\n            // Send to Make.com webhook\n            await sendToMakeWebhook(config.webhookUrl, {\n                formType,\n                timestamp: new Date().toISOString(),\n                source: 'setmee-website',\n                userAgent:  true ? window.navigator.userAgent : 0,\n                referrer:  true ? window.document.referrer : 0,\n                ...data\n            });\n            console.log(\"\".concat(formType, \" form sent to Make webhook:\"), data.email || data.name);\n        } else {\n            // Fallback: log to console if no webhook configured\n            console.log(\"\".concat(formType, \" form submitted (no webhook configured):\"), data);\n        }\n        return {\n            success: true,\n            message: config.successMessage\n        };\n    } catch (error) {\n        console.error(\"\".concat(formType, \" form submission error:\"), error);\n        const config = (0,_config_forms__WEBPACK_IMPORTED_MODULE_0__.getFormConfig)(formType);\n        return {\n            success: false,\n            message: config.errorMessage\n        };\n    }\n};\n// Specific form submission functions (backward compatibility)\nconst submitContactForm = async (data)=>{\n    return submitForm('contact', data);\n};\nconst submitEmailSubscription = async (data)=>{\n    return submitForm('newsletter', data);\n};\n// Additional form submission functions for different types\nconst submitDemoRequest = async (data)=>{\n    return submitForm('demo', data);\n};\nconst submitAuditRequest = async (data)=>{\n    return submitForm('audit', data);\n};\nconst submitConsultationRequest = async (data)=>{\n    return submitForm('consultation', data);\n}; // Example integration with Formspree (commented out)\n /*\nexport const submitContactFormFormspree = async (data: ContactFormData) => {\n  const response = await fetch('https://formspree.io/f/YOUR_FORM_ID', {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify(data),\n  });\n  \n  if (!response.ok) {\n    throw new Error('Failed to submit form');\n  }\n  \n  return response.json();\n};\n*/  // Example integration with EmailJS (commented out)\n /*\nimport emailjs from '@emailjs/browser';\n\nexport const submitContactFormEmailJS = async (data: ContactFormData) => {\n  const result = await emailjs.send(\n    'YOUR_SERVICE_ID',\n    'YOUR_TEMPLATE_ID',\n    {\n      from_name: data.name,\n      from_email: data.email,\n      phone: data.phone,\n      company: data.company,\n      message: data.message,\n    },\n    'YOUR_PUBLIC_KEY'\n  );\n  \n  return result;\n};\n*/ \n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/form-service.ts\n"));

/***/ })

});