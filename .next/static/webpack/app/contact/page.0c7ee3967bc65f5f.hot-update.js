"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contact/page",{

/***/ "(app-pages-browser)/./src/config/forms.ts":
/*!*****************************!*\
  !*** ./src/config/forms.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FORM_CONFIGS: () => (/* binding */ FORM_CONFIGS),\n/* harmony export */   getFormConfig: () => (/* binding */ getFormConfig),\n/* harmony export */   isWebhookConfigured: () => (/* binding */ isWebhookConfigured)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Form configuration for different types of forms and their Make.com webhooks\nconst FORM_CONFIGS = {\n    // Основная контактная форма\n    contact: {\n        webhookUrl: \"https://hook.eu1.make.com/9qtsqgumbg91vc5llliahsy1k41tfgl3\",\n        successMessage: 'Thank you for your message! We will get back to you within 24 hours.',\n        errorMessage: 'Sorry, there was an error sending your message. Please try again.',\n        fields: [\n            'name',\n            'email',\n            'phone',\n            'company',\n            'message'\n        ],\n        type: 'contact'\n    },\n    // Подписка на новости/презентацию\n    newsletter: {\n        webhookUrl: \"https://hook.eu1.make.com/9qtsqgumbg91vc5llliahsy1k41tfgl3\",\n        successMessage: 'Thank you for subscribing! We will send you the presentation shortly.',\n        errorMessage: 'Sorry, there was an error with your subscription. Please try again.',\n        fields: [\n            'email'\n        ],\n        type: 'newsletter'\n    },\n    // Запрос демо\n    demo: {\n        webhookUrl: process.env.NEXT_PUBLIC_MAKE_DEMO_WEBHOOK,\n        successMessage: 'Demo request sent! We will contact you soon to schedule a presentation.',\n        errorMessage: 'Sorry, there was an error sending your demo request. Please try again.',\n        fields: [\n            'name',\n            'email',\n            'company',\n            'phone'\n        ],\n        type: 'demo'\n    },\n    // Запрос аудита\n    audit: {\n        webhookUrl: process.env.NEXT_PUBLIC_MAKE_AUDIT_WEBHOOK,\n        successMessage: 'Audit request received! We will prepare your report and contact you within 2 business days.',\n        errorMessage: 'Sorry, there was an error sending your audit request. Please try again.',\n        fields: [\n            'name',\n            'email',\n            'website',\n            'company',\n            'currentCrm'\n        ],\n        type: 'audit'\n    },\n    // Консультация эксперта\n    consultation: {\n        webhookUrl: process.env.NEXT_PUBLIC_MAKE_CONSULTATION_WEBHOOK,\n        successMessage: 'Consultation request sent! Our expert will contact you within 24 hours.',\n        errorMessage: 'Sorry, there was an error sending your consultation request. Please try again.',\n        fields: [\n            'name',\n            'email',\n            'phone',\n            'company',\n            'challenge'\n        ],\n        type: 'consultation'\n    }\n};\n// Получить конфигурацию формы по типу\nconst getFormConfig = (formType)=>{\n    const config = FORM_CONFIGS[formType];\n    if (!config) {\n        throw new Error(\"Form configuration not found for type: \".concat(formType));\n    }\n    return config;\n};\n// Проверить, настроен ли webhook для формы\nconst isWebhookConfigured = (formType)=>{\n    const config = getFormConfig(formType);\n    return !!config.webhookUrl;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/config/forms.ts\n"));

/***/ })

});