"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/IntegrationsSection.tsx":
/*!*********************************************************!*\
  !*** ./src/components/sections/IntegrationsSection.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst IntegrationsSection = ()=>{\n    // Popular integrations with placeholder logos\n    const integrations = [\n        {\n            name: 'WhatsApp',\n            color: 'bg-green-500'\n        },\n        {\n            name: 'Telegram',\n            color: 'bg-blue-500'\n        },\n        {\n            name: 'Facebook',\n            color: 'bg-blue-600'\n        },\n        {\n            name: 'Instagram',\n            color: 'bg-pink-500'\n        },\n        {\n            name: 'Gmail',\n            color: 'bg-red-500'\n        },\n        {\n            name: 'Slack',\n            color: 'bg-purple-500'\n        },\n        {\n            name: 'Zoom',\n            color: 'bg-blue-400'\n        },\n        {\n            name: 'Mailchimp',\n            color: 'bg-yellow-500'\n        },\n        {\n            name: 'Stripe',\n            color: 'bg-indigo-500'\n        },\n        {\n            name: 'Zapier',\n            color: 'bg-orange-500'\n        },\n        {\n            name: 'Calendly',\n            color: 'bg-blue-500'\n        },\n        {\n            name: 'Typeform',\n            color: 'bg-gray-700'\n        },\n        {\n            name: 'Google Analytics',\n            color: 'bg-orange-400'\n        },\n        {\n            name: 'Twilio',\n            color: 'bg-red-600'\n        },\n        {\n            name: 'Dropbox',\n            color: 'bg-blue-500'\n        },\n        {\n            name: 'Intercom',\n            color: 'bg-blue-600'\n        },\n        {\n            name: 'Zendesk',\n            color: 'bg-green-600'\n        },\n        {\n            name: 'ActiveCampaign',\n            color: 'bg-blue-700'\n        },\n        {\n            name: 'Facebook Ads',\n            color: 'bg-blue-600'\n        },\n        {\n            name: 'Google Calendar',\n            color: 'bg-blue-500'\n        },\n        {\n            name: 'Viber',\n            color: 'bg-purple-600'\n        },\n        {\n            name: 'WeChat',\n            color: 'bg-green-500'\n        },\n        {\n            name: 'RingCentral',\n            color: 'bg-orange-500'\n        },\n        {\n            name: 'Formstack',\n            color: 'bg-orange-600'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Section, {\n        id: \"integrations\",\n        padding: \"xl\",\n        background: \"white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.SectionHeader, {\n                subtitle: \"integrations\",\n                title: \"100+ integrations with various services available on Kommo Marketplace\",\n                align: \"center\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-4 mb-12\",\n                children: integrations.map((integration)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"group relative bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors duration-200 cursor-pointer\",\n                        style: {\n                            pointerEvents: 'auto !important',\n                            position: 'relative',\n                            zIndex: 10,\n                            cursor: 'pointer'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 \".concat(integration.color, \" rounded-lg flex items-center justify-center mx-auto mb-2 group-hover:scale-110 transition-transform duration-200\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white font-bold text-xs\",\n                                    children: integration.name.charAt(0)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-600 text-center font-medium truncate\",\n                                children: integration.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10\",\n                                children: integration.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, integration.name, true, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-primary-50 to-secondary-50 rounded-2xl p-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-3xl mx-auto space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Can't find the integration you need?\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 leading-relaxed\",\n                                    children: \"We create custom integrations tailored to your specific business needs. Our team has experience with hundreds of different services and APIs.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/contact\",\n                                            className: \"inline-flex items-center justify-center font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 px-6 py-3 text-base rounded-lg\",\n                                            style: {\n                                                pointerEvents: 'auto !important',\n                                                position: 'relative',\n                                                zIndex: 20,\n                                                cursor: 'pointer'\n                                            },\n                                            children: \"Request Custom Integration\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"inline-flex items-center justify-center font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white focus:ring-primary-500 px-6 py-3 text-base rounded-lg\",\n                                            style: {\n                                                pointerEvents: 'auto !important',\n                                                position: 'relative',\n                                                zIndex: 20,\n                                                cursor: 'pointer'\n                                            },\n                                            children: \"View All Integrations\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8 pt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-primary-600\",\n                                        children: \"100+\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Ready Integrations\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-secondary-500\",\n                                        children: \"30+\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Custom Developed\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-green-500\",\n                                        children: \"24/7\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Integration Support\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/IntegrationsSection.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, undefined);\n};\n_c = IntegrationsSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (IntegrationsSection);\nvar _c;\n$RefreshReg$(_c, \"IntegrationsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/IntegrationsSection.tsx\n"));

/***/ })

});