"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/CTAVideoSection.tsx":
/*!*****************************************************!*\
  !*** ./src/components/sections/CTAVideoSection.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _lib_validations__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/validations */ \"(app-pages-browser)/./src/lib/validations.ts\");\n/* harmony import */ var _lib_form_service__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/form-service */ \"(app-pages-browser)/./src/lib/form-service.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CTAVideoSection = ()=>{\n    _s();\n    const [isSubmitted, setIsSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitMessage, setSubmitMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isVideoModalOpen, setIsVideoModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewImage, setPreviewImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('/images/player-preview.png');\n    const [isImageLoaded, setIsImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFormSubmitting, setIsFormSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle Escape key to close modal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CTAVideoSection.useEffect\": ()=>{\n            const handleEscape = {\n                \"CTAVideoSection.useEffect.handleEscape\": (e)=>{\n                    if (e.key === 'Escape' && isVideoModalOpen) {\n                        setIsVideoModalOpen(false);\n                    }\n                }\n            }[\"CTAVideoSection.useEffect.handleEscape\"];\n            if (isVideoModalOpen) {\n                document.addEventListener('keydown', handleEscape);\n                // Prevent body scroll when modal is open\n                document.body.style.overflow = 'hidden';\n            }\n            return ({\n                \"CTAVideoSection.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleEscape);\n                    document.body.style.overflow = 'unset';\n                }\n            })[\"CTAVideoSection.useEffect\"];\n        }\n    }[\"CTAVideoSection.useEffect\"], [\n        isVideoModalOpen\n    ]);\n    const handleImageLoad = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CTAVideoSection.useCallback[handleImageLoad]\": ()=>{\n            console.log('Image loaded successfully:', previewImage);\n            setIsImageLoaded(true);\n        }\n    }[\"CTAVideoSection.useCallback[handleImageLoad]\"], [\n        previewImage\n    ]);\n    const { register, handleSubmit, formState: { errors }, reset } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_7__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(_lib_validations__WEBPACK_IMPORTED_MODULE_5__.emailSubscriptionSchema)\n    });\n    const onSubmit = async (data)=>{\n        try {\n            setIsFormSubmitting(true);\n            const result = await (0,_lib_form_service__WEBPACK_IMPORTED_MODULE_6__.submitEmailSubscription)(data);\n            if (result.success) {\n                setIsSubmitted(true);\n                setSubmitMessage(result.message);\n                reset();\n            } else {\n                setSubmitMessage(result.message);\n            }\n        } catch (e) {\n            setSubmitMessage('An unexpected error occurred. Please try again.');\n        } finally{\n            setIsFormSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Section, {\n        id: \"cta-video\",\n        padding: \"xl\",\n        background: \"primary\",\n        className: \"text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"aspect-video bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl overflow-hidden shadow-2xl relative cursor-pointer group hover:shadow-3xl transition-all duration-300\",\n                            onClick: ()=>{\n                                console.log('Video clicked!');\n                                setIsVideoModalOpen(true);\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: previewImage,\n                                        alt: \"Kommo Demo Video - Power your sales with Kommo\",\n                                        fill: true,\n                                        sizes: \"(max-width: 1024px) 100vw, 50vw\",\n                                        className: \"object-cover transition-opacity duration-500 \".concat(isImageLoaded ? 'opacity-100' : 'opacity-0'),\n                                        onLoad: ()=>setIsImageLoaded(true),\n                                        onError: (e)=>{\n                                            console.error('Failed to load preview image:', e.currentTarget.src);\n                                            if (!e.currentTarget.src.includes('youtube')) {\n                                                setPreviewImage('https://img.youtube.com/vi/mReZr_e70OA/maxresdefault.jpg');\n                                            }\n                                            // Also set loaded to true to show the (potentially broken) fallback or let it retry\n                                            setIsImageLoaded(true);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center justify-center bg-black bg-opacity-20 group-hover:bg-opacity-30 transition-all duration-300 z-20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-20 h-20 bg-white bg-opacity-95 rounded-full flex items-center justify-center hover:bg-opacity-100 transition-all duration-200 shadow-xl group-hover:scale-110 border-4 border-white border-opacity-20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8 text-gray-800 ml-1\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-block\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-secondary-500 text-white px-3 py-1 rounded-full text-sm font-semibold uppercase tracking-wide\",\n                                            children: \"SELL MORE WITH kommo\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl md:text-4xl font-bold leading-tight\",\n                                        children: \"A dedicated solution for startups and enterprises\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-primary-100 leading-relaxed\",\n                                        children: \"Get an extended Kommo presentation and see how our CRM solution can help your business grow and scale efficiently.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-primary-700 rounded-xl p-6 space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 text-secondary-400\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold\",\n                                                children: \"Get an extended Kommo presentation!\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    !isSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit(onSubmit),\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        ...register('email'),\n                                                        placeholder: \"Enter your email address\",\n                                                        className: \"w-full px-4 py-3 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-secondary-500 \".concat(errors.email ? 'ring-2 ring-red-500' : '')\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-300\",\n                                                        children: errors.email.message\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            submitMessage && !isSubmitted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg text-sm \".concat(submitMessage.includes('error') || submitMessage.includes('Sorry') ? 'bg-red-500 text-white' : 'bg-green-500 text-white'),\n                                                children: submitMessage\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                type: \"submit\",\n                                                variant: \"secondary\",\n                                                size: \"lg\",\n                                                disabled: isFormSubmitting,\n                                                className: \"w-full\",\n                                                children: isFormSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Subscribing...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 21\n                                                }, undefined) : 'Get Started'\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2 text-green-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: \"Successful!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-primary-200 text-sm\",\n                                                children: submitMessage\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    href: \"/contact\",\n                                    variant: \"outline\",\n                                    size: \"lg\",\n                                    className: \"border-white text-white hover:bg-white hover:text-primary-800\",\n                                    children: \"Talk to an expert\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, undefined),\n            isVideoModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4\",\n                onClick: ()=>setIsVideoModalOpen(false),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full max-w-4xl aspect-video\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsVideoModalOpen(false),\n                            className: \"absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors duration-200 z-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-8 h-8\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full bg-black rounded-lg overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                src: \"https://www.youtube.com/embed/mReZr_e70OA?autoplay=1&rel=0&modestbranding=1\",\n                                title: \"Kommo Demo - See how Kommo can transform your business\",\n                                className: \"w-full h-full\",\n                                allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\",\n                                allowFullScreen: true,\n                                frameBorder: \"0\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                lineNumber: 218,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CTAVideoSection, \"UhHhqqTzgQvAFx8RPBiBvCVHb9Q=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_7__.useForm\n    ];\n});\n_c = CTAVideoSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CTAVideoSection);\nvar _c;\n$RefreshReg$(_c, \"CTAVideoSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/CTAVideoSection.tsx\n"));

/***/ })

});