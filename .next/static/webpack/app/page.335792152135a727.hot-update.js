"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/CTAVideoSection.tsx":
/*!*****************************************************!*\
  !*** ./src/components/sections/CTAVideoSection.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _lib_validations__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/validations */ \"(app-pages-browser)/./src/lib/validations.ts\");\n/* harmony import */ var _lib_form_service__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/form-service */ \"(app-pages-browser)/./src/lib/form-service.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst CTAVideoSection = ()=>{\n    _s();\n    const [isSubmitted, setIsSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitMessage, setSubmitMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isVideoModalOpen, setIsVideoModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewImage, setPreviewImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('https://img.youtube.com/vi/mReZr_e70OA/hqdefault.jpg');\n    const [isImageLoaded, setIsImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFormSubmitting, setIsFormSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle Escape key to close modal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CTAVideoSection.useEffect\": ()=>{\n            const handleEscape = {\n                \"CTAVideoSection.useEffect.handleEscape\": (e)=>{\n                    if (e.key === 'Escape' && isVideoModalOpen) {\n                        setIsVideoModalOpen(false);\n                    }\n                }\n            }[\"CTAVideoSection.useEffect.handleEscape\"];\n            if (isVideoModalOpen) {\n                document.addEventListener('keydown', handleEscape);\n                // Prevent body scroll when modal is open\n                document.body.style.overflow = 'hidden';\n            }\n            return ({\n                \"CTAVideoSection.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleEscape);\n                    document.body.style.overflow = 'unset';\n                }\n            })[\"CTAVideoSection.useEffect\"];\n        }\n    }[\"CTAVideoSection.useEffect\"], [\n        isVideoModalOpen\n    ]);\n    const handleImageLoad = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CTAVideoSection.useCallback[handleImageLoad]\": ()=>{\n            console.log('✅ Image loaded successfully:', previewImage);\n            setIsImageLoaded(true);\n            setImageError(false);\n        }\n    }[\"CTAVideoSection.useCallback[handleImageLoad]\"], [\n        previewImage\n    ]);\n    const handleImageError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CTAVideoSection.useCallback[handleImageError]\": (e)=>{\n            console.error('❌ Failed to load preview image:', e.currentTarget.src);\n            setImageError(true);\n            setIsImageLoaded(true); // Show fallback content\n        }\n    }[\"CTAVideoSection.useCallback[handleImageError]\"], []);\n    // Debug: Log current state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CTAVideoSection.useEffect\": ()=>{\n            console.log('🔍 Current image state:', {\n                previewImage,\n                isImageLoaded,\n                imageError\n            });\n        }\n    }[\"CTAVideoSection.useEffect\"], [\n        previewImage,\n        isImageLoaded,\n        imageError\n    ]);\n    // Debug: Test if component is mounted and interactive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CTAVideoSection.useEffect\": ()=>{\n            console.log('🎯 CTAVideoSection mounted and ready');\n            // Test if we can access DOM elements\n            const videoElement = document.querySelector('[data-testid=\"video-player\"]');\n            const emailElement = document.querySelector('[data-testid=\"email-input\"]');\n            console.log('🔍 DOM elements found:', {\n                videoElement: !!videoElement,\n                emailElement: !!emailElement\n            });\n        }\n    }[\"CTAVideoSection.useEffect\"], []);\n    // Timeout for image loading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CTAVideoSection.useEffect\": ()=>{\n            const timeout = setTimeout({\n                \"CTAVideoSection.useEffect.timeout\": ()=>{\n                    if (!isImageLoaded && !imageError) {\n                        console.log('⏰ Image loading timeout, keeping fallback');\n                        setImageError(true);\n                    }\n                }\n            }[\"CTAVideoSection.useEffect.timeout\"], 5000); // 5 second timeout\n            return ({\n                \"CTAVideoSection.useEffect\": ()=>clearTimeout(timeout)\n            })[\"CTAVideoSection.useEffect\"];\n        }\n    }[\"CTAVideoSection.useEffect\"], [\n        isImageLoaded,\n        imageError\n    ]);\n    const { register, handleSubmit, formState: { errors }, reset } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(_lib_validations__WEBPACK_IMPORTED_MODULE_4__.emailSubscriptionSchema)\n    });\n    const onSubmit = async (data)=>{\n        try {\n            setIsFormSubmitting(true);\n            const result = await (0,_lib_form_service__WEBPACK_IMPORTED_MODULE_5__.submitEmailSubscription)(data);\n            if (result.success) {\n                setIsSubmitted(true);\n                setSubmitMessage(result.message);\n                reset();\n            } else {\n                setSubmitMessage(result.message);\n            }\n        } catch (e) {\n            setSubmitMessage('An unexpected error occurred. Please try again.');\n        } finally{\n            setIsFormSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Section, {\n        id: \"cta-video\",\n        padding: \"xl\",\n        background: \"primary\",\n        className: \"text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                style: {\n                    position: 'relative',\n                    zIndex: 10,\n                    pointerEvents: 'auto'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            \"data-testid\": \"video-player\",\n                            className: \"aspect-video bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl overflow-hidden shadow-2xl relative cursor-pointer group hover:shadow-3xl transition-all duration-300\",\n                            onClick: (e)=>{\n                                console.log('Video player clicked!', e);\n                                setIsVideoModalOpen(true);\n                            },\n                            onMouseEnter: ()=>console.log('Video player hover'),\n                            style: {\n                                pointerEvents: 'auto !important',\n                                position: 'relative',\n                                zIndex: 20,\n                                cursor: 'pointer'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full h-full overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center text-white px-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 mx-auto mb-4 bg-white bg-opacity-20 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-8 h-8\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold mb-2\",\n                                                    children: \"Kommo Demo Video\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm opacity-90 mb-1\",\n                                                    children: \"Discover how Kommo can transform your business\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs opacity-70\",\n                                                    children: \"Click to watch our presentation\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isImageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: previewImage,\n                                        alt: \"Kommo Demo Video - Power your sales with Kommo\",\n                                        className: \"absolute inset-0 w-full h-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: previewImage,\n                                        alt: \"\",\n                                        className: \"hidden\",\n                                        onLoad: handleImageLoad,\n                                        onError: handleImageError\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isImageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center justify-center bg-black bg-opacity-20 group-hover:bg-opacity-30 transition-all duration-300 z-20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-20 h-20 bg-white bg-opacity-95 rounded-full flex items-center justify-center hover:bg-opacity-100 transition-all duration-200 shadow-xl group-hover:scale-110 border-4 border-white border-opacity-20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8 text-gray-800 ml-1\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        style: {\n                            position: 'relative',\n                            zIndex: 20,\n                            pointerEvents: 'auto'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-block\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-secondary-500 text-white px-3 py-1 rounded-full text-sm font-semibold uppercase tracking-wide\",\n                                            children: \"SELL MORE WITH kommo\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl md:text-4xl font-bold leading-tight\",\n                                        children: \"A dedicated solution for startups and enterprises\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-primary-100 leading-relaxed\",\n                                        children: \"Get an extended Kommo presentation and see how our CRM solution can help your business grow and scale efficiently.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-primary-700 rounded-xl p-6 space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 text-secondary-400\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold\",\n                                                children: \"Get an extended Kommo presentation!\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    !isSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit(onSubmit),\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        \"data-testid\": \"email-input\",\n                                                        type: \"email\",\n                                                        ...register('email'),\n                                                        placeholder: \"Enter your email address\",\n                                                        className: \"w-full px-4 py-3 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-secondary-500 \".concat(errors.email ? 'ring-2 ring-red-500' : ''),\n                                                        onClick: (e)=>{\n                                                            console.log('Email input clicked!', e);\n                                                        },\n                                                        onFocus: (e)=>{\n                                                            console.log('Email input focused!', e);\n                                                        },\n                                                        style: {\n                                                            pointerEvents: 'auto !important',\n                                                            position: 'relative',\n                                                            zIndex: 30\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-300\",\n                                                        children: errors.email.message\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            submitMessage && !isSubmitted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg text-sm \".concat(submitMessage.includes('error') || submitMessage.includes('Sorry') ? 'bg-red-500 text-white' : 'bg-green-500 text-white'),\n                                                children: submitMessage\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                type: \"submit\",\n                                                variant: \"secondary\",\n                                                size: \"lg\",\n                                                disabled: isFormSubmitting,\n                                                className: \"w-full\",\n                                                onClick: (e)=>{\n                                                    console.log('Get Started button clicked!', e);\n                                                },\n                                                style: {\n                                                    pointerEvents: 'auto'\n                                                },\n                                                children: isFormSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Subscribing...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 21\n                                                }, undefined) : 'Get Started'\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2 text-green-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: \"Successful!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-primary-200 text-sm\",\n                                                children: submitMessage\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    href: \"/contact\",\n                                    variant: \"outline\",\n                                    size: \"lg\",\n                                    className: \"border-white text-white hover:bg-white hover:text-primary-800\",\n                                    onClick: (e)=>{\n                                        console.log('Talk to an expert button clicked!', e);\n                                    },\n                                    style: {\n                                        pointerEvents: 'auto'\n                                    },\n                                    children: \"Talk to an expert\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, undefined),\n            isVideoModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4\",\n                onClick: ()=>setIsVideoModalOpen(false),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full max-w-4xl aspect-video\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsVideoModalOpen(false),\n                            className: \"absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors duration-200 z-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-8 h-8\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full bg-black rounded-lg overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                src: \"https://www.youtube.com/embed/mReZr_e70OA?autoplay=1&rel=0&modestbranding=1\",\n                                title: \"Kommo Demo - See how Kommo can transform your business\",\n                                className: \"w-full h-full\",\n                                allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\",\n                                allowFullScreen: true,\n                                frameBorder: \"0\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                    lineNumber: 324,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                lineNumber: 320,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CTAVideoSection, \"xzCmnCd77uMleIZfUJBLAdlQ0Xk=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm\n    ];\n});\n_c = CTAVideoSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CTAVideoSection);\nvar _c;\n$RefreshReg$(_c, \"CTAVideoSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/CTAVideoSection.tsx\n"));

/***/ })

});