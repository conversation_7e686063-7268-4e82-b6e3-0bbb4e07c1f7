"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/CTAVideoSection.tsx":
/*!*****************************************************!*\
  !*** ./src/components/sections/CTAVideoSection.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _lib_validations__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/validations */ \"(app-pages-browser)/./src/lib/validations.ts\");\n/* harmony import */ var _lib_form_service__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/form-service */ \"(app-pages-browser)/./src/lib/form-service.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst CTAVideoSection = ()=>{\n    _s();\n    const [isSubmitted, setIsSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitMessage, setSubmitMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isVideoModalOpen, setIsVideoModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewImage, setPreviewImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('https://img.youtube.com/vi/mReZr_e70OA/hqdefault.jpg');\n    const [isImageLoaded, setIsImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFormSubmitting, setIsFormSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle Escape key to close modal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CTAVideoSection.useEffect\": ()=>{\n            const handleEscape = {\n                \"CTAVideoSection.useEffect.handleEscape\": (e)=>{\n                    if (e.key === 'Escape' && isVideoModalOpen) {\n                        setIsVideoModalOpen(false);\n                    }\n                }\n            }[\"CTAVideoSection.useEffect.handleEscape\"];\n            if (isVideoModalOpen) {\n                document.addEventListener('keydown', handleEscape);\n                // Prevent body scroll when modal is open\n                document.body.style.overflow = 'hidden';\n            }\n            return ({\n                \"CTAVideoSection.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleEscape);\n                    document.body.style.overflow = 'unset';\n                }\n            })[\"CTAVideoSection.useEffect\"];\n        }\n    }[\"CTAVideoSection.useEffect\"], [\n        isVideoModalOpen\n    ]);\n    const handleImageLoad = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CTAVideoSection.useCallback[handleImageLoad]\": ()=>{\n            console.log('✅ Image loaded successfully:', previewImage);\n            setIsImageLoaded(true);\n            setImageError(false);\n        }\n    }[\"CTAVideoSection.useCallback[handleImageLoad]\"], [\n        previewImage\n    ]);\n    const handleImageError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CTAVideoSection.useCallback[handleImageError]\": (e)=>{\n            console.error('❌ Failed to load preview image:', e.currentTarget.src);\n            setImageError(true);\n            setIsImageLoaded(true); // Show fallback content\n        }\n    }[\"CTAVideoSection.useCallback[handleImageError]\"], []);\n    // Debug: Log current state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CTAVideoSection.useEffect\": ()=>{\n            console.log('🔍 Current image state:', {\n                previewImage,\n                isImageLoaded,\n                imageError\n            });\n        }\n    }[\"CTAVideoSection.useEffect\"], [\n        previewImage,\n        isImageLoaded,\n        imageError\n    ]);\n    // Timeout for image loading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CTAVideoSection.useEffect\": ()=>{\n            const timeout = setTimeout({\n                \"CTAVideoSection.useEffect.timeout\": ()=>{\n                    if (!isImageLoaded && !imageError) {\n                        console.log('⏰ Image loading timeout, keeping fallback');\n                        setImageError(true);\n                    }\n                }\n            }[\"CTAVideoSection.useEffect.timeout\"], 5000); // 5 second timeout\n            return ({\n                \"CTAVideoSection.useEffect\": ()=>clearTimeout(timeout)\n            })[\"CTAVideoSection.useEffect\"];\n        }\n    }[\"CTAVideoSection.useEffect\"], [\n        isImageLoaded,\n        imageError\n    ]);\n    const { register, handleSubmit, formState: { errors }, reset } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(_lib_validations__WEBPACK_IMPORTED_MODULE_4__.emailSubscriptionSchema)\n    });\n    const onSubmit = async (data)=>{\n        try {\n            setIsFormSubmitting(true);\n            const result = await (0,_lib_form_service__WEBPACK_IMPORTED_MODULE_5__.submitEmailSubscription)(data);\n            if (result.success) {\n                setIsSubmitted(true);\n                setSubmitMessage(result.message);\n                reset();\n            } else {\n                setSubmitMessage(result.message);\n            }\n        } catch (e) {\n            setSubmitMessage('An unexpected error occurred. Please try again.');\n        } finally{\n            setIsFormSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Section, {\n        id: \"cta-video\",\n        padding: \"xl\",\n        background: \"primary\",\n        className: \"text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"aspect-video bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl overflow-hidden shadow-2xl relative cursor-pointer group hover:shadow-3xl transition-all duration-300\",\n                            onClick: ()=>setIsVideoModalOpen(true),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full h-full overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center text-white px-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 mx-auto mb-4 bg-white bg-opacity-20 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-8 h-8\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold mb-2\",\n                                                    children: \"Kommo Demo Video\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm opacity-90 mb-1\",\n                                                    children: \"Discover how Kommo can transform your business\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs opacity-70\",\n                                                    children: \"Click to watch our presentation\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isImageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: previewImage,\n                                        alt: \"Kommo Demo Video - Power your sales with Kommo\",\n                                        className: \"absolute inset-0 w-full h-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: previewImage,\n                                        alt: \"\",\n                                        className: \"hidden\",\n                                        onLoad: handleImageLoad,\n                                        onError: handleImageError\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center justify-center bg-black bg-opacity-20 group-hover:bg-opacity-30 transition-all duration-300 z-20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-20 h-20 bg-white bg-opacity-95 rounded-full flex items-center justify-center hover:bg-opacity-100 transition-all duration-200 shadow-xl group-hover:scale-110 border-4 border-white border-opacity-20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8 text-gray-800 ml-1\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-block\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-secondary-500 text-white px-3 py-1 rounded-full text-sm font-semibold uppercase tracking-wide\",\n                                            children: \"SELL MORE WITH kommo\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl md:text-4xl font-bold leading-tight\",\n                                        children: \"A dedicated solution for startups and enterprises\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-primary-100 leading-relaxed\",\n                                        children: \"Get an extended Kommo presentation and see how our CRM solution can help your business grow and scale efficiently.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-primary-700 rounded-xl p-6 space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 text-secondary-400\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold\",\n                                                children: \"Get an extended Kommo presentation!\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    !isSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit(onSubmit),\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        ...register('email'),\n                                                        placeholder: \"Enter your email address\",\n                                                        className: \"w-full px-4 py-3 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-secondary-500 \".concat(errors.email ? 'ring-2 ring-red-500' : '')\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-300\",\n                                                        children: errors.email.message\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            submitMessage && !isSubmitted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg text-sm \".concat(submitMessage.includes('error') || submitMessage.includes('Sorry') ? 'bg-red-500 text-white' : 'bg-green-500 text-white'),\n                                                children: submitMessage\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                type: \"submit\",\n                                                variant: \"secondary\",\n                                                size: \"lg\",\n                                                disabled: isFormSubmitting,\n                                                className: \"w-full\",\n                                                children: isFormSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Subscribing...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 21\n                                                }, undefined) : 'Get Started'\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2 text-green-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: \"Successful!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-primary-200 text-sm\",\n                                                children: submitMessage\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    href: \"/contact\",\n                                    variant: \"outline\",\n                                    size: \"lg\",\n                                    className: \"border-white text-white hover:bg-white hover:text-primary-800\",\n                                    children: \"Talk to an expert\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, undefined),\n            isVideoModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4\",\n                onClick: ()=>setIsVideoModalOpen(false),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full max-w-4xl aspect-video\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsVideoModalOpen(false),\n                            className: \"absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors duration-200 z-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-8 h-8\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full bg-black rounded-lg overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                src: \"https://www.youtube.com/embed/mReZr_e70OA?autoplay=1&rel=0&modestbranding=1\",\n                                title: \"Kommo Demo - See how Kommo can transform your business\",\n                                className: \"w-full h-full\",\n                                allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\",\n                                allowFullScreen: true,\n                                frameBorder: \"0\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n                lineNumber: 259,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/SetMee/src/components/sections/CTAVideoSection.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CTAVideoSection, \"VFoeF/y2f/RZabPZsV5xtUjASE0=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm\n    ];\n});\n_c = CTAVideoSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CTAVideoSection);\nvar _c;\n$RefreshReg$(_c, \"CTAVideoSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/CTAVideoSection.tsx\n"));

/***/ })

});