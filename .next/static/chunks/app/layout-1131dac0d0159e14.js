(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},1786:(e,r,t)=>{"use strict";t.d(r,{default:()=>h});var s=t(5155),a=t(2115),n=t(6874),l=t.n(n),i=t(6766),o=t(2452);let d=[{label:"Our services",href:"#our-services"},{label:"Who are we",href:"#who-are-we"},{label:"About Kommo",href:"#about-kommo"}],c={label:"Contact",href:"/contact"};var m=t(9434);let h=()=>{let[e,r]=(0,a.useState)(!1);return(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-100 sticky top-0 z-50",children:(0,s.jsxs)(o.mc,{children:[(0,s.jsxs)("div",{className:"flex items-center justify-between h-16 md:h-20",children:[(0,s.jsx)(l(),{href:"/",className:"flex items-center",children:(0,s.jsx)("div",{className:"relative w-[120px] h-10",children:(0,s.jsx)(i.default,{src:"/images/setmee-logo.svg",alt:"Setmee Logo",fill:!0,sizes:"(max-width: 768px) 120px, 120px",className:"object-contain object-left",priority:!0})})}),(0,s.jsxs)("div",{className:"hidden md:flex items-center space-x-8",children:[(0,s.jsx)("nav",{className:"flex items-center space-x-8",children:d.map(e=>(0,s.jsx)(l(),{href:e.href,className:"text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200",children:e.label},e.href))}),(0,s.jsx)(o.$n,{href:c.href,variant:"primary",size:"sm",children:c.label})]}),(0,s.jsx)("button",{onClick:()=>{r(!e)},className:"md:hidden p-2 rounded-md text-gray-700 hover:text-primary-600 hover:bg-gray-100 transition-colors duration-200","aria-label":"Toggle menu",children:(0,s.jsx)("svg",{className:"h-6 w-6",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",viewBox:"0 0 24 24",stroke:"currentColor",children:e?(0,s.jsx)("path",{d:"M6 18L18 6M6 6l12 12"}):(0,s.jsx)("path",{d:"M4 6h16M4 12h16M4 18h16"})})})]}),(0,s.jsx)("div",{className:(0,m.cn)("md:hidden transition-all duration-300 ease-in-out overflow-hidden",e?"max-h-96 opacity-100":"max-h-0 opacity-0"),children:(0,s.jsxs)("nav",{className:"py-4 space-y-4 border-t border-gray-100",children:[d.map(e=>(0,s.jsx)(l(),{href:e.href,className:"block text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200",onClick:()=>r(!1),children:e.label},e.href)),(0,s.jsx)("div",{className:"pt-4 space-y-3",children:(0,s.jsx)(o.$n,{href:c.href,variant:"primary",size:"sm",className:"w-full justify-center",onClick:()=>r(!1),children:c.label})})]})})]})})}},2316:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6874,23)),Promise.resolve().then(t.t.bind(t,3063,23)),Promise.resolve().then(t.t.bind(t,347,23)),Promise.resolve().then(t.bind(t,1786)),Promise.resolve().then(t.bind(t,8650))},2452:(e,r,t)=>{"use strict";t.d(r,{$n:()=>i,Zp:()=>o,mc:()=>d,wn:()=>c,X3:()=>m});var s=t(5155);t(2115);var a=t(6874),n=t.n(a),l=t(9434);let i=e=>{let{children:r,variant:t="primary",size:a="md",href:i,onClick:o,disabled:d=!1,className:c,type:m="button",target:h="_self"}=e,x=(0,l.cn)("inline-flex items-center justify-center font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500",secondary:"bg-secondary-500 text-white hover:bg-secondary-600 focus:ring-secondary-500",outline:"border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white focus:ring-primary-500",ghost:"text-primary-600 hover:bg-primary-50 focus:ring-primary-500"}[t],{sm:"px-4 py-2 text-sm rounded-md",md:"px-6 py-3 text-base rounded-lg",lg:"px-8 py-4 text-lg rounded-xl"}[a],c),p={pointerEvents:"auto",position:"relative",zIndex:20,cursor:"pointer"};return i?i.startsWith("http")||i.startsWith("mailto:")||i.startsWith("tel:")?(0,s.jsx)("a",{href:i,target:h,rel:"_blank"===h?"noopener noreferrer":void 0,className:x,style:p,children:r}):(0,s.jsx)(n(),{href:i,className:x,style:p,children:r}):(0,s.jsx)("button",{type:m,onClick:o,disabled:d,className:x,style:p,children:r})},o=e=>{let{children:r,className:t,variant:a="default",padding:n="md",hover:i=!1}=e;return(0,s.jsx)("div",{className:(0,l.cn)({default:"bg-white border border-gray-200 rounded-lg",bordered:"bg-white border-2 border-gray-300 rounded-lg",elevated:"bg-white shadow-lg rounded-lg border border-gray-100",flat:"bg-gray-50 rounded-lg"}[a],{none:"",sm:"p-4",md:"p-6",lg:"p-8",xl:"p-10"}[n],i?"transition-all duration-200 hover:shadow-xl hover:-translate-y-1":"",t),children:r})},d=e=>{let{children:r,size:t="xl",className:a}=e;return(0,s.jsx)("div",{className:(0,l.cn)("mx-auto px-4 sm:px-6 lg:px-8",{sm:"max-w-2xl",md:"max-w-4xl",lg:"max-w-6xl",xl:"max-w-7xl",full:"max-w-full"}[t],a),children:r})},c=e=>{let{children:r,id:t,className:a,containerSize:n="xl",padding:i="lg",background:o="white"}=e;return(0,s.jsx)("section",{id:t,className:(0,l.cn)({none:"",sm:"py-8",md:"py-12",lg:"py-16 md:py-20",xl:"py-20 md:py-24"}[i],{white:"bg-white",gray:"bg-gray-50",primary:"bg-primary-800 text-white",transparent:"bg-transparent"}[o],a),children:(0,s.jsx)(d,{size:n,children:r})})},m=e=>{let{title:r,subtitle:t,description:a,align:n="center",className:i}=e;return(0,s.jsxs)("div",{className:(0,l.cn)("mb-12",{left:"text-left",center:"text-center",right:"text-right"}[n],i),children:[t&&(0,s.jsx)("p",{className:"text-sm font-semibold text-secondary-500 uppercase tracking-wide mb-2",children:t}),(0,s.jsx)("h2",{className:"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4",children:r}),a&&(0,s.jsx)("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto",children:a})]})};t(8650)},8650:(e,r,t)=>{"use strict";t.d(r,{default:()=>l});var s=t(5155),a=t(2115),n=t(9434);let l=()=>{let[e,r]=(0,a.useState)(!1),t=()=>{window.pageYOffset>window.innerHeight?r(!0):r(!1)};return(0,a.useEffect)(()=>(window.addEventListener("scroll",t),()=>{window.removeEventListener("scroll",t)}),[]),(0,s.jsx)("div",{className:"fixed bottom-6 right-6 z-50",children:(0,s.jsx)("button",{type:"button",onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},className:(0,n.cn)("inline-flex items-center justify-center w-12 h-12 rounded-full shadow-lg transition-all duration-300 ease-in-out transform hover:scale-110 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500","bg-primary-600/80 hover:bg-primary-700/90 text-white backdrop-blur-sm",e?"opacity-100 translate-y-0 pointer-events-auto":"opacity-0 translate-y-2 pointer-events-none"),"aria-label":"Scroll to top",style:{pointerEvents:e?"auto":"none"},children:(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2.5,d:"M5 15l7-7 7 7"})})})})}},9434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(2596),a=t(9688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}}},e=>{var r=r=>e(e.s=r);e.O(0,[690,342,766,441,684,358],()=>r(2316)),_N_E=e.O()}]);