import{validateFieldsNatively as e,toNestErrors as r}from"@hookform/resolvers";import s from"ajv";import o from"ajv-errors";import{appendErrors as a}from"react-hook-form";const t=(e,r)=>{const s={},o=e=>{"required"===e.keyword&&(e.instancePath+=`/${e.params.missingProperty}`);const o=e.instancePath.substring(1).replace(/\//g,".");if(s[o]||(s[o]={message:e.message,type:e.keyword}),r){const t=s[o].types,n=t&&t[e.keyword];s[o]=a(o,r,s,e.keyword,n?[].concat(n,e.message||""):e.message)}};for(let r=0;r<e.length;r+=1){const s=e[r];"errorMessage"===s.keyword?s.params.errors.forEach(e=>{e.message=s.message,o(e)}):o(s)}return s},n=(a,n,i={})=>async(c,m,l)=>{const d=new s(Object.assign({},{allErrors:!0,validateSchema:!0},n));o(d);const g=d.compile(Object.assign({$async:i&&"async"===i.mode},a)),p=g(c);return l.shouldUseNativeValidation&&e({},l),p?{values:c,errors:{}}:{values:{},errors:r(t(g.errors,!l.shouldUseNativeValidation&&"all"===l.criteriaMode),l)}};export{n as ajvResolver};
//# sourceMappingURL=ajv.modern.mjs.map
