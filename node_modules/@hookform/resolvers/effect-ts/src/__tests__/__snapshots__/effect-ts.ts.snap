// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`effectTsResolver > should return a single error from effectTsResolver when validation fails 1`] = `
{
  "errors": {
    "animal": {
      "message": "Expected string, actual ["dog"]",
      "ref": undefined,
      "type": "Type",
    },
    "luckyNumbers": [
      ,
      ,
      {
        "message": "Expected number, actual "3"",
        "ref": undefined,
        "type": "Type",
      },
    ],
    "password": {
      "message": "At least 1 special character.",
      "ref": {
        "name": "password",
      },
      "type": "Refinement",
    },
    "vehicles": [
      ,
      {
        "horsepower": {
          "message": "is missing",
          "ref": undefined,
          "type": "Missing",
        },
      },
    ],
  },
  "values": {},
}
`;

exports[`effectTsResolver > should return all the errors from effectTsResolver when validation fails with \`validateAllFieldCriteria\` set to true 1`] = `
{
  "errors": {
    "phoneNumber": {
      "message": "Please enter a valid phone number in international format.",
      "ref": {
        "name": "phoneNumber",
      },
      "type": "Refinement",
      "types": {
        "Refinement": "Please enter a valid phone number in international format.",
        "Type": "Expected undefined, actual "123"",
      },
    },
  },
  "values": {},
}
`;

exports[`effectTsResolver > should return the first error from effectTsResolver when validation fails with \`validateAllFieldCriteria\` set to firstError 1`] = `
{
  "errors": {
    "phoneNumber": {
      "message": "Please enter a valid phone number in international format.",
      "ref": {
        "name": "phoneNumber",
      },
      "type": "Refinement",
    },
  },
  "values": {},
}
`;
