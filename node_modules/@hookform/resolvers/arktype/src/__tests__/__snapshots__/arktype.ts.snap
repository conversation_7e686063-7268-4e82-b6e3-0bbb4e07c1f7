// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`arktypeResolver > should return a single error from arktypeResolver when validation fails 1`] = `
{
  "errors": {
    "accessToken": {
      "message": "accessToken must be a number or a string (was missing)",
      "ref": undefined,
      "type": "required",
    },
    "birthYear": {
      "message": "birthYear must be a number (was a string)",
      "ref": undefined,
      "type": "domain",
    },
    "dateStr": {
      "message": "dateStr must be a Date (was missing)",
      "ref": undefined,
      "type": "required",
    },
    "email": {
      "message": "email must be an email address (was "")",
      "ref": {
        "name": "email",
      },
      "type": "pattern",
    },
    "enabled": {
      "message": "enabled must be boolean (was missing)",
      "ref": undefined,
      "type": "required",
    },
    "like": [
      {
        "id": {
          "message": "like[0].id must be a number (was a string)",
          "ref": undefined,
          "type": "domain",
        },
        "name": {
          "message": "like[0].name must be a string (was missing)",
          "ref": undefined,
          "type": "required",
        },
      },
    ],
    "password": {
      "message": "password must be matched by .*[A-Za-z].* or matched by .*\\d.* (was "___")",
      "ref": {
        "name": "password",
      },
      "type": "union",
    },
    "repeatPassword": {
      "message": "repeatPassword must be a string (was missing)",
      "ref": undefined,
      "type": "required",
    },
    "tags": {
      "message": "tags must be an array (was missing)",
      "ref": undefined,
      "type": "required",
    },
    "username": {
      "message": "username must be a string (was missing)",
      "ref": {
        "name": "username",
      },
      "type": "required",
    },
  },
  "values": {},
}
`;
