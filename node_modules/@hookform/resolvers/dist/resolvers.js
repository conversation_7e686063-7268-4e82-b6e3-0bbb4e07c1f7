var e=require("react-hook-form"),r=function(r,t,i){if(r&&"reportValidity"in r){var n=e.get(i,t);r.setCustomValidity(n&&n.message||""),r.reportValidity()}},t=function(e,t){var i=function(i){var n=t.fields[i];n&&n.ref&&"reportValidity"in n.ref?r(n.ref,i,e):n&&n.refs&&n.refs.forEach(function(t){return r(t,i,e)})};for(var n in t.fields)i(n)},i=function(e,r){var t=n(r);return e.some(function(e){return n(e).match("^"+t+"\\.\\d+")})};function n(e){return e.replace(/\]|\[/g,"")}exports.toNestErrors=function(r,n){n.shouldUseNativeValidation&&t(r,n);var a={};for(var o in r){var s=e.get(n.fields,o),f=Object.assign(r[o]||{},{ref:s&&s.ref});if(i(n.names||Object.keys(r),o)){var u=Object.assign({},e.get(a,o));e.set(u,"root",f),e.set(a,o,u)}else e.set(a,o,f)}return a},exports.validateFieldsNatively=t;
//# sourceMappingURL=resolvers.js.map
