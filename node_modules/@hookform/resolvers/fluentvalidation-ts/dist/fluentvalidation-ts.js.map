{"version": 3, "file": "fluentvalidation-ts.js", "sources": ["../src/fluentvalidation-ts.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport {\n  AsyncValidator,\n  ValidationErrors,\n  Validator,\n} from 'fluentvalidation-ts';\nimport { FieldError, FieldValues, Resolver } from 'react-hook-form';\n\nfunction traverseObject<T>(\n  object: ValidationErrors<T>,\n  errors: Record<string, FieldError>,\n  parentIndices: (string | number)[] = [],\n) {\n  for (const key in object) {\n    const currentIndex = [...parentIndices, key];\n    const currentValue = object[key];\n\n    if (Array.isArray(currentValue)) {\n      currentValue.forEach((item: any, index: number) => {\n        traverseObject(item, errors, [...currentIndex, index]);\n      });\n    } else if (typeof currentValue === 'object' && currentValue !== null) {\n      traverseObject(currentValue, errors, currentIndex);\n    } else if (typeof currentValue === 'string') {\n      errors[currentIndex.join('.')] = {\n        type: 'validation',\n        message: currentValue,\n      };\n    }\n  }\n}\n\nfunction parseErrorSchema<T>(\n  validationErrors: ValidationErrors<T>,\n  validateAllFieldCriteria: boolean,\n) {\n  if (validateAllFieldCriteria) {\n    // TODO: check this but i think its always one validation error\n  }\n\n  const errors: Record<string, FieldError> = {};\n  traverseObject(validationErrors, errors);\n\n  return errors;\n}\n\n/**\n * Creates a resolver for react-hook-form using FluentValidation schema validation\n * @param {Validator<TFieldValues>} validator - The FluentValidation validator to use\n * @returns {Resolver<TFieldValues>} A resolver function compatible with react-hook-form\n * @example\n * import { Validator } from 'fluentvalidation-ts';\n *\n * class SchemaValidator extends Validator<Schema> {\n *   constructor() {\n *     super();\n *     this.ruleFor('username').notEmpty();\n *     this.ruleFor('password').notEmpty();\n *   }\n * }\n *\n * const validator = new SchemaValidator();\n *\n * useForm({\n *   resolver: fluentValidationResolver(validator)\n * });\n */\nexport function fluentValidationResolver<TFieldValues extends FieldValues>(\n  validator: Validator<TFieldValues>,\n): Resolver<TFieldValues> {\n  return async (values, _context, options) => {\n    const validationResult = validator.validate(values);\n    const isValid = Object.keys(validationResult).length === 0;\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return isValid\n      ? {\n          values: values,\n          errors: {},\n        }\n      : {\n          values: {},\n          errors: toNestErrors(\n            parseErrorSchema(\n              validationResult,\n              !options.shouldUseNativeValidation &&\n                options.criteriaMode === 'all',\n            ),\n            options,\n          ),\n        };\n  };\n}\n\nexport function fluentAsyncValidationResolver<\n  TFieldValues extends FieldValues,\n  TValidator extends AsyncValidator<TFieldValues>,\n>(validator: TValidator): Resolver<TFieldValues> {\n  return async (values, _context, options) => {\n    const validationResult = await validator.validateAsync(values);\n    const isValid = Object.keys(validationResult).length === 0;\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return isValid\n      ? {\n          values: values,\n          errors: {},\n        }\n      : {\n          values: {},\n          errors: toNestErrors(\n            parseErrorSchema(\n              validationResult,\n              !options.shouldUseNativeValidation &&\n                options.criteriaMode === 'all',\n            ),\n            options,\n          ),\n        };\n  };\n}\n"], "names": ["traverseObject", "object", "errors", "parentIndices", "_loop", "currentIndex", "concat", "key", "currentValue", "Array", "isArray", "for<PERSON>ach", "item", "index", "join", "type", "message", "parseErrorSchema", "validationErrors", "validateAllFieldCriteria", "validator", "values", "_context", "options", "Promise", "resolve", "validateAsync", "then", "validationResult", "<PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "length", "shouldUseNativeValidation", "validateFieldsNatively", "toNestErrors", "e", "reject", "validate"], "mappings": "qCAQA,SAASA,EACPC,EACAC,EACAC,YAAAA,IAAAA,EAAqC,IAAE,IAAAC,EAAAA,WAGrC,IAAMC,EAAY,GAAAC,OAAOH,EAAa,CAAEI,IAClCC,EAAeP,EAAOM,GAExBE,MAAMC,QAAQF,GAChBA,EAAaG,QAAQ,SAACC,EAAWC,GAC/Bb,EAAeY,EAAMV,EAAMI,GAAAA,OAAMD,EAAY,CAAEQ,IACjD,GACiC,iBAAjBL,GAA8C,OAAjBA,EAC7CR,EAAeQ,EAAcN,EAAQG,GACJ,iBAAjBG,IAChBN,EAAOG,EAAaS,KAAK,MAAQ,CAC/BC,KAAM,aACNC,QAASR,GAGf,EAhBA,IAAK,IAAMD,KAAON,EAAMG,GAiB1B,CAEA,SAASa,EACPC,EACAC,GAMA,IAAMjB,EAAqC,CAAA,EAG3C,OAFAF,EAAekB,EAAkBhB,GAE1BA,CACT,uCAmDM,SAGJkB,GACA,OAAcC,SAAAA,EAAQC,EAAUC,GAAO,WAAIC,QAAAC,QACVL,EAAUM,cAAcL,IAAOM,KAAxDC,SAAAA,GACN,IAAMC,EAAmD,IAAzCC,OAAOC,KAAKH,GAAkBI,OAI9C,OAFAT,EAAQU,2BAA6BC,EAAAA,uBAAuB,CAAE,EAAEX,GAEzDM,EACH,CACER,OAAQA,EACRnB,OAAQ,IAEV,CACEmB,OAAQ,GACRnB,OAAQiC,EAAYA,aAClBlB,EACEW,GAIFL,GAEF,EACR,CAAC,MAAAa,GAAA,OAAAZ,QAAAa,OAAAD,EACH,CAAA,CAAA,mCAvDgB,SACdhB,GAEA,OAAcC,SAAAA,EAAQC,EAAUC,GAAW,IACzC,IAAMK,EAAmBR,EAAUkB,SAASjB,GACtCQ,EAAmD,IAAzCC,OAAOC,KAAKH,GAAkBI,OAI9C,OAFAT,EAAQU,2BAA6BC,EAAAA,uBAAuB,CAAE,EAAEX,GAEhEC,QAAAC,QAAOI,EACH,CACER,OAAQA,EACRnB,OAAQ,IAEV,CACEmB,OAAQ,GACRnB,OAAQiC,EAAAA,aACNlB,EACEW,GAIFL,IAGV,CAAC,MAAAa,UAAAZ,QAAAa,OAAAD,EAAA,CAAA,CACH"}