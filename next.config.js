/** @type {import('next').NextConfig} */
// Add logging for static files
const path = require('path');
const fs = require('fs');

const nextConfig = {
  // Enable experimental features
  experimental: {
    // optimizeCss: true, // Disabled due to build issues
    optimizePackageImports: ['lucide-react'],
  },
  
  // Image optimization
  images: {
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 60 * 60 * 24 * 365, // 1 year
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**.ytimg.com',
      },
      {
        protocol: 'https',
        hostname: '**.youtube.com',
      },
      {
        protocol: 'https',
        hostname: 'i.ytimg.com',
      },
      {
        protocol: 'https',
        hostname: 'img.youtube.com',
      },
    ],
  },

  // Compression and optimization
  compress: true,

  // Security and Cache Headers
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
          {
            key: 'Content-Security-Policy',
            value: [
              "default-src 'self';",
              "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.youtube.com https://s.ytimg.com;",
              "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;",
              `img-src 'self' data: blob: https://*.youtube.com https://*.ytimg.com https://i.ytimg.com https://img.youtube.com ${process.env.NODE_ENV === 'development' ? 'http://localhost:3000 http://localhost:3001' : ''}`,
              "font-src 'self' https://fonts.gstatic.com data:;",
              `connect-src 'self' https://*.googleapis.com https://*.youtube.com ${process.env.NODE_ENV === 'development' ? 'http://localhost:3000 http://localhost:3001' : ''}`,
              "frame-src 'self' https://www.youtube.com;",
              `media-src 'self' https://*.youtube.com ${process.env.NODE_ENV === 'development' ? 'http://localhost:3000 http://localhost:3001' : ''}`,
              "object-src 'none'"
            ].join(' '),
          },
        ],
      },
      {
        source: '/images/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },

  // Redirects for SEO
  async redirects() {
    return [
      {
        source: '/home',
        destination: '/',
        permanent: true,
      },
    ];
  },

  // Performance optimizations
  poweredByHeader: false,
  reactStrictMode: true,
  
  // Bundle analyzer (uncomment for analysis)
  // webpack: (config, { isServer }) => {
  //   if (!isServer) {
  //     config.resolve.fallback = {
  //       ...config.resolve.fallback,
  //       fs: false,
  //     };
  //   }
  //   return config;
  // },
};

module.exports = nextConfig;
