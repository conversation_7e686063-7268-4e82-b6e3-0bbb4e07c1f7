// CMS Types for Content Management System

export type ContentStatus = 'draft' | 'published' | 'archived';

// Base content interface
export interface BaseContent {
  id: string;
  title: string;
  slug: string;
  status: ContentStatus;
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
  author: string;
  seo: SEOData;
}

// SEO data structure
export interface SEOData {
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  canonicalUrl?: string;
  noIndex?: boolean;
  noFollow?: boolean;
}

// Page content structure
export interface PageContent extends BaseContent {
  type: 'page';
  sections: PageSection[];
  template?: string;
}

// Page section types
export interface PageSection {
  id: string;
  type: 'hero' | 'text' | 'features' | 'cta' | 'testimonials' | 'contact' | 'custom';
  title?: string;
  content: Record<string, any>;
  order: number;
  visible: boolean;
}

// Blog post structure
export interface BlogPost extends BaseContent {
  type: 'blog';
  excerpt: string;
  content: string;
  featuredImage?: string;
  tags: string[];
  category: string;
  readingTime?: number;
}

// Site settings
export interface SiteSettings {
  siteName: string;
  siteDescription: string;
  siteUrl: string;
  logo?: string;
  favicon?: string;
  socialMedia: {
    twitter?: string;
    linkedin?: string;
    facebook?: string;
    instagram?: string;
  };
  contact: {
    email: string;
    phone?: string;
    address?: string;
  };
  analytics: {
    googleAnalytics?: string;
    yandexMetrica?: string;
  };
  seo: {
    defaultMetaTitle: string;
    defaultMetaDescription: string;
    defaultKeywords: string[];
    ogImage: string;
  };
}

// Content management operations
export interface ContentOperation {
  action: 'create' | 'update' | 'delete' | 'publish' | 'unpublish';
  contentType: 'page' | 'blog' | 'settings';
  contentId: string;
  timestamp: string;
  author: string;
}

// Admin user
export interface AdminUser {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'editor';
  lastLogin?: string;
  createdAt: string;
}

// Content filters and search
export interface ContentFilters {
  status?: ContentStatus;
  type?: 'page' | 'blog';
  author?: string;
  category?: string;
  tags?: string[];
  search?: string;
  dateFrom?: string;
  dateTo?: string;
}

// API response types
export interface ContentListResponse {
  items: (PageContent | BlogPost)[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Form data types for admin interface
export interface PageFormData {
  title: string;
  slug: string;
  status: ContentStatus;
  template?: string;
  sections: PageSection[];
  seo: SEOData;
}

export interface BlogFormData {
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  status: ContentStatus;
  featuredImage?: string;
  tags: string[];
  category: string;
  seo: SEOData;
}

// Content validation schemas
export interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// File upload types
export interface UploadedFile {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  uploadedAt: string;
}

// Navigation menu structure
export interface NavigationItem {
  id: string;
  label: string;
  href: string;
  order: number;
  visible: boolean;
  children?: NavigationItem[];
}

export interface NavigationMenu {
  id: string;
  name: string;
  items: NavigationItem[];
  location: 'header' | 'footer' | 'sidebar';
}
