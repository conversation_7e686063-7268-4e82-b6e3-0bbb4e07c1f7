'use client';

import { useEffect } from 'react';
import { getSettings } from '@/lib/cms-utils';

const BlogHero: React.FC = () => {
  useEffect(() => {
    // Функция для плавного обновления контента
    const updateHeroContent = () => {
      const siteSettings = getSettings();

      const heroElement = document.getElementById('blog-hero');
      const titleElement = document.getElementById('blog-hero-title');
      const descriptionElement = document.getElementById('blog-hero-description');

      if (heroElement && titleElement && descriptionElement && siteSettings) {
        const newTitle = siteSettings.blogHeroTitle as string;
        const newDescription = siteSettings.blogHeroDescription as string;
        const newBackgroundColor = siteSettings.blogHeroBackgroundColor as string;

        // Проверяем, есть ли изменения
        const titleChanged = newTitle && titleElement.textContent !== newTitle;
        const descriptionChanged = newDescription && descriptionElement.textContent !== newDescription;
        const colorChanged = newBackgroundColor && heroElement.style.backgroundColor !== newBackgroundColor;

        if (titleChanged || descriptionChanged || colorChanged) {
          // Добавляем класс для плавного перехода
          heroElement.style.transition = 'all 0.3s ease';

          if (titleChanged) {
            titleElement.style.opacity = '0';
            setTimeout(() => {
              titleElement.textContent = newTitle;
              titleElement.style.opacity = '1';
            }, 150);
          }

          if (descriptionChanged) {
            descriptionElement.style.opacity = '0';
            setTimeout(() => {
              descriptionElement.textContent = newDescription;
              descriptionElement.style.opacity = '1';
            }, 150);
          }

          if (colorChanged) {
            heroElement.style.backgroundColor = newBackgroundColor;
          }
        }
      }
    };

    // Запускаем обновление после монтирования компонента
    const timeoutId = setTimeout(updateHeroContent, 50);

    return () => clearTimeout(timeoutId);
  }, []);

  // Этот компонент не рендерит ничего видимого
  return null;
};

export default BlogHero;
