'use client';

import React, { useState, useEffect } from 'react';
import { getSettings } from '@/lib/cms-utils';

interface BlogHeroProps {
  defaultTitle?: string;
  defaultDescription?: string;
  defaultBackgroundColor?: string;
}

const BlogHero: React.FC<BlogHeroProps> = ({
  defaultTitle = 'SetMee Blog',
  defaultDescription = 'Expert articles about Kommo CRM, integrations, business automation and best practices',
  defaultBackgroundColor = '#1e40af'
}) => {
  const [settings, setSettings] = useState({
    blogHeroTitle: defaultTitle,
    blogHeroDescription: defaultDescription,
    blogHeroBackgroundColor: defaultBackgroundColor
  });
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    // Загружаем настройки на клиенте только после монтирования
    const siteSettings = getSettings();
    setSettings({
      blogHeroTitle: siteSettings?.blogHeroTitle as string || defaultTitle,
      blogHeroDescription: siteSettings?.blogHeroDescription as string || defaultDescription,
      blogHeroBackgroundColor: siteSettings?.blogHeroBackgroundColor as string || defaultBackgroundColor
    });
  }, [defaultTitle, defaultDescription, defaultBackgroundColor]);

  // Показываем серверную версию до монтирования клиентского компонента
  if (!mounted) {
    return (
      <div
        className="py-16 px-4 sm:px-6 lg:px-8"
        style={{ backgroundColor: defaultBackgroundColor }}
      >
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
            {defaultTitle}
          </h1>
          <p className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
            {defaultDescription}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div
      className="py-16 px-4 sm:px-6 lg:px-8 transition-all duration-300"
      style={{ backgroundColor: settings.blogHeroBackgroundColor }}
    >
      <div className="max-w-4xl mx-auto text-center">
        <h1 className="text-4xl md:text-5xl font-bold text-white mb-6 transition-all duration-300">
          {settings.blogHeroTitle}
        </h1>
        <p className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed transition-all duration-300">
          {settings.blogHeroDescription}
        </p>
      </div>
    </div>
  );
};

export default BlogHero;
