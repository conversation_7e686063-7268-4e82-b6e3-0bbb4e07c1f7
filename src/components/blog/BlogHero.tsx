'use client';

import React, { useState, useEffect } from 'react';
import { getSettings } from '@/lib/cms-utils';

interface BlogHeroProps {
  defaultTitle?: string;
  defaultDescription?: string;
  defaultBackgroundColor?: string;
}

const BlogHero: React.FC<BlogHeroProps> = ({
  defaultTitle = 'Блог Setmee',
  defaultDescription = 'Полезные статьи о Kommo CRM, интеграциях, автоматизации бизнеса и лучших практиках',
  defaultBackgroundColor = '#1e40af'
}) => {
  const [settings, setSettings] = useState({
    blogHeroTitle: defaultTitle,
    blogHeroDescription: defaultDescription,
    blogHeroBackgroundColor: defaultBackgroundColor
  });

  useEffect(() => {
    // Загружаем настройки на клиенте
    const siteSettings = getSettings();
    setSettings({
      blogHeroTitle: siteSettings?.blogHeroTitle as string || defaultTitle,
      blogHeroDescription: siteSettings?.blogHeroDescription as string || defaultDescription,
      blogHeroBackgroundColor: siteSettings?.blogHeroBackgroundColor as string || defaultBackgroundColor
    });
  }, [defaultTitle, defaultDescription, defaultBackgroundColor]);

  return (
    <div 
      className="py-16 px-4 sm:px-6 lg:px-8"
      style={{ backgroundColor: settings.blogHeroBackgroundColor }}
    >
      <div className="max-w-4xl mx-auto text-center">
        <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
          {settings.blogHeroTitle}
        </h1>
        <p className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
          {settings.blogHeroDescription}
        </p>
      </div>
    </div>
  );
};

export default BlogHero;
