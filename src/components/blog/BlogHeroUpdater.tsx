'use client';

import { useEffect } from 'react';
import { getSettings } from '@/lib/cms-utils';

const BlogHeroUpdater: React.FC = () => {
  useEffect(() => {
    // Функция для обновления Hero блока без мерцания
    const updateHeroContent = () => {
      const siteSettings = getSettings();
      
      const heroElement = document.getElementById('blog-hero');
      const titleElement = document.getElementById('blog-hero-title');
      const descriptionElement = document.getElementById('blog-hero-description');
      
      if (heroElement && titleElement && descriptionElement && siteSettings) {
        const newTitle = siteSettings.blogHeroTitle as string;
        const newDescription = siteSettings.blogHeroDescription as string;
        const newBackgroundColor = siteSettings.blogHeroBackgroundColor as string;
        
        // Получаем текущие значения
        const currentTitle = titleElement.textContent?.trim();
        const currentDescription = descriptionElement.textContent?.trim();
        const currentBgColor = heroElement.style.backgroundColor;
        
        // Обновляем только если есть реальные изменения
        let hasChanges = false;
        
        if (newTitle && newTitle !== currentTitle) {
          titleElement.textContent = newTitle;
          hasChanges = true;
        }
        
        if (newDescription && newDescription !== currentDescription) {
          descriptionElement.textContent = newDescription;
          hasChanges = true;
        }
        
        if (newBackgroundColor && newBackgroundColor !== currentBgColor) {
          heroElement.style.backgroundColor = newBackgroundColor;
          hasChanges = true;
        }
        
        // Логируем изменения для отладки
        if (hasChanges) {
          console.log('Hero блок обновлен:', {
            title: newTitle,
            description: newDescription,
            backgroundColor: newBackgroundColor
          });
        }
      }
    };

    // Запускаем обновление после полной загрузки страницы
    if (document.readyState === 'complete') {
      updateHeroContent();
    } else {
      window.addEventListener('load', updateHeroContent);
      return () => window.removeEventListener('load', updateHeroContent);
    }
  }, []);

  // Этот компонент не рендерит ничего видимого
  return null;
};

export default BlogHeroUpdater;
