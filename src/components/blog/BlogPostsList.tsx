'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { getClientBlogPosts } from '@/lib/cms-utils';
import { BlogPost } from '@/types/cms';

const BlogPostsList: React.FC = () => {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [mounted, setMounted] = useState(false);

  const loadPosts = () => {
    try {
      const publishedPosts = getClientBlogPosts('published');
      setPosts(publishedPosts);
      console.log(`BlogPostsList: Загружено ${publishedPosts.length} опубликованных постов`);
    } catch (error) {
      console.error('Ошибка при загрузке постов для блога:', error);
      setPosts([]);
    }
  };

  useEffect(() => {
    setMounted(true);
    loadPosts();

    // Слушаем события обновления контента
    const handleContentUpdate = (event: any) => {
      console.log('BlogPostsList: Получено событие обновления контента', event.detail);
      if (event.detail.type === 'blog') {
        loadPosts();
      }
    };

    window.addEventListener('cms-content-updated', handleContentUpdate);

    return () => {
      window.removeEventListener('cms-content-updated', handleContentUpdate);
    };
  }, []);

  if (!mounted) {
    return (
      <div className="text-center py-16">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
        <p className="mt-4 text-gray-600">Загрузка статей...</p>
      </div>
    );
  }

  if (posts.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="max-w-md mx-auto">
          <svg className="mx-auto h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
          </svg>
          <h3 className="mt-4 text-lg font-medium text-gray-900">No articles yet</h3>
          <p className="mt-2 text-gray-500">
            We're working on creating great content for you. Check back soon!
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
      {posts.map((post) => (
        <article key={post.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
          <Link href={`/blog/${post.slug}`}>
            <div className="p-6">
              <div className="flex items-center text-sm text-gray-500 mb-3">
                <time dateTime={post.createdAt}>
                  {new Date(post.createdAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </time>
              </div>
              <h2 className="text-xl font-semibold text-gray-900 mb-3 line-clamp-2">
                {post.title}
              </h2>
              <p className="text-gray-600 line-clamp-3 mb-4">
                {post.excerpt}
              </p>
              {post.tags && post.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {post.tags.slice(0, 2).map((tag, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-700"
                    >
                      {tag}
                    </span>
                  ))}
                  {post.tags.length > 2 && (
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-700">
                      +{post.tags.length - 2}
                    </span>
                  )}
                </div>
              )}
            </div>
          </Link>
        </article>
      ))}
    </div>
  );
};

export default BlogPostsList;
