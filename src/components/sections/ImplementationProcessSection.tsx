import React from 'react';
import Image from 'next/image';
import { Section, SectionHeader, Button } from '@/components/ui';
import { implementationSteps } from '@/data/content';

const ImplementationProcessSection: React.FC = () => {
  return (
    <Section
      id="implementation-process"
      padding="xl"
      background="white"
    >
      <div className="grid lg:grid-cols-2 gap-12 items-center">
        {/* Content */}
        <div className="space-y-8">
          <SectionHeader
            title="To increase efficiency, we have divided the implementation process into several stages"
            align="left"
            className="mb-8"
          />

          <div className="space-y-6">
            {implementationSteps.map((step) => (
              <div key={step.id} className="flex gap-4">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center font-bold text-sm">
                    {step.order}
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {step.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {step.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          <div className="pt-6">
            <Button
              href="/contact"
              variant="primary"
              size="lg"
            >
              Contact a specialist
            </Button>
          </div>
        </div>

        {/* Visual */}
        <div className="relative">
          <div className="relative bg-gradient-to-br from-primary-50 to-secondary-50 rounded-2xl p-8 overflow-hidden">
            {/* Background decoration */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-primary-100 rounded-full opacity-50 -translate-y-16 translate-x-16"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-secondary-100 rounded-full opacity-50 translate-y-12 -translate-x-12"></div>
            
            {/* Main image */}
            <div className="relative z-10">
              <Image
                src="/images/setmee-bg-1.png"
                alt="Implementation process illustration"
                width={500}
                height={400}
                className="w-full h-auto rounded-lg"
              />
            </div>

            {/* Floating cards */}
            <div className="absolute top-4 left-4 bg-white rounded-lg shadow-lg p-3 z-20">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-xs font-medium text-gray-700">Audit Complete</span>
              </div>
            </div>

            <div className="absolute bottom-4 right-4 bg-white rounded-lg shadow-lg p-3 z-20">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                <span className="text-xs font-medium text-gray-700">Setup in Progress</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Section>
  );
};

export default ImplementationProcessSection;
