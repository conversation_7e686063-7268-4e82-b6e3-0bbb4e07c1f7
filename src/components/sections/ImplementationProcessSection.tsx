import React from 'react';
import Image from 'next/image';
import { Section, SectionHeader, Button } from '@/components/ui';
import { implementationSteps } from '@/data/content';

const ImplementationProcessSection: React.FC = () => {
  return (
    <Section
      id="implementation-process"
      padding="xl"
      background="white"
    >
      <div className="grid lg:grid-cols-2 gap-12 items-center">
        {/* Content */}
        <div className="space-y-8">
          <SectionHeader
            title="To increase efficiency, we have divided the implementation process into several stages"
            align="left"
            className="mb-8"
          />

          <div className="space-y-6">
            {implementationSteps.map((step) => (
              <div key={step.id} className="flex gap-4">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center font-bold text-sm">
                    {step.order}
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {step.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {step.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          <div className="pt-6">
            <Button
              href="/contact"
              variant="primary"
              size="lg"
            >
              Contact a specialist
            </Button>
          </div>
        </div>

        {/* Visual */}
        <div className="relative">
          <div className="space-y-4">
            {/* Stage 1: Audit Dashboard */}
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-xs">1</div>
                <span className="text-sm font-semibold text-blue-800">Audit of the sales department</span>
              </div>
              <div className="bg-white rounded-lg p-3">
                <svg viewBox="0 0 200 80" className="w-full h-16">
                  {/* Audit checklist */}
                  <rect x="10" y="10" width="180" height="60" rx="6" fill="#f8fafc" stroke="#e2e8f0"/>

                  {/* Checklist items */}
                  <rect x="20" y="20" width="60" height="8" rx="4" fill="#3b82f6"/>
                  <circle cx="85" cy="24" r="3" fill="#10b981"/>
                  <path d="M83 24l1.5 1.5 3-3" stroke="white" strokeWidth="0.8" fill="none"/>

                  <rect x="20" y="35" width="50" height="8" rx="4" fill="#f59e0b"/>
                  <circle cx="75" cy="39" r="3" fill="#f59e0b"/>
                  <text x="75" y="41" textAnchor="middle" fontSize="4" fill="white">!</text>

                  <rect x="20" y="50" width="55" height="8" rx="4" fill="#ef4444"/>
                  <circle cx="80" cy="54" r="3" fill="#ef4444"/>
                  <path d="M78 52l4 4M82 52l-4 4" stroke="white" strokeWidth="0.8"/>

                  {/* Progress indicator */}
                  <rect x="110" y="20" width="70" height="40" rx="4" fill="#f1f5f9"/>
                  <rect x="115" y="25" width="60" height="6" rx="3" fill="#e2e8f0"/>
                  <rect x="115" y="25" width="42" height="6" rx="3" fill="#3b82f6"/>
                  <text x="145" y="45" textAnchor="middle" fontSize="8" fill="#3b82f6">70% Complete</text>

                  {/* Scanning dots */}
                  <circle cx="160" cy="55" r="2" fill="#3b82f6" opacity="0.8">
                    <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite"/>
                  </circle>
                  <circle cx="170" cy="55" r="2" fill="#3b82f6" opacity="0.6">
                    <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" begin="0.3s" repeatCount="indefinite"/>
                  </circle>
                </svg>
              </div>
            </div>

            {/* Stage 2: System Setup Dashboard */}
            <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-4 border border-purple-200">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-6 h-6 bg-purple-600 text-white rounded-full flex items-center justify-center font-bold text-xs">2</div>
                <span className="text-sm font-semibold text-purple-800">System Setup</span>
              </div>
              <div className="bg-white rounded-lg p-3">
                <svg viewBox="0 0 200 80" className="w-full h-16">
                  {/* Setup interface */}
                  <rect x="10" y="10" width="180" height="60" rx="6" fill="#f8fafc" stroke="#e2e8f0"/>

                  {/* Funnel configuration */}
                  <rect x="20" y="20" width="50" height="40" rx="4" fill="#f3e8ff"/>
                  <rect x="25" y="25" width="40" height="6" rx="3" fill="#8b5cf6"/>
                  <rect x="27" y="35" width="36" height="6" rx="3" fill="#8b5cf6"/>
                  <rect x="29" y="45" width="32" height="6" rx="3" fill="#8b5cf6"/>
                  <rect x="31" y="55" width="28" height="4" rx="2" fill="#8b5cf6"/>

                  {/* Integration icons */}
                  <circle cx="90" cy="30" r="8" fill="#10b981"/>
                  <text x="90" y="33" textAnchor="middle" fontSize="6" fill="white">API</text>

                  <circle cx="110" cy="30" r="8" fill="#3b82f6"/>
                  <text x="110" y="33" textAnchor="middle" fontSize="6" fill="white">WEB</text>

                  <circle cx="130" cy="30" r="8" fill="#f59e0b"/>
                  <text x="130" y="33" textAnchor="middle" fontSize="6" fill="white">TEL</text>

                  {/* Configuration progress */}
                  <rect x="85" y="45" width="60" height="20" rx="4" fill="#f1f5f9"/>
                  <rect x="90" y="50" width="15" height="4" rx="2" fill="#10b981"/>
                  <rect x="90" y="57" width="12" height="4" rx="2" fill="#8b5cf6"/>
                  <text x="110" y="53" fontSize="6" fill="#10b981">Funnels ✓</text>
                  <text x="110" y="60" fontSize="6" fill="#8b5cf6">Fields ⚙</text>

                  {/* Setup animation */}
                  <circle cx="160" cy="40" r="3" fill="#8b5cf6">
                    <animate attributeName="r" values="3;5;3" dur="2s" repeatCount="indefinite"/>
                    <animate attributeName="opacity" values="1;0.5;1" dur="2s" repeatCount="indefinite"/>
                  </circle>
                </svg>
              </div>
            </div>

            {/* Stage 3: Training Dashboard */}
            <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-4 border border-green-200">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center font-bold text-xs">3</div>
                <span className="text-sm font-semibold text-green-800">Training</span>
              </div>
              <div className="bg-white rounded-lg p-3">
                <svg viewBox="0 0 200 80" className="w-full h-16">
                  {/* Training interface */}
                  <rect x="10" y="10" width="180" height="60" rx="6" fill="#f8fafc" stroke="#e2e8f0"/>

                  {/* Video call interface */}
                  <rect x="20" y="20" width="80" height="40" rx="4" fill="#f0fdf4"/>
                  <circle cx="60" cy="35" r="8" fill="#10b981"/>
                  <circle cx="60" cy="32" r="3" fill="white"/>
                  <path d="M55 40 Q60 38 65 40" stroke="white" strokeWidth="1" fill="none"/>
                  <text x="60" y="50" textAnchor="middle" fontSize="6" fill="#10b981">Trainer</text>

                  {/* Training materials */}
                  <rect x="110" y="20" width="70" height="40" rx="4" fill="#f1f5f9"/>
                  <rect x="115" y="25" width="20" height="12" rx="2" fill="#3b82f6"/>
                  <text x="125" y="32" textAnchor="middle" fontSize="5" fill="white">PDF</text>
                  <rect x="140" y="25" width="20" height="12" rx="2" fill="#f59e0b"/>
                  <text x="150" y="32" textAnchor="middle" fontSize="5" fill="white">VID</text>
                  <rect x="165" y="25" width="10" height="12" rx="2" fill="#10b981"/>
                  <text x="170" y="32" textAnchor="middle" fontSize="4" fill="white">✓</text>

                  {/* Progress tracking */}
                  <rect x="115" y="42" width="60" height="8" rx="4" fill="#e2e8f0"/>
                  <rect x="115" y="42" width="45" height="8" rx="4" fill="#10b981"/>
                  <text x="145" y="57" textAnchor="middle" fontSize="6" fill="#10b981">75% Complete</text>

                  {/* Live indicator */}
                  <circle cx="30" cy="25" r="3" fill="#ef4444">
                    <animate attributeName="opacity" values="1;0.3;1" dur="1s" repeatCount="indefinite"/>
                  </circle>
                  <text x="38" y="28" fontSize="5" fill="#ef4444">LIVE</text>
                </svg>
              </div>
            </div>

            {/* Stage 4: Support Dashboard */}
            <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl p-4 border border-orange-200">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-6 h-6 bg-orange-600 text-white rounded-full flex items-center justify-center font-bold text-xs">4</div>
                <span className="text-sm font-semibold text-orange-800">Support & Growth</span>
              </div>
              <div className="bg-white rounded-lg p-3">
                <svg viewBox="0 0 200 80" className="w-full h-16">
                  {/* Support dashboard */}
                  <rect x="10" y="10" width="180" height="60" rx="6" fill="#f8fafc" stroke="#e2e8f0"/>

                  {/* Growth metrics */}
                  <rect x="20" y="20" width="60" height="40" rx="4" fill="#fff7ed"/>
                  <polyline points="25,50 35,45 45,40 55,35 65,30 75,25"
                           stroke="#f59e0b" strokeWidth="2" fill="none"/>
                  <text x="50" y="57" textAnchor="middle" fontSize="6" fill="#f59e0b">Growth +24%</text>

                  {/* Support tickets */}
                  <rect x="90" y="20" width="40" height="40" rx="4" fill="#f0fdf4"/>
                  <circle cx="110" cy="35" r="8" fill="#10b981"/>
                  <text x="110" y="38" textAnchor="middle" fontSize="6" fill="white">24/7</text>
                  <text x="110" y="50" textAnchor="middle" fontSize="6" fill="#10b981">Support</text>

                  {/* Performance indicators */}
                  <rect x="140" y="20" width="40" height="40" rx="4" fill="#f1f5f9"/>
                  <rect x="145" y="25" width="30" height="6" rx="3" fill="#10b981"/>
                  <text x="160" y="35" textAnchor="middle" fontSize="5" fill="#10b981">Efficiency</text>
                  <rect x="145" y="40" width="25" height="6" rx="3" fill="#3b82f6"/>
                  <text x="157" y="50" textAnchor="middle" fontSize="5" fill="#3b82f6">ROI</text>
                  <rect x="145" y="55" width="20" height="4" rx="2" fill="#8b5cf6"/>

                  {/* Success pulse */}
                  <circle cx="170" cy="30" r="2" fill="#10b981" opacity="0.6">
                    <animate attributeName="r" values="2;4;2" dur="2s" repeatCount="indefinite"/>
                    <animate attributeName="opacity" values="0.6;0.2;0.6" dur="2s" repeatCount="indefinite"/>
                  </circle>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Section>
  );
};

export default ImplementationProcessSection;
