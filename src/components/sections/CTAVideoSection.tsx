'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import Image from 'next/image';
import { Section, Button } from '@/components/ui';
import { emailSubscriptionSchema, EmailSubscriptionData } from '@/lib/validations';
import { submitEmailSubscription } from '@/lib/form-service';

const CTAVideoSection: React.FC = () => {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('https://img.youtube.com/vi/mReZr_e70OA/hqdefault.jpg');
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  const [isFormSubmitting, setIsFormSubmitting] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Handle Escape key to close modal
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isVideoModalOpen) {
        setIsVideoModalOpen(false);
      }
    };

    if (isVideoModalOpen) {
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isVideoModalOpen]);

  const handleImageLoad = useCallback(() => {
    console.log('✅ Image loaded successfully:', previewImage);
    setIsImageLoaded(true);
    setImageError(false);
  }, [previewImage]);

  const handleImageError = useCallback((e: React.SyntheticEvent<HTMLImageElement>) => {
    console.error('❌ Failed to load preview image:', e.currentTarget.src);
    setImageError(true);
    setIsImageLoaded(true); // Show fallback content
  }, []);

  // Debug: Log current state
  useEffect(() => {
    console.log('🔍 Current image state:', {
      previewImage,
      isImageLoaded,
      imageError
    });
  }, [previewImage, isImageLoaded, imageError]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<EmailSubscriptionData>({
    resolver: zodResolver(emailSubscriptionSchema),
  });

  const onSubmit = async (data: EmailSubscriptionData) => {
    try {
      setIsFormSubmitting(true);
      const result = await submitEmailSubscription(data);

      if (result.success) {
        setIsSubmitted(true);
        setSubmitMessage(result.message);
        reset();
      } else {
        setSubmitMessage(result.message);
      }
    } catch {
      setSubmitMessage('An unexpected error occurred. Please try again.');
    } finally {
      setIsFormSubmitting(false);
    }
  };

  return (
    <Section
      id="cta-video"
      padding="xl"
      background="primary"
      className="text-white"
    >
      <div className="grid lg:grid-cols-2 gap-12 items-center">
        {/* Video Section */}
        <div className="w-full">
          {/* Video Thumbnail with Play Button */}
          <div
            className="aspect-video bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl overflow-hidden shadow-2xl relative cursor-pointer group hover:shadow-3xl transition-all duration-300"
            onClick={() => setIsVideoModalOpen(true)}
          >
            {/* Video Thumbnail - Simple SVG approach */}
            <div className="relative w-full h-full">
              {/* Static SVG background */}
              <svg
                className="absolute inset-0 w-full h-full"
                viewBox="0 0 800 450"
                xmlns="http://www.w3.org/2000/svg"
              >
                <defs>
                  <linearGradient id="videoBg" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style={{stopColor:'#4F46E5', stopOpacity:1}} />
                    <stop offset="100%" style={{stopColor:'#7C3AED', stopOpacity:1}} />
                  </linearGradient>
                </defs>
                <rect width="800" height="450" fill="url(#videoBg)"/>
                <text x="400" y="200" textAnchor="middle" fill="white" fontSize="32" fontWeight="bold" fontFamily="Arial, sans-serif">
                  Kommo Demo Video
                </text>
                <text x="400" y="240" textAnchor="middle" fill="white" fontSize="18" fontFamily="Arial, sans-serif" opacity="0.8">
                  Discover how Kommo can transform your business
                </text>
                <text x="400" y="280" textAnchor="middle" fill="white" fontSize="14" fontFamily="Arial, sans-serif" opacity="0.6">
                  Click to watch our presentation
                </text>
              </svg>

              {/* Try to load YouTube thumbnail over the SVG */}
              <img
                src={previewImage}
                alt="Kommo Demo Video - Power your sales with Kommo"
                className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-500 ${isImageLoaded ? 'opacity-100' : 'opacity-0'}`}
                onLoad={handleImageLoad}
                onError={handleImageError}
              />

              {/* Play Button Overlay */}
              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-20 group-hover:bg-opacity-30 transition-all duration-300 z-20">
                <div className="w-20 h-20 bg-white bg-opacity-95 rounded-full flex items-center justify-center hover:bg-opacity-100 transition-all duration-200 shadow-xl group-hover:scale-110 border-4 border-white border-opacity-20">
                  <svg className="w-8 h-8 text-gray-800 ml-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content Section */}
        <div className="space-y-8">
          <div className="space-y-6">
            <div className="inline-block">
              <span className="bg-secondary-500 text-white px-3 py-1 rounded-full text-sm font-semibold uppercase tracking-wide">
                SELL MORE WITH kommo
              </span>
            </div>
            
            <h2 className="text-3xl md:text-4xl font-bold leading-tight">
              A dedicated solution for startups and enterprises
            </h2>
            
            <p className="text-lg text-primary-100 leading-relaxed">
              Get an extended Kommo presentation and see how our CRM solution can help your business grow and scale efficiently.
            </p>
          </div>

          {/* Email Form */}
          <div className="bg-primary-700 rounded-xl p-6 space-y-4">
            <div className="flex items-center space-x-2 mb-4">
              <svg className="w-6 h-6 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <h3 className="text-lg font-semibold">Get an extended Kommo presentation!</h3>
            </div>

            {!isSubmitted ? (
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <div>
                  <input
                    type="email"
                    {...register('email')}
                    placeholder="Enter your email address"
                    className={`w-full px-4 py-3 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-secondary-500 ${
                      errors.email ? 'ring-2 ring-red-500' : ''
                    }`}
                  />
                  {errors.email && (
                    <p className="mt-1 text-sm text-red-300">{errors.email.message}</p>
                  )}
                </div>

                {submitMessage && !isSubmitted && (
                  <div className={`p-3 rounded-lg text-sm ${submitMessage.includes('error') || submitMessage.includes('Sorry') ? 'bg-red-500 text-white' : 'bg-green-500 text-white'}`}>
                    {submitMessage}
                  </div>
                )}

                <Button
                  type="submit"
                  variant="secondary"
                  size="lg"
                  disabled={isFormSubmitting}
                  className="w-full"
                >
                  {isFormSubmitting ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Subscribing...</span>
                    </div>
                  ) : (
                    'Get Started'
                  )}
                </Button>
              </form>
            ) : (
              <div className="text-center space-y-3">
                <div className="flex items-center justify-center space-x-2 text-green-400">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="font-semibold">Successful!</span>
                </div>
                <p className="text-primary-200 text-sm">
                  {submitMessage}
                </p>
              </div>
            )}
          </div>

          {/* Additional CTA */}
          <div className="pt-4">
            <Button
              href="/contact"
              variant="outline"
              size="lg"
              className="border-white text-white hover:bg-white hover:text-primary-800"
            >
              Talk to an expert
            </Button>
          </div>
        </div>
      </div>

      {/* Video Modal */}
      {isVideoModalOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
          onClick={() => setIsVideoModalOpen(false)}
        >
          <div
            className="relative w-full max-w-4xl aspect-video"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close Button */}
            <button
              onClick={() => setIsVideoModalOpen(false)}
              className="absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors duration-200 z-10"
            >
              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            {/* YouTube Video */}
            <div className="w-full h-full bg-black rounded-lg overflow-hidden">
              <iframe
                src="https://www.youtube.com/embed/mReZr_e70OA?autoplay=1&rel=0&modestbranding=1"
                title="Kommo Demo - See how Kommo can transform your business"
                className="w-full h-full"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                allowFullScreen
                frameBorder="0"
              />
            </div>
          </div>
        </div>
      )}
    </Section>
  );
};

export default CTAVideoSection;
