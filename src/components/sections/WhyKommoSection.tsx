import React from 'react';
import { Section, SectionHeader, Card } from '@/components/ui';
import { kommoBenefits } from '@/data/content';

const WhyKommoSection: React.FC = () => {
  return (
    <Section
      id="why-kommo"
      padding="xl"
      background="gray"
    >
      <SectionHeader
        subtitle="why kOmmo?"
        title="What benefits do you get from using Kommo"
        align="center"
      />

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {kommoBenefits.map((benefit) => (
          <Card
            key={benefit.id}
            variant="elevated"
            padding="lg"
            hover
            className="relative"
          >
            <div className="space-y-4">
              {/* Number badge */}
              <div className="absolute -top-3 -left-3 w-12 h-12 bg-primary-600 text-white rounded-full flex items-center justify-center font-bold text-lg shadow-lg">
                {benefit.number}
              </div>

              {/* Content */}
              <div className="pt-4 space-y-3">
                <h3 className="text-lg font-bold text-gray-900 leading-tight">
                  {benefit.title}
                </h3>
                <p className="text-gray-600 leading-relaxed text-sm">
                  {benefit.description}
                </p>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Call to action */}
      <div className="mt-16 text-center">
        <div className="bg-white rounded-2xl p-8 md:p-12 shadow-lg max-w-4xl mx-auto">
          <div className="space-y-6">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900">
              Need help with Kommo implementation?
            </h3>
            <p className="text-lg text-gray-600">
              Our certified experts are ready to help you get the most out of your Kommo CRM system.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/contact"
                className="inline-flex items-center justify-center font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed bg-secondary-500 text-white hover:bg-secondary-600 focus:ring-secondary-500 px-8 py-4 text-lg rounded-xl"
              >
                Talk to an expert
              </a>
            </div>
          </div>
        </div>
      </div>
    </Section>
  );
};

export default WhyKommoSection;
