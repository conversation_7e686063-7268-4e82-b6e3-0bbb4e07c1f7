import React from 'react';
import Image from 'next/image';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from '@/components/ui';

const AuditSection: React.FC = () => {
  return (
    <Section
      id="audit"
      padding="xl"
      background="gray"
    >
      <div className="grid lg:grid-cols-2 gap-12 items-center">
        {/* Visual */}
        <div className="relative order-2 lg:order-1">
          <div className="relative">
            <Image
              src="/images/setmee-bg-1.png"
              alt="Kommo audit process"
              width={500}
              height={400}
              className="w-full h-auto rounded-2xl shadow-lg"
            />
            
            {/* Overlay card */}
            <div className="absolute -bottom-6 -right-6 bg-white rounded-xl shadow-xl p-6 max-w-xs">
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium text-gray-700">Audit Report Ready</span>
                </div>
                <div className="text-xs text-gray-500">
                  ✓ Funnel settings analyzed<br/>
                  ✓ Technical errors identified<br/>
                  ✓ Analytics optimized
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="space-y-8 order-1 lg:order-2">
          <SectionHeader
            title="You are a Kommo user already, but not sure if you get the most out of your system?"
            align="left"
            className="mb-8"
          />

          <div className="space-y-6">
            <Card variant="flat" padding="lg" className="border-l-4 border-l-primary-500">
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-gray-900">
                  Audit your system
                </h3>
                <p className="text-gray-600">
                  During the audit, our experts will check your system against an impressive checklist of settings:
                </p>
                <ul className="text-sm text-gray-600 space-y-1 ml-4">
                  <li>• funnel settings</li>
                  <li>• overdue transactions and tasks</li>
                  <li>• logical and technical errors</li>
                  <li>• analytics failures and so on</li>
                </ul>
              </div>
            </Card>

            <Card variant="flat" padding="lg" className="border-l-4 border-l-secondary-500">
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-gray-900">
                  Report with audit results
                </h3>
                <p className="text-gray-600">
                  With a bit of help, your Insights section will give you all the data you need.
                </p>
              </div>
            </Card>

            <Card variant="flat" padding="lg" className="border-l-4 border-l-green-500">
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-gray-900">
                  Realization
                </h3>
                <p className="text-gray-600">
                  Our experts will set up the system so that it works with maximum efficiency and will conduct training sessions on working in the system.
                </p>
              </div>
            </Card>
          </div>

          <div className="pt-6">
            <Button
              href="/contact"
              variant="primary"
              size="lg"
            >
              Contact a specialist
            </Button>
          </div>
        </div>
      </div>
    </Section>
  );
};

export default AuditSection;
