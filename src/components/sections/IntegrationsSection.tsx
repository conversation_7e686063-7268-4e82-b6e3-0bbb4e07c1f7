import React from 'react';
import { Section, SectionHeader } from '@/components/ui';

const IntegrationsSection: React.FC = () => {
  // Popular integrations with placeholder logos
  const integrations = [
    { name: 'WhatsApp', color: 'bg-green-500' },
    { name: 'Telegram', color: 'bg-blue-500' },
    { name: 'Facebook', color: 'bg-blue-600' },
    { name: 'Instagram', color: 'bg-pink-500' },
    { name: 'Gmail', color: 'bg-red-500' },
    { name: 'Slack', color: 'bg-purple-500' },
    { name: 'Zoom', color: 'bg-blue-400' },
    { name: 'Mailchimp', color: 'bg-yellow-500' },
    { name: 'Stripe', color: 'bg-indigo-500' },
    { name: 'Zapier', color: 'bg-orange-500' },
    { name: 'Calendly', color: 'bg-blue-500' },
    { name: 'Typeform', color: 'bg-gray-700' },
    { name: 'Google Analytics', color: 'bg-orange-400' },
    { name: 'Twi<PERSON>', color: 'bg-red-600' },
    { name: 'Dropbox', color: 'bg-blue-500' },
    { name: 'Intercom', color: 'bg-blue-600' },
    { name: 'Zendesk', color: 'bg-green-600' },
    { name: 'ActiveCampaign', color: 'bg-blue-700' },
    { name: 'Facebook Ads', color: 'bg-blue-600' },
    { name: 'Google Calendar', color: 'bg-blue-500' },
    { name: 'Viber', color: 'bg-purple-600' },
    { name: 'WeChat', color: 'bg-green-500' },
    { name: 'RingCentral', color: 'bg-orange-500' },
    { name: 'Formstack', color: 'bg-orange-600' },
  ];

  return (
    <Section
      id="integrations"
      padding="xl"
      background="white"
    >
      <SectionHeader
        subtitle="integrations"
        title="100+ integrations with various services available on Kommo Marketplace"
        align="center"
      />

      {/* Integration grid */}
      <div className="grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-4 mb-12">
        {integrations.map((integration) => (
          <div
            key={integration.name}
            className="group relative bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors duration-200 cursor-pointer"
            style={{
              pointerEvents: 'auto !important',
              position: 'relative',
              zIndex: 10,
              cursor: 'pointer'
            }}
            onClick={() => console.log(`Clicked on ${integration.name} integration`)}
          >
            {/* Logo placeholder */}
            <div className={`w-12 h-12 ${integration.color} rounded-lg flex items-center justify-center mx-auto mb-2 group-hover:scale-110 transition-transform duration-200`}>
              <span className="text-white font-bold text-xs">
                {integration.name.charAt(0)}
              </span>
            </div>
            
            {/* Name */}
            <p className="text-xs text-gray-600 text-center font-medium truncate">
              {integration.name}
            </p>

            {/* Hover tooltip */}
            <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
              {integration.name}
            </div>
          </div>
        ))}
      </div>

      {/* Additional info */}
      <div className="text-center space-y-6">
        <div className="bg-gradient-to-r from-primary-50 to-secondary-50 rounded-2xl p-8">
          <div className="max-w-3xl mx-auto space-y-4">
            <h3 className="text-2xl font-bold text-gray-900">
              Can't find the integration you need?
            </h3>
            <p className="text-gray-600 leading-relaxed">
              We create custom integrations tailored to your specific business needs. 
              Our team has experience with hundreds of different services and APIs.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center pt-4">
              <a
                href="/contact"
                className="inline-flex items-center justify-center font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 px-6 py-3 text-base rounded-lg"
                style={{
                  pointerEvents: 'auto !important',
                  position: 'relative',
                  zIndex: 20,
                  cursor: 'pointer'
                }}
              >
                Request Custom Integration
              </a>
              <a
                href="#"
                className="inline-flex items-center justify-center font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white focus:ring-primary-500 px-6 py-3 text-base rounded-lg"
                style={{
                  pointerEvents: 'auto !important',
                  position: 'relative',
                  zIndex: 20,
                  cursor: 'pointer'
                }}
              >
                View All Integrations
              </a>
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 pt-8">
          <div className="text-center space-y-2">
            <div className="text-3xl font-bold text-primary-600">100+</div>
            <div className="text-gray-600">Ready Integrations</div>
          </div>
          <div className="text-center space-y-2">
            <div className="text-3xl font-bold text-secondary-500">30+</div>
            <div className="text-gray-600">Custom Developed</div>
          </div>
          <div className="text-center space-y-2">
            <div className="text-3xl font-bold text-green-500">24/7</div>
            <div className="text-gray-600">Integration Support</div>
          </div>
        </div>
      </div>
    </Section>
  );
};

export default IntegrationsSection;
