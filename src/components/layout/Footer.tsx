import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Container } from '@/components/ui';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-white border-t border-gray-200">
      <Container>
        <div className="py-8 md:py-12">
          <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
            {/* Logo */}
            <Link href="/" className="flex items-center">
              <Image
                src="/images/setmee-logo.svg"
                alt="Setmee Logo"
                width={120}
                height={40}
                className="h-8 w-auto"
              />
            </Link>

            {/* Kommo Partner Logo - Center */}
            <div className="flex items-center justify-center">
              <Image
                src="/images/kommo-partner-logo-dark.svg"
                alt="Certified Kommo Partner"
                width={140}
                height={50}
                className="object-contain"
              />
            </div>

            {/* Copyright */}
            <div className="text-center md:text-right">
              <p className="text-gray-600 text-sm">
                Setmee © {currentYear} | All Rights Reserved
              </p>
            </div>
          </div>
        </div>
      </Container>
    </footer>
  );
};

export default Footer;
