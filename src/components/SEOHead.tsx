'use client';

import Head from 'next/head';
import { SEOData } from '@/types/cms';

interface SEOHeadProps {
  seoData: SEOData;
  title?: string;
  description?: string;
  url?: string;
  siteName?: string;
  defaultImage?: string;
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  tags?: string[];
}

const SEOHead: React.FC<SEOHeadProps> = ({
  seoData,
  title,
  description,
  url,
  siteName = 'SetMee',
  defaultImage,
  publishedTime,
  modifiedTime,
  author,
  tags = []
}) => {
  // Определяем финальные значения с fallback
  const finalTitle = seoData.metaTitle || title || 'SetMee';
  const finalDescription = seoData.metaDescription || description || '';
  const finalUrl = url || (typeof window !== 'undefined' ? window.location.href : '');
  
  // Open Graph данные
  const ogTitle = seoData.ogTitle || finalTitle;
  const ogDescription = seoData.ogDescription || finalDescription;
  const ogImage = seoData.ogImage || defaultImage;
  const ogType = seoData.ogType || 'website';
  
  // Twitter Card данные
  const twitterTitle = seoData.twitterTitle || finalTitle;
  const twitterDescription = seoData.twitterDescription || finalDescription;
  const twitterImage = seoData.twitterImage || ogImage;
  const twitterCard = seoData.twitterCard || 'summary_large_image';
  
  // Canonical URL
  const canonicalUrl = seoData.canonicalUrl || finalUrl;
  
  // Robots meta
  const robotsContent = [];
  if (seoData.noIndex) robotsContent.push('noindex');
  if (seoData.noFollow) robotsContent.push('nofollow');
  if (seoData.noArchive) robotsContent.push('noarchive');
  if (seoData.noSnippet) robotsContent.push('nosnippet');
  
  const robotsMeta = robotsContent.length > 0 ? robotsContent.join(', ') : 'index, follow';
  
  // Структурированные данные
  const generateStructuredData = () => {
    const baseData = {
      '@context': 'https://schema.org',
      '@type': seoData.schemaType || 'WebPage',
      name: finalTitle,
      description: finalDescription,
      url: finalUrl
    };
    
    if (seoData.schemaType === 'Article' || seoData.schemaType === 'BlogPosting') {
      return {
        ...baseData,
        headline: finalTitle,
        author: author ? {
          '@type': 'Person',
          name: author
        } : undefined,
        publisher: {
          '@type': 'Organization',
          name: siteName
        },
        datePublished: publishedTime,
        dateModified: modifiedTime || publishedTime,
        image: ogImage ? [ogImage] : undefined,
        keywords: seoData.keywords?.join(', '),
        articleSection: tags.length > 0 ? tags[0] : undefined
      };
    }
    
    return baseData;
  };
  
  const structuredData = generateStructuredData();

  return (
    <Head>
      {/* Основные мета-теги */}
      <title>{finalTitle}</title>
      <meta name="description" content={finalDescription} />
      
      {/* Keywords */}
      {seoData.keywords && seoData.keywords.length > 0 && (
        <meta name="keywords" content={seoData.keywords.join(', ')} />
      )}
      
      {/* Robots */}
      <meta name="robots" content={robotsMeta} />
      
      {/* Canonical URL */}
      {canonicalUrl && <link rel="canonical" href={canonicalUrl} />}
      
      {/* Open Graph */}
      <meta property="og:title" content={ogTitle} />
      <meta property="og:description" content={ogDescription} />
      <meta property="og:type" content={ogType} />
      <meta property="og:url" content={finalUrl} />
      <meta property="og:site_name" content={siteName} />
      {ogImage && <meta property="og:image" content={ogImage} />}
      {ogImage && <meta property="og:image:alt" content={finalTitle} />}
      
      {/* Open Graph для статей */}
      {(seoData.schemaType === 'Article' || seoData.schemaType === 'BlogPosting') && (
        <>
          {publishedTime && <meta property="article:published_time" content={publishedTime} />}
          {modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}
          {author && <meta property="article:author" content={author} />}
          {tags.map((tag, index) => (
            <meta key={index} property="article:tag" content={tag} />
          ))}
        </>
      )}
      
      {/* Twitter Card */}
      <meta name="twitter:card" content={twitterCard} />
      <meta name="twitter:title" content={twitterTitle} />
      <meta name="twitter:description" content={twitterDescription} />
      {twitterImage && <meta name="twitter:image" content={twitterImage} />}
      
      {/* Дополнительные мета-теги */}
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
      
      {/* Структурированные данные */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData, null, 2)
        }}
      />
    </Head>
  );
};

export default SEOHead;
