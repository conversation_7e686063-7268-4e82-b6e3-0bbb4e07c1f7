'use client';

import React, { useState, useRef, useEffect } from 'react';

interface SimpleMarkdownEditorProps {
  value: string;
  onChange: (value: string) => void;
  onImageInsert?: () => void;
  placeholder?: string;
  className?: string;
}

const SimpleMarkdownEditor: React.FC<SimpleMarkdownEditorProps> = ({
  value,
  onChange,
  onImageInsert,
  placeholder = "Начните писать...",
  className = ""
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [isPreview, setIsPreview] = useState(false);

  // Функция для вставки изображения в позицию курсора
  const insertImageAtCursor = (imageUrl: string) => {
    if (textareaRef.current) {
      const textarea = textareaRef.current;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      
      const imageMarkdown = `![Изображение](${imageUrl})`;
      const beforeCursor = value.substring(0, start);
      const afterCursor = value.substring(end);
      
      // Добавляем переносы строк для красивого форматирования
      const needsNewlineBefore = start > 0 && !beforeCursor.endsWith('\n');
      const needsNewlineAfter = afterCursor.length > 0 && !afterCursor.startsWith('\n');
      
      const newContent = beforeCursor + 
        (needsNewlineBefore ? '\n' : '') + 
        imageMarkdown + 
        (needsNewlineAfter ? '\n' : '') + 
        afterCursor;
      
      onChange(newContent);
      
      // Устанавливаем курсор после вставленного изображения
      const newCursorPosition = start + (needsNewlineBefore ? 1 : 0) + imageMarkdown.length;
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(newCursorPosition, newCursorPosition);
      }, 0);
    }
  };

  // Глобальная функция для вставки изображений
  useEffect(() => {
    window.insertImageIntoEditor = insertImageAtCursor;
    return () => {
      delete window.insertImageIntoEditor;
    };
  }, [value]);

  // Функция для конвертации markdown в HTML для превью
  const markdownToHtml = (markdown: string): string => {
    return markdown
      .replace(/^### (.*$)/gim, '<h3 class="text-xl font-semibold text-gray-900 mb-3 mt-6">$1</h3>')
      .replace(/^## (.*$)/gim, '<h2 class="text-2xl font-semibold text-gray-900 mb-4 mt-8">$1</h2>')
      .replace(/^# (.*$)/gim, '<h1 class="text-3xl font-bold text-gray-900 mb-6">$1</h1>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<div class="my-6"><img src="$2" alt="$1" class="w-full max-w-2xl mx-auto rounded-lg shadow-md" /></div>')
      .replace(/\n\n/g, '</p><p class="text-gray-700 mb-4 leading-relaxed">')
      .replace(/^(.+)$/gm, '<p class="text-gray-700 mb-4 leading-relaxed">$1</p>')
      .replace(/<p class="text-gray-700 mb-4 leading-relaxed"><\/p>/g, '')
      .replace(/<p class="text-gray-700 mb-4 leading-relaxed">(<h[1-6][^>]*>.*<\/h[1-6]>)<\/p>/g, '$1')
      .replace(/<p class="text-gray-700 mb-4 leading-relaxed">(<div[^>]*>.*<\/div>)<\/p>/g, '$1');
  };

  const toolbarButtons = [
    {
      icon: <strong>H1</strong>,
      title: 'Заголовок 1',
      action: () => insertText('# ')
    },
    {
      icon: <strong>H2</strong>,
      title: 'Заголовок 2',
      action: () => insertText('## ')
    },
    {
      icon: <strong>H3</strong>,
      title: 'Заголовок 3',
      action: () => insertText('### ')
    },
    { type: 'separator' },
    {
      icon: <strong>B</strong>,
      title: 'Жирный',
      action: () => wrapText('**', '**')
    },
    {
      icon: <em>I</em>,
      title: 'Курсив',
      action: () => wrapText('*', '*')
    },
    { type: 'separator' },
    {
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      ),
      title: 'Изображение',
      action: onImageInsert || (() => {})
    },
    { type: 'separator' },
    {
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        </svg>
      ),
      title: isPreview ? 'Редактировать' : 'Превью',
      action: () => setIsPreview(!isPreview)
    }
  ];

  const insertText = (text: string) => {
    if (textareaRef.current) {
      const textarea = textareaRef.current;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      
      const beforeCursor = value.substring(0, start);
      const afterCursor = value.substring(end);
      
      const newContent = beforeCursor + text + afterCursor;
      onChange(newContent);
      
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(start + text.length, start + text.length);
      }, 0);
    }
  };

  const wrapText = (before: string, after: string) => {
    if (textareaRef.current) {
      const textarea = textareaRef.current;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      
      const selectedText = value.substring(start, end);
      const beforeCursor = value.substring(0, start);
      const afterCursor = value.substring(end);
      
      const newContent = beforeCursor + before + selectedText + after + afterCursor;
      onChange(newContent);
      
      setTimeout(() => {
        textarea.focus();
        if (selectedText) {
          textarea.setSelectionRange(start + before.length, end + before.length);
        } else {
          textarea.setSelectionRange(start + before.length, start + before.length);
        }
      }, 0);
    }
  };

  return (
    <div className={`border border-gray-300 rounded-lg overflow-hidden ${className}`}>
      {/* Toolbar */}
      <div className="bg-gray-50 border-b border-gray-300 p-2">
        <div className="flex flex-wrap items-center gap-1">
          {toolbarButtons.map((button, index) => {
            if (button.type === 'separator') {
              return <div key={index} className="w-px h-6 bg-gray-300 mx-1" />;
            }
            
            return (
              <button
                key={index}
                type="button"
                onClick={button.action}
                title={button.title}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
              >
                {button.icon}
              </button>
            );
          })}
        </div>
      </div>

      {/* Editor/Preview */}
      {isPreview ? (
        <div 
          className="min-h-[400px] p-4 prose prose-lg max-w-none bg-white"
          dangerouslySetInnerHTML={{ __html: markdownToHtml(value) }}
        />
      ) : (
        <textarea
          ref={textareaRef}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          className="w-full min-h-[400px] p-4 border-none resize-none focus:outline-none font-mono text-sm leading-relaxed"
          style={{ fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace' }}
        />
      )}
    </div>
  );
};

export default SimpleMarkdownEditor;
