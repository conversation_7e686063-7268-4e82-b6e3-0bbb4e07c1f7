'use client';

import React, { useState, useRef, useEffect } from 'react';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  onImageInsert?: () => void;
  placeholder?: string;
  className?: string;
}

// Global function to insert image into active editor
declare global {
  interface Window {
    insertImageIntoEditor?: (imageUrl: string) => void;
  }
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  onImageInsert,
  placeholder = "Начните писать...",
  className = ""
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [isToolbarSticky, setIsToolbarSticky] = useState(false);

  useEffect(() => {
    // Convert markdown to HTML for display
    if (editorRef.current && value !== editorRef.current.innerHTML) {
      editorRef.current.innerHTML = markdownToHtml(value);
    }
  }, [value]);

  // Простая функция для вставки изображения
  const insertImageIntoMarkdown = (imageUrl: string) => {
    const imageMarkdown = `![Изображение](${imageUrl})`;

    // Добавляем изображение в конец контента с правильным форматированием
    const currentContent = value.trim();
    const newContent = currentContent +
      (currentContent ? '\n\n' : '') +
      imageMarkdown + '\n\n';

    onChange(newContent);

    // Фокусируемся на редакторе после вставки
    setTimeout(() => {
      if (editorRef.current) {
        editorRef.current.focus();
        // Устанавливаем курсор в конец
        const selection = window.getSelection();
        if (selection) {
          selection.selectAllChildren(editorRef.current);
          selection.collapseToEnd();
        }
      }
    }, 100);
  };

  useEffect(() => {
    // Set up global function for image insertion
    window.insertImageIntoEditor = insertImageIntoMarkdown;

    return () => {
      delete window.insertImageIntoEditor;
    };
  }, [value, onChange]);

  useEffect(() => {
    const handleScroll = () => {
      if (editorRef.current) {
        const rect = editorRef.current.getBoundingClientRect();
        setIsToolbarSticky(rect.top <= 100);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const markdownToHtml = (markdown: string): string => {
    return markdown
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1" style="max-width: 100%; height: auto; margin: 10px 0;" />')
      .replace(/\n\n/g, '</p><p>')
      .replace(/^(.+)$/gm, '<p>$1</p>')
      .replace(/<p><\/p>/g, '')
      .replace(/<p>(<h[1-6]>.*<\/h[1-6]>)<\/p>/g, '$1')
      .replace(/<p>(<img[^>]*>)<\/p>/g, '<div style="text-align: center; margin: 20px 0;">$1</div>');
  };

  const htmlToMarkdown = (html: string): string => {
    return html
      .replace(/<h1>(.*?)<\/h1>/g, '# $1\n\n')
      .replace(/<h2>(.*?)<\/h2>/g, '## $1\n\n')
      .replace(/<h3>(.*?)<\/h3>/g, '### $1\n\n')
      .replace(/<strong>(.*?)<\/strong>/g, '**$1**')
      .replace(/<em>(.*?)<\/em>/g, '*$1*')
      .replace(/<div[^>]*><img src="([^"]*)" alt="([^"]*)"[^>]*><\/div>/g, '![${2}](${1})\n\n')
      .replace(/<img src="([^"]*)" alt="([^"]*)"[^>]*>/g, '![${2}](${1})\n\n')
      .replace(/<p>(.*?)<\/p>/g, '$1\n\n')
      .replace(/<br\s*\/?>/g, '\n')
      .replace(/&nbsp;/g, ' ')
      .replace(/\n{3,}/g, '\n\n')
      .trim();
  };

  const handleInput = () => {
    if (editorRef.current) {
      const html = editorRef.current.innerHTML;
      const markdown = htmlToMarkdown(html);
      onChange(markdown);
    }
  };

  const execCommand = (command: string, value?: string) => {
    // eslint-disable-next-line deprecation/deprecation
    document.execCommand(command, false, value);
    handleInput();
    editorRef.current?.focus();
  };

  const insertHeading = (level: number) => {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const selectedText = range.toString() || 'Заголовок';
      
      const heading = document.createElement(`h${level}`);
      heading.textContent = selectedText;
      
      range.deleteContents();
      range.insertNode(heading);
      
      // Move cursor after heading
      range.setStartAfter(heading);
      range.setEndAfter(heading);
      selection.removeAllRanges();
      selection.addRange(range);
      
      handleInput();
    }
  };

  const insertImage = () => {
    if (onImageInsert) {
      onImageInsert();
    }
  };

  const insertQuote = () => {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const selectedText = range.toString() || 'Цитата';

      const blockquote = document.createElement('blockquote');
      blockquote.style.borderLeft = '4px solid #e5e7eb';
      blockquote.style.paddingLeft = '1rem';
      blockquote.style.margin = '1rem 0';
      blockquote.style.fontStyle = 'italic';
      blockquote.style.color = '#6b7280';
      blockquote.textContent = selectedText;

      range.deleteContents();
      range.insertNode(blockquote);

      range.setStartAfter(blockquote);
      range.setEndAfter(blockquote);
      selection.removeAllRanges();
      selection.addRange(range);

      handleInput();
    }
  };

  const insertCode = () => {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const selectedText = range.toString() || 'код';

      const code = document.createElement('code');
      code.style.backgroundColor = '#f3f4f6';
      code.style.padding = '0.25rem 0.5rem';
      code.style.borderRadius = '0.25rem';
      code.style.fontFamily = 'monospace';
      code.style.fontSize = '0.875rem';
      code.textContent = selectedText;

      range.deleteContents();
      range.insertNode(code);

      range.setStartAfter(code);
      range.setEndAfter(code);
      selection.removeAllRanges();
      selection.addRange(range);

      handleInput();
    }
  };

  const insertLink = () => {
    const url = prompt('Введите URL ссылки:');
    if (url) {
      execCommand('createLink', url);
    }
  };

  const toolbarButtons = [
    {
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 2l3 6H5l3 6" />
        </svg>
      ),
      title: 'Заголовок 1',
      action: () => insertHeading(1)
    },
    {
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 4l3 4H5l3 4" />
        </svg>
      ),
      title: 'Заголовок 2',
      action: () => insertHeading(2)
    },
    {
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 6l3 2H5l3 2" />
        </svg>
      ),
      title: 'Заголовок 3',
      action: () => insertHeading(3)
    },
    { type: 'separator' },
    {
      icon: <strong>B</strong>,
      title: 'Жирный',
      action: () => execCommand('bold')
    },
    {
      icon: <em>I</em>,
      title: 'Курсив',
      action: () => execCommand('italic')
    },
    {
      icon: <u>U</u>,
      title: 'Подчеркнутый',
      action: () => execCommand('underline')
    },
    { type: 'separator' },
    {
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h8m-8 6h16" />
        </svg>
      ),
      title: 'По левому краю',
      action: () => execCommand('justifyLeft')
    },
    {
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      ),
      title: 'По центру',
      action: () => execCommand('justifyCenter')
    },
    {
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h12" />
        </svg>
      ),
      title: 'По правому краю',
      action: () => execCommand('justifyRight')
    },
    { type: 'separator' },
    {
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
        </svg>
      ),
      title: 'Список',
      action: () => execCommand('insertUnorderedList')
    },
    {
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
        </svg>
      ),
      title: 'Нумерованный список',
      action: () => execCommand('insertOrderedList')
    },
    { type: 'separator' },
    {
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v6a2 2 0 01-2 2h-2l-4 4z" />
        </svg>
      ),
      title: 'Цитата',
      action: insertQuote
    },
    {
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
        </svg>
      ),
      title: 'Код',
      action: insertCode
    },
    { type: 'separator' },
    {
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
        </svg>
      ),
      title: 'Ссылка',
      action: insertLink
    },
    {
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      ),
      title: 'Изображение',
      action: insertImage
    },
    { type: 'separator' },
    {
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16l-4-4m0 0l4-4m-4 4h18" />
        </svg>
      ),
      title: 'Отменить',
      action: () => execCommand('undo')
    },
    {
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
        </svg>
      ),
      title: 'Повторить',
      action: () => execCommand('redo')
    }
  ];

  return (
    <div className={`border border-gray-300 rounded-lg overflow-hidden ${className}`}>
      {/* Toolbar */}
      <div className={`bg-gray-50 border-b border-gray-300 p-2 ${isToolbarSticky ? 'sticky top-0 z-10 shadow-md' : ''}`}>
        <div className="flex flex-wrap items-center gap-1">
          {toolbarButtons.map((button, index) => {
            if (button.type === 'separator') {
              return <div key={index} className="w-px h-6 bg-gray-300 mx-1" />;
            }
            
            return (
              <button
                key={index}
                type="button"
                onClick={button.action}
                title={button.title}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
              >
                {button.icon}
              </button>
            );
          })}
        </div>
      </div>

      {/* Editor */}
      <div
        ref={editorRef}
        contentEditable
        onInput={handleInput}
        onPaste={() => {
          // Handle paste to maintain formatting
          setTimeout(handleInput, 0);
        }}
        className="min-h-[400px] p-4 focus:outline-none prose prose-lg max-w-none"
        style={{
          lineHeight: '1.6',
        }}
        suppressContentEditableWarning={true}
        data-placeholder={placeholder}
      />

      <style jsx>{`
        [contenteditable]:empty:before {
          content: attr(data-placeholder);
          color: #9ca3af;
          pointer-events: none;
        }
        
        [contenteditable] h1 {
          font-size: 2rem;
          font-weight: bold;
          margin: 1rem 0;
          line-height: 1.2;
        }
        
        [contenteditable] h2 {
          font-size: 1.5rem;
          font-weight: bold;
          margin: 1rem 0;
          line-height: 1.3;
        }
        
        [contenteditable] h3 {
          font-size: 1.25rem;
          font-weight: bold;
          margin: 1rem 0;
          line-height: 1.4;
        }
        
        [contenteditable] p {
          margin: 0.5rem 0;
          line-height: 1.6;
        }
        
        [contenteditable] ul, [contenteditable] ol {
          margin: 1rem 0;
          padding-left: 2rem;
        }
        
        [contenteditable] li {
          margin: 0.25rem 0;
        }
        
        [contenteditable] img {
          max-width: 100%;
          height: auto;
          border-radius: 0.5rem;
          margin: 1rem 0;
        }
        
        [contenteditable] a {
          color: #3b82f6;
          text-decoration: underline;
        }
        
        [contenteditable] a:hover {
          color: #1d4ed8;
        }

        [contenteditable] blockquote {
          border-left: 4px solid #e5e7eb;
          padding-left: 1rem;
          margin: 1rem 0;
          font-style: italic;
          color: #6b7280;
          background-color: #f9fafb;
          padding: 1rem;
          border-radius: 0.5rem;
        }

        [contenteditable] code {
          background-color: #f3f4f6;
          padding: 0.25rem 0.5rem;
          border-radius: 0.25rem;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 0.875rem;
          color: #1f2937;
        }

        [contenteditable] pre {
          background-color: #1f2937;
          color: #f9fafb;
          padding: 1rem;
          border-radius: 0.5rem;
          overflow-x: auto;
          margin: 1rem 0;
        }

        [contenteditable] pre code {
          background-color: transparent;
          padding: 0;
          color: inherit;
        }
      `}</style>
    </div>
  );
};

export default RichTextEditor;
