'use client';

import React, { useState } from 'react';
import { SEOData } from '@/types/cms';

interface SEOSettingsProps {
  seoData: SEOData;
  onChange: (seoData: SEOData) => void;
  title: string;
  excerpt?: string;
  className?: string;
}

const SEOSettings: React.FC<SEOSettingsProps> = ({
  seoData,
  onChange,
  title,
  excerpt = '',
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState<'general' | 'social' | 'advanced'>('general');

  const handleChange = (field: keyof SEOData, value: string | boolean | string[]) => {
    onChange({
      ...seoData,
      [field]: value
    });
  };

  // Автоматическое заполнение полей на основе заголовка и описания
  const autoFillSEO = () => {
    const updates: Partial<SEOData> = {};
    
    if (!seoData.metaTitle && title) {
      updates.metaTitle = title;
    }
    
    if (!seoData.metaDescription && excerpt) {
      updates.metaDescription = excerpt.substring(0, 160);
    }
    
    if (!seoData.ogTitle && title) {
      updates.ogTitle = title;
    }
    
    if (!seoData.ogDescription && excerpt) {
      updates.ogDescription = excerpt.substring(0, 300);
    }
    
    if (!seoData.twitterTitle && title) {
      updates.twitterTitle = title;
    }
    
    if (!seoData.twitterDescription && excerpt) {
      updates.twitterDescription = excerpt.substring(0, 200);
    }
    
    onChange({
      ...seoData,
      ...updates
    });
  };

  // Анализ SEO (базовый)
  const getSEOScore = () => {
    let score = 0;
    let issues = [];
    
    // Проверка мета-заголовка
    if (seoData.metaTitle) {
      if (seoData.metaTitle.length >= 30 && seoData.metaTitle.length <= 60) {
        score += 20;
      } else {
        issues.push('Мета-заголовок должен быть 30-60 символов');
      }
    } else {
      issues.push('Мета-заголовок не заполнен');
    }
    
    // Проверка мета-описания
    if (seoData.metaDescription) {
      if (seoData.metaDescription.length >= 120 && seoData.metaDescription.length <= 160) {
        score += 20;
      } else {
        issues.push('Мета-описание должно быть 120-160 символов');
      }
    } else {
      issues.push('Мета-описание не заполнено');
    }
    
    // Проверка ключевых слов
    if (seoData.keywords && seoData.keywords.length > 0) {
      score += 15;
    } else {
      issues.push('Ключевые слова не заданы');
    }
    
    // Проверка фокусного ключевого слова
    if (seoData.focusKeyword) {
      score += 15;
      // Проверка вхождения в заголовок
      if (title.toLowerCase().includes(seoData.focusKeyword.toLowerCase())) {
        score += 10;
      } else {
        issues.push('Фокусное ключевое слово не найдено в заголовке');
      }
    } else {
      issues.push('Фокусное ключевое слово не задано');
    }
    
    // Проверка Open Graph
    if (seoData.ogTitle && seoData.ogDescription) {
      score += 10;
    } else {
      issues.push('Open Graph данные не полные');
    }
    
    // Проверка Twitter Card
    if (seoData.twitterTitle && seoData.twitterDescription) {
      score += 10;
    } else {
      issues.push('Twitter Card данные не полные');
    }
    
    return { score, issues };
  };

  const { score, issues } = getSEOScore();
  
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreLabel = (score: number) => {
    if (score >= 80) return 'Отлично';
    if (score >= 60) return 'Хорошо';
    if (score >= 40) return 'Удовлетворительно';
    return 'Требует улучшения';
  };

  const tabs = [
    { id: 'general', label: 'Основные', icon: '🎯' },
    { id: 'social', label: 'Соцсети', icon: '📱' },
    { id: 'advanced', label: 'Дополнительно', icon: '⚙️' }
  ];

  return (
    <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">SEO настройки</h3>
        <button
          type="button"
          onClick={autoFillSEO}
          className="text-sm text-primary-600 hover:text-primary-700 font-medium"
        >
          Автозаполнение
        </button>
      </div>

      {/* SEO Score */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">SEO оценка</span>
          <span className={`text-lg font-bold ${getScoreColor(score)}`}>
            {score}/100 - {getScoreLabel(score)}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className={`h-2 rounded-full transition-all duration-300 ${
              score >= 80 ? 'bg-green-500' : 
              score >= 60 ? 'bg-yellow-500' : 'bg-red-500'
            }`}
            style={{ width: `${score}%` }}
          />
        </div>
        {issues.length > 0 && (
          <div className="mt-3">
            <p className="text-sm font-medium text-gray-700 mb-1">Рекомендации:</p>
            <ul className="text-sm text-gray-600 space-y-1">
              {issues.slice(0, 3).map((issue, index) => (
                <li key={index} className="flex items-start">
                  <span className="text-yellow-500 mr-1">•</span>
                  {issue}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              type="button"
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-4">
        {activeTab === 'general' && (
          <>
            {/* Focus Keyword */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Фокусное ключевое слово
              </label>
              <input
                type="text"
                value={seoData.focusKeyword || ''}
                onChange={(e) => handleChange('focusKeyword', e.target.value)}
                className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                placeholder="основное ключевое слово"
              />
              <p className="mt-1 text-sm text-gray-500">
                Основное ключевое слово для этой страницы
              </p>
            </div>

            {/* Meta Title */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Мета-заголовок ({seoData.metaTitle?.length || 0}/60)
              </label>
              <input
                type="text"
                value={seoData.metaTitle}
                onChange={(e) => handleChange('metaTitle', e.target.value)}
                className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                placeholder="SEO заголовок страницы"
                maxLength={60}
              />
              <p className="mt-1 text-sm text-gray-500">
                Рекомендуется 30-60 символов
              </p>
            </div>

            {/* Meta Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Мета-описание ({seoData.metaDescription?.length || 0}/160)
              </label>
              <textarea
                rows={3}
                value={seoData.metaDescription}
                onChange={(e) => handleChange('metaDescription', e.target.value)}
                className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                placeholder="Краткое описание страницы для поисковых систем"
                maxLength={160}
              />
              <p className="mt-1 text-sm text-gray-500">
                Рекомендуется 120-160 символов
              </p>
            </div>

            {/* Keywords */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Ключевые слова
              </label>
              <input
                type="text"
                value={seoData.keywords?.join(', ') || ''}
                onChange={(e) => handleChange('keywords', e.target.value.split(',').map(k => k.trim()).filter(Boolean))}
                className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                placeholder="ключевое слово 1, ключевое слово 2, ключевое слово 3"
              />
              <p className="mt-1 text-sm text-gray-500">
                Разделяйте ключевые слова запятыми
              </p>
            </div>
          </>
        )}

        {activeTab === 'social' && (
          <>
            {/* Open Graph */}
            <div className="border-b border-gray-200 pb-4 mb-4">
              <h4 className="text-md font-medium text-gray-900 mb-3">Open Graph (Facebook, LinkedIn)</h4>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    OG заголовок
                  </label>
                  <input
                    type="text"
                    value={seoData.ogTitle || ''}
                    onChange={(e) => handleChange('ogTitle', e.target.value)}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Заголовок для социальных сетей"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    OG описание
                  </label>
                  <textarea
                    rows={2}
                    value={seoData.ogDescription || ''}
                    onChange={(e) => handleChange('ogDescription', e.target.value)}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Описание для социальных сетей"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Тип контента
                  </label>
                  <select
                    value={seoData.ogType || 'article'}
                    onChange={(e) => handleChange('ogType', e.target.value)}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="website">Веб-сайт</option>
                    <option value="article">Статья</option>
                    <option value="product">Товар</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Twitter Card */}
            <div>
              <h4 className="text-md font-medium text-gray-900 mb-3">Twitter Card</h4>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Twitter заголовок
                  </label>
                  <input
                    type="text"
                    value={seoData.twitterTitle || ''}
                    onChange={(e) => handleChange('twitterTitle', e.target.value)}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Заголовок для Twitter"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Twitter описание
                  </label>
                  <textarea
                    rows={2}
                    value={seoData.twitterDescription || ''}
                    onChange={(e) => handleChange('twitterDescription', e.target.value)}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Описание для Twitter"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Тип карточки
                  </label>
                  <select
                    value={seoData.twitterCard || 'summary_large_image'}
                    onChange={(e) => handleChange('twitterCard', e.target.value)}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="summary">Краткая</option>
                    <option value="summary_large_image">С большим изображением</option>
                  </select>
                </div>
              </div>
            </div>
          </>
        )}

        {activeTab === 'advanced' && (
          <>
            {/* Canonical URL */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Канонический URL
              </label>
              <input
                type="url"
                value={seoData.canonicalUrl || ''}
                onChange={(e) => handleChange('canonicalUrl', e.target.value)}
                className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                placeholder="https://example.com/canonical-url"
              />
              <p className="mt-1 text-sm text-gray-500">
                Оставьте пустым для автоматического определения
              </p>
            </div>

            {/* Schema Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Тип структурированных данных
              </label>
              <select
                value={seoData.schemaType || 'Article'}
                onChange={(e) => handleChange('schemaType', e.target.value)}
                className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="Article">Статья</option>
                <option value="BlogPosting">Блог пост</option>
                <option value="WebPage">Веб-страница</option>
                <option value="Organization">Организация</option>
                <option value="Person">Персона</option>
              </select>
            </div>

            {/* Robot Settings */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Настройки индексации
              </label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={seoData.noIndex || false}
                    onChange={(e) => handleChange('noIndex', e.target.checked)}
                    className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
                  />
                  <span className="ml-2 text-sm text-gray-700">Не индексировать (noindex)</span>
                </label>
                
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={seoData.noFollow || false}
                    onChange={(e) => handleChange('noFollow', e.target.checked)}
                    className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
                  />
                  <span className="ml-2 text-sm text-gray-700">Не следовать ссылкам (nofollow)</span>
                </label>
                
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={seoData.noArchive || false}
                    onChange={(e) => handleChange('noArchive', e.target.checked)}
                    className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
                  />
                  <span className="ml-2 text-sm text-gray-700">Не архивировать (noarchive)</span>
                </label>
                
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={seoData.noSnippet || false}
                    onChange={(e) => handleChange('noSnippet', e.target.checked)}
                    className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
                  />
                  <span className="ml-2 text-sm text-gray-700">Не показывать сниппет (nosnippet)</span>
                </label>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default SEOSettings;
