// Form configuration for different types of forms and their Make.com webhooks

export interface FormConfig {
  webhookUrl?: string;
  successMessage: string;
  errorMessage: string;
  fields: string[];
  type: 'contact' | 'newsletter' | 'demo' | 'audit' | 'consultation';
}

export const FORM_CONFIGS: Record<string, FormConfig> = {
  // Основная контактная форма
  contact: {
    webhookUrl: process.env.NEXT_PUBLIC_MAKE_CONTACT_WEBHOOK,
    successMessage: 'Thank you for your message! We will get back to you within 24 hours.',
    errorMessage: 'Sorry, there was an error sending your message. Please try again.',
    fields: ['name', 'email', 'phone', 'company', 'message'],
    type: 'contact'
  },

  // Подписка на новости/презентацию
  newsletter: {
    webhookUrl: process.env.NEXT_PUBLIC_MAKE_NEWSLETTER_WEBHOOK,
    successMessage: 'Thank you for subscribing! We will send you the presentation shortly.',
    errorMessage: 'Sorry, there was an error with your subscription. Please try again.',
    fields: ['email'],
    type: 'newsletter'
  },

  // Запрос демо
  demo: {
    webhookUrl: process.env.NEXT_PUBLIC_MAKE_DEMO_WEBHOOK,
    successMessage: 'Demo request sent! We will contact you soon to schedule a presentation.',
    errorMessage: 'Sorry, there was an error sending your demo request. Please try again.',
    fields: ['name', 'email', 'company', 'phone'],
    type: 'demo'
  },

  // Запрос аудита
  audit: {
    webhookUrl: process.env.NEXT_PUBLIC_MAKE_AUDIT_WEBHOOK,
    successMessage: 'Audit request received! We will prepare your report and contact you within 2 business days.',
    errorMessage: 'Sorry, there was an error sending your audit request. Please try again.',
    fields: ['name', 'email', 'website', 'company', 'currentCrm'],
    type: 'audit'
  },

  // Консультация эксперта
  consultation: {
    webhookUrl: process.env.NEXT_PUBLIC_MAKE_CONSULTATION_WEBHOOK,
    successMessage: 'Consultation request sent! Our expert will contact you within 24 hours.',
    errorMessage: 'Sorry, there was an error sending your consultation request. Please try again.',
    fields: ['name', 'email', 'phone', 'company', 'challenge'],
    type: 'consultation'
  }
};

// Получить конфигурацию формы по типу
export const getFormConfig = (formType: string): FormConfig => {
  const config = FORM_CONFIGS[formType];
  if (!config) {
    throw new Error(`Form configuration not found for type: ${formType}`);
  }
  return config;
};

// Проверить, настроен ли webhook для формы
export const isWebhookConfigured = (formType: string): boolean => {
  const config = getFormConfig(formType);
  return !!config.webhookUrl;
};
