import { z } from 'zod';

export const contactFormSchema = z.object({
  name: z
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(50, 'Name must be less than 50 characters'),
  email: z
    .string()
    .email('Please enter a valid email address'),
  phone: z
    .string()
    .optional()
    .refine((val) => !val || val.length >= 10, {
      message: 'Phone number must be at least 10 digits',
    }),
  company: z
    .string()
    .optional(),
  message: z
    .string()
    .min(10, 'Message must be at least 10 characters')
    .max(1000, 'Message must be less than 1000 characters'),
});

export const emailSubscriptionSchema = z.object({
  email: z
    .string()
    .email('Please enter a valid email address'),
});

export const demoRequestSchema = z.object({
  name: z
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(50, 'Name must be less than 50 characters'),
  email: z
    .string()
    .email('Please enter a valid email address'),
  company: z
    .string()
    .optional(),
  phone: z
    .string()
    .optional()
    .refine((val) => !val || val.length >= 10, {
      message: 'Phone number must be at least 10 digits',
    }),
});

export const auditRequestSchema = z.object({
  name: z
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(50, 'Name must be less than 50 characters'),
  email: z
    .string()
    .email('Please enter a valid email address'),
  website: z
    .string()
    .url('Please enter a valid website URL')
    .optional()
    .or(z.literal('')),
  company: z
    .string()
    .optional(),
  currentCrm: z
    .string()
    .optional(),
});

export const consultationRequestSchema = z.object({
  name: z
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(50, 'Name must be less than 50 characters'),
  email: z
    .string()
    .email('Please enter a valid email address'),
  phone: z
    .string()
    .optional()
    .refine((val) => !val || val.length >= 10, {
      message: 'Phone number must be at least 10 digits',
    }),
  company: z
    .string()
    .optional(),
  challenge: z
    .string()
    .min(10, 'Please describe your challenge in at least 10 characters')
    .max(500, 'Challenge description must be less than 500 characters')
    .optional(),
});

// Type exports
export type ContactFormData = z.infer<typeof contactFormSchema>;
export type EmailSubscriptionData = z.infer<typeof emailSubscriptionSchema>;
export type DemoRequestData = z.infer<typeof demoRequestSchema>;
export type AuditRequestData = z.infer<typeof auditRequestSchema>;
export type ConsultationRequestData = z.infer<typeof consultationRequestSchema>;

// Union type for all form data
export type FormData = ContactFormData | EmailSubscriptionData | DemoRequestData | AuditRequestData | ConsultationRequestData;

// Webhook payload type
export interface WebhookPayload {
  formType: string;
  timestamp: string;
  source: string;
  userAgent?: string;
  referrer?: string;
  [key: string]: unknown;
}
