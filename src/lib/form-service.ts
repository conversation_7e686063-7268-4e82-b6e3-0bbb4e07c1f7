import {
  ContactFormData,
  EmailSubscriptionData,
  DemoRequestData,
  AuditRequestData,
  ConsultationRequestData,
  WebhookPayload
} from './validations';
import { getFormConfig, isWebhookConfigured } from '@/config/forms';

// Universal form submission service with Make.com webhook integration
// Supports multiple form types with different webhook endpoints

// Generic webhook sender
const sendToMakeWebhook = async (webhookUrl: string, data: WebhookPayload): Promise<Response> => {
  const response = await fetch(webhookUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error(`Webhook failed: ${response.status} ${response.statusText}`);
  }

  return response;
};

// Universal form submission function
export const submitForm = async (
  formType: string,
  data: Record<string, unknown>
): Promise<{ success: boolean; message: string }> => {
  try {
    const config = getFormConfig(formType);

    if (config.webhookUrl && isWebhookConfigured(formType)) {
      // Send to Make.com webhook
      await sendToMakeWebhook(config.webhookUrl, {
        formType,
        timestamp: new Date().toISOString(),
        source: 'setmee-website',
        userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server',
        referrer: typeof window !== 'undefined' ? window.document.referrer : '',
        ...data,
      });

      console.log(`${formType} form sent to Make webhook:`, data.email || data.name);
    } else {
      // Fallback: log to console if no webhook configured
      console.log(`${formType} form submitted (no webhook configured):`, data);
    }

    return {
      success: true,
      message: config.successMessage,
    };
  } catch (error) {
    console.error(`${formType} form submission error:`, error);
    const config = getFormConfig(formType);
    return {
      success: false,
      message: config.errorMessage,
    };
  }
};

// Specific form submission functions (backward compatibility)
export const submitContactForm = async (data: ContactFormData): Promise<{ success: boolean; message: string }> => {
  return submitForm('contact', data);
};

export const submitEmailSubscription = async (data: EmailSubscriptionData): Promise<{ success: boolean; message: string }> => {
  return submitForm('newsletter', data);
};

// Additional form submission functions for different types
export const submitDemoRequest = async (data: DemoRequestData): Promise<{ success: boolean; message: string }> => {
  return submitForm('demo', data);
};

export const submitAuditRequest = async (data: AuditRequestData): Promise<{ success: boolean; message: string }> => {
  return submitForm('audit', data);
};

export const submitConsultationRequest = async (data: ConsultationRequestData): Promise<{ success: boolean; message: string }> => {
  return submitForm('consultation', data);
};

// Example integration with Formspree (commented out)
/*
export const submitContactFormFormspree = async (data: ContactFormData) => {
  const response = await fetch('https://formspree.io/f/YOUR_FORM_ID', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  
  if (!response.ok) {
    throw new Error('Failed to submit form');
  }
  
  return response.json();
};
*/

// Example integration with EmailJS (commented out)
/*
import emailjs from '@emailjs/browser';

export const submitContactFormEmailJS = async (data: ContactFormData) => {
  const result = await emailjs.send(
    'YOUR_SERVICE_ID',
    'YOUR_TEMPLATE_ID',
    {
      from_name: data.name,
      from_email: data.email,
      phone: data.phone,
      company: data.company,
      message: data.message,
    },
    'YOUR_PUBLIC_KEY'
  );
  
  return result;
};
*/
