'use client';

export interface MediaFile {
  id: string;
  name: string;
  url: string;
  size: number;
  type: string;
  uploadedAt: string;
}

// Функция для сжатия изображения
const compressImage = (file: File, maxWidth: number = 1200, quality: number = 0.8): Promise<string> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Вычисляем новые размеры с сохранением пропорций
      let { width, height } = img;
      
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }

      canvas.width = width;
      canvas.height = height;

      // Рисуем сжатое изображение
      ctx?.drawImage(img, 0, 0, width, height);

      // Конвертируем в base64 с сжатием
      const compressedDataUrl = canvas.toDataURL('image/jpeg', quality);
      resolve(compressedDataUrl);
    };

    img.onerror = reject;
    img.src = URL.createObjectURL(file);
  });
};

// Функция для создания миниатюры
const createThumbnail = (file: File): Promise<string> => {
  return compressImage(file, 300, 0.7);
};

// IndexedDB для хранения больших файлов
class MediaDB {
  private dbName = 'MediaStorage';
  private version = 1;
  private db: IDBDatabase | null = null;

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // Создаем хранилище для медиа-файлов
        if (!db.objectStoreNames.contains('media')) {
          const store = db.createObjectStore('media', { keyPath: 'id' });
          store.createIndex('uploadedAt', 'uploadedAt', { unique: false });
        }
      };
    });
  }

  async saveFile(mediaFile: MediaFile): Promise<boolean> {
    if (!this.db) await this.init();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['media'], 'readwrite');
      const store = transaction.objectStore('media');
      const request = store.put(mediaFile);

      request.onsuccess = () => resolve(true);
      request.onerror = () => reject(request.error);
    });
  }

  async getFiles(): Promise<MediaFile[]> {
    if (!this.db) await this.init();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['media'], 'readonly');
      const store = transaction.objectStore('media');
      const request = store.getAll();

      request.onsuccess = () => resolve(request.result || []);
      request.onerror = () => reject(request.error);
    });
  }

  async deleteFile(id: string): Promise<boolean> {
    if (!this.db) await this.init();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['media'], 'readwrite');
      const store = transaction.objectStore('media');
      const request = store.delete(id);

      request.onsuccess = () => resolve(true);
      request.onerror = () => reject(request.error);
    });
  }

  async clearAll(): Promise<boolean> {
    if (!this.db) await this.init();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['media'], 'readwrite');
      const store = transaction.objectStore('media');
      const request = store.clear();

      request.onsuccess = () => resolve(true);
      request.onerror = () => reject(request.error);
    });
  }
}

const mediaDB = new MediaDB();

// Fallback к localStorage для метаданных (без изображений)
const STORAGE_KEY = 'media_metadata';

interface MediaMetadata {
  id: string;
  name: string;
  size: number;
  type: string;
  uploadedAt: string;
}

const saveToLocalStorage = (metadata: MediaMetadata[]): boolean => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(metadata));
    return true;
  } catch (error) {
    console.error('Error saving to localStorage:', error);
    return false;
  }
};

const getFromLocalStorage = (): MediaMetadata[] => {
  try {
    const data = localStorage.getItem(STORAGE_KEY);
    return data ? JSON.parse(data) : [];
  } catch (error) {
    console.error('Error reading from localStorage:', error);
    return [];
  }
};

// Основные функции для работы с медиа
export const uploadMediaFile = async (file: File): Promise<MediaFile | null> => {
  try {
    // Проверяем, что это изображение
    if (!file.type.startsWith('image/')) {
      throw new Error('Поддерживаются только изображения');
    }

    // Создаем ID и метаданные
    const id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
    const uploadedAt = new Date().toISOString();

    // Сжимаем изображение
    const compressedUrl = await compressImage(file);

    const mediaFile: MediaFile = {
      id,
      name: file.name,
      url: compressedUrl,
      size: file.size,
      type: file.type,
      uploadedAt
    };

    // Пытаемся сохранить в IndexedDB
    try {
      await mediaDB.saveFile(mediaFile);
    } catch (error) {
      console.warn('IndexedDB failed, using localStorage fallback:', error);
      
      // Fallback: сохраняем только метаданные в localStorage
      const metadata = getFromLocalStorage();
      metadata.push({
        id,
        name: file.name,
        size: file.size,
        type: file.type,
        uploadedAt
      });
      
      if (!saveToLocalStorage(metadata)) {
        throw new Error('Недостаточно места для хранения');
      }
      
      // Для fallback используем Object URL (временный)
      mediaFile.url = URL.createObjectURL(file);
    }

    return mediaFile;
  } catch (error) {
    console.error('Error uploading media file:', error);
    return null;
  }
};

export const getMediaFiles = async (): Promise<MediaFile[]> => {
  try {
    // Пытаемся получить из IndexedDB
    const files = await mediaDB.getFiles();
    return files.sort((a, b) => new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime());
  } catch (error) {
    console.warn('IndexedDB failed, using localStorage fallback:', error);
    
    // Fallback: получаем метаданные из localStorage
    const metadata = getFromLocalStorage();
    return metadata.map(meta => ({
      ...meta,
      url: '' // URL будет пустой для fallback
    })).sort((a, b) => new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime());
  }
};

export const deleteMediaFile = async (id: string): Promise<boolean> => {
  try {
    // Пытаемся удалить из IndexedDB
    await mediaDB.deleteFile(id);
    return true;
  } catch (error) {
    console.warn('IndexedDB failed, using localStorage fallback:', error);
    
    // Fallback: удаляем из localStorage
    const metadata = getFromLocalStorage();
    const filtered = metadata.filter(item => item.id !== id);
    return saveToLocalStorage(filtered);
  }
};

export const clearAllMedia = async (): Promise<boolean> => {
  try {
    await mediaDB.clearAll();
    localStorage.removeItem(STORAGE_KEY);
    return true;
  } catch (error) {
    console.error('Error clearing media:', error);
    return false;
  }
};

// Утилита для проверки доступного места
export const getStorageInfo = (): { used: number; available: number } => {
  let used = 0;
  let available = 5 * 1024 * 1024; // 5MB по умолчанию

  try {
    // Подсчитываем использованное место в localStorage
    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        used += localStorage[key].length;
      }
    }
  } catch (error) {
    console.error('Error calculating storage:', error);
  }

  return { used, available: available - used };
};
