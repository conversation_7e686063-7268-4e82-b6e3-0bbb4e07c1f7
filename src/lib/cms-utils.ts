// CMS Utilities for Content Management

import { PageContent, BlogPost, SEOData, ContentStatus } from '@/types/cms';

// Generate slug from title
export const generateSlug = (title: string): string => {
  return title
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
};

// Generate unique ID
export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// Format date for display
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('ru-RU', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// Calculate reading time for blog posts
export const calculateReadingTime = (content: string): number => {
  const wordsPerMinute = 200;
  const words = content.trim().split(/\s+/).length;
  return Math.ceil(words / wordsPerMinute);
};

// Validate slug uniqueness
export const isSlugUnique = async (slug: string, excludeId?: string): Promise<boolean> => {
  // In a real implementation, this would check against a database
  // For now, we'll simulate with localStorage
  const existingContent = getAllContent();
  return !existingContent.some(item => 
    item.slug === slug && item.id !== excludeId
  );
};

// Get all content from storage (localStorage for now)
export const getAllContent = (): (PageContent | BlogPost)[] => {
  if (typeof window === 'undefined') return [];
  
  try {
    const stored = localStorage.getItem('cms_content');
    return stored ? JSON.parse(stored) : [];
  } catch {
    return [];
  }
};

// Save content to storage
export const saveContent = (content: PageContent | BlogPost): void => {
  if (typeof window === 'undefined') return;
  
  const allContent = getAllContent();
  const existingIndex = allContent.findIndex(item => item.id === content.id);
  
  if (existingIndex >= 0) {
    allContent[existingIndex] = content;
  } else {
    allContent.push(content);
  }
  
  localStorage.setItem('cms_content', JSON.stringify(allContent));
};

// Delete content from storage
export const deleteContent = (id: string): void => {
  if (typeof window === 'undefined') return;
  
  const allContent = getAllContent();
  const filtered = allContent.filter(item => item.id !== id);
  localStorage.setItem('cms_content', JSON.stringify(filtered));
};

// Get content by ID
export const getContentById = (id: string): PageContent | BlogPost | null => {
  const allContent = getAllContent();
  return allContent.find(item => item.id === id) || null;
};

// Get content by slug
export const getContentBySlug = (slug: string): PageContent | BlogPost | null => {
  const allContent = getAllContent();
  return allContent.find(item => item.slug === slug) || null;
};

// Get published content only
export const getPublishedContent = (): (PageContent | BlogPost)[] => {
  return getAllContent().filter(item => item.status === 'published');
};

// Get blog posts only
export const getBlogPosts = (status?: ContentStatus): BlogPost[] => {
  const allContent = getAllContent();
  let posts = allContent.filter(item => item.type === 'blog') as BlogPost[];
  
  if (status) {
    posts = posts.filter(post => post.status === status);
  }
  
  return posts.sort((a, b) => 
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );
};

// Get pages only
export const getPages = (status?: ContentStatus): PageContent[] => {
  const allContent = getAllContent();
  let pages = allContent.filter(item => item.type === 'page') as PageContent[];
  
  if (status) {
    pages = pages.filter(page => page.status === status);
  }
  
  return pages.sort((a, b) => 
    new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
  );
};

// Create default SEO data
export const createDefaultSEO = (title: string, description?: string): SEOData => {
  return {
    metaTitle: title,
    metaDescription: description || `${title} - Setmee`,
    keywords: ['Kommo', 'CRM', 'автоматизация', 'бизнес'],
    ogTitle: title,
    ogDescription: description || `${title} - Setmee`,
    noIndex: false,
    noFollow: false
  };
};

// Validate content data
export const validateContent = (content: Partial<PageContent | BlogPost>): string[] => {
  const errors: string[] = [];
  
  if (!content.title?.trim()) {
    errors.push('Заголовок обязателен');
  }
  
  if (!content.slug?.trim()) {
    errors.push('URL slug обязателен');
  } else if (!/^[a-z0-9-]+$/.test(content.slug)) {
    errors.push('URL slug может содержать только строчные буквы, цифры и дефисы');
  }
  
  if (content.type === 'blog') {
    const blogPost = content as Partial<BlogPost>;
    if (!blogPost.content?.trim()) {
      errors.push('Содержание статьи обязательно');
    }
    if (!blogPost.excerpt?.trim()) {
      errors.push('Краткое описание обязательно');
    }
    if (!blogPost.category?.trim()) {
      errors.push('Категория обязательна');
    }
  }
  
  if (content.seo) {
    if (!content.seo.metaTitle?.trim()) {
      errors.push('Meta title обязателен');
    }
    if (!content.seo.metaDescription?.trim()) {
      errors.push('Meta description обязательно');
    }
    if (content.seo.metaDescription && content.seo.metaDescription.length > 160) {
      errors.push('Meta description не должно превышать 160 символов');
    }
  }
  
  return errors;
};

// Export content as JSON
export const exportContent = (): string => {
  const allContent = getAllContent();
  return JSON.stringify(allContent, null, 2);
};

// Import content from JSON
export const importContent = (jsonData: string): boolean => {
  try {
    const content = JSON.parse(jsonData);
    if (Array.isArray(content)) {
      localStorage.setItem('cms_content', JSON.stringify(content));
      return true;
    }
    return false;
  } catch {
    return false;
  }
};

// Search content
export const searchContent = (query: string): (PageContent | BlogPost)[] => {
  if (!query.trim()) return [];
  
  const allContent = getAllContent();
  const searchTerm = query.toLowerCase();
  
  return allContent.filter(item =>
    item.title.toLowerCase().includes(searchTerm) ||
    item.seo.metaDescription.toLowerCase().includes(searchTerm) ||
    (item.type === 'blog' && (item as BlogPost).content.toLowerCase().includes(searchTerm))
  );
};

// Get single blog post
export const getBlogPost = (id: string): BlogPost | null => {
  const posts = getBlogPosts();
  return posts.find(post => post.id === id) || null;
};

// Create new blog post
export const createBlogPost = (postData: Omit<BlogPost, 'id' | 'createdAt' | 'updatedAt'>): BlogPost => {
  const newPost: BlogPost = {
    ...postData,
    id: generateId(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  saveContent(newPost);
  return newPost;
};

// Update blog post
export const updateBlogPost = (id: string, updates: Partial<BlogPost>): BlogPost | null => {
  const post = getBlogPost(id);
  if (!post) return null;

  const updatedPost: BlogPost = {
    ...post,
    ...updates,
    updatedAt: new Date().toISOString(),
  };

  saveContent(updatedPost);
  return updatedPost;
};

// Delete blog post
export const deleteBlogPost = (id: string): boolean => {
  const post = getBlogPost(id);
  if (!post) return false;

  deleteContent(id);
  return true;
};

// Settings management
export const getSettings = (): Record<string, unknown> => {
  if (typeof window === 'undefined') return {};

  try {
    const settings = localStorage.getItem('site_settings');
    return settings ? JSON.parse(settings) : {
      siteName: 'Setmee',
      siteDescription: 'Профессиональная интеграция Kommo CRM',
      siteUrl: 'https://setmee.ru',
      contactEmail: '<EMAIL>',
      blogEnabled: true,
      commentsEnabled: false,
      postsPerPage: 10,
      metaTitle: 'Setmee - Kommo Partner',
      metaDescription: 'Профессиональная интеграция и настройка Kommo CRM для вашего бизнеса',
      googleAnalytics: '',
      yandexMetrica: '',
      blogHeroTitle: 'Блог Setmee',
      blogHeroDescription: 'Полезные статьи о Kommo CRM, интеграциях, автоматизации бизнеса и лучших практиках',
      blogHeroBackgroundColor: '#1e40af',
    };
  } catch (error) {
    console.error('Error loading settings:', error);
    return {};
  }
};

export const saveSettings = (settings: Record<string, unknown>): boolean => {
  if (typeof window === 'undefined') return false;

  try {
    localStorage.setItem('site_settings', JSON.stringify(settings));
    return true;
  } catch (error) {
    console.error('Error saving settings:', error);
    return false;
  }
};

// Media file interface
export interface MediaFile {
  id: string;
  name: string;
  originalName: string;
  size: number;
  type: string;
  url: string;
  uploadedAt: string;
}

// Media management
export const getMediaFiles = (): MediaFile[] => {
  if (typeof window === 'undefined') return [];

  try {
    const files = localStorage.getItem('media_files');
    return files ? JSON.parse(files) : [];
  } catch (error) {
    console.error('Error loading media files:', error);
    return [];
  }
};

export const uploadMediaFile = (file: File): Promise<MediaFile> => {
  return new Promise((resolve, reject) => {
    if (typeof window === 'undefined') {
      reject(new Error('Window is not available'));
      return;
    }

    const reader = new FileReader();
    reader.onload = () => {
      try {
        const mediaFile: MediaFile = {
          id: generateId(),
          name: file.name.replace(/[^a-zA-Z0-9.-]/g, '_'),
          originalName: file.name,
          size: file.size,
          type: file.type,
          url: reader.result as string,
          uploadedAt: new Date().toISOString(),
        };

        const existingFiles = getMediaFiles();
        existingFiles.push(mediaFile);

        localStorage.setItem('media_files', JSON.stringify(existingFiles));
        resolve(mediaFile);
      } catch (error) {
        reject(error);
      }
    };

    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };

    reader.readAsDataURL(file);
  });
};

export const saveMediaFile = (mediaFile: MediaFile): boolean => {
  if (typeof window === 'undefined') return false;

  try {
    const existingFiles = getMediaFiles();
    existingFiles.push(mediaFile);
    localStorage.setItem('media_files', JSON.stringify(existingFiles));
    return true;
  } catch (error) {
    console.error('Error saving media file:', error);
    return false;
  }
};

export const deleteMediaFile = (id: string): boolean => {
  if (typeof window === 'undefined') return false;

  try {
    const files = getMediaFiles();
    const filteredFiles = files.filter(file => file.id !== id);

    if (filteredFiles.length === files.length) return false;

    localStorage.setItem('media_files', JSON.stringify(filteredFiles));
    return true;
  } catch (error) {
    console.error('Error deleting media file:', error);
    return false;
  }
};
