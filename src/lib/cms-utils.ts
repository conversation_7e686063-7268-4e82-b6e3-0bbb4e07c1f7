// CMS Utilities for Content Management

import { PageContent, BlogPost, SEOData, ContentStatus } from '@/types/cms';

// Generate slug from title
export const generateSlug = (title: string): string => {
  return title
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
};

// Generate unique ID
export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// Format date for display
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('ru-RU', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// Calculate reading time for blog posts
export const calculateReadingTime = (content: string): number => {
  const wordsPerMinute = 200;
  const words = content.trim().split(/\s+/).length;
  return Math.ceil(words / wordsPerMinute);
};

// Validate slug uniqueness
export const isSlugUnique = async (slug: string, excludeId?: string): Promise<boolean> => {
  // In a real implementation, this would check against a database
  // For now, we'll simulate with localStorage
  const existingContent = getAllContent();
  return !existingContent.some(item => 
    item.slug === slug && item.id !== excludeId
  );
};

// Тестовые данные для сервера
const getServerSideTestData = (): (PageContent | BlogPost)[] => {
  return [
    {
      id: 'mcuz5bxhwv8o2t3xo4o',
      type: 'blog',
      title: 'Why you should launch a website for your business in 2025',
      slug: 'first-article',
      excerpt: 'In today\'s digital age, having a website is no longer optional for businesses. Here\'s why 2025 is the perfect time to launch your business website.',
      content: '# Why you should launch a website for your business in 2025\n\nIn today\'s digital age, having a website is no longer optional for businesses. Here\'s why 2025 is the perfect time to launch your business website.\n\n## 1. Online is the norm\n\nMore than 80% of consumers research products and services online before making a purchase decision. Without a website, you\'re missing out on a huge potential customer base.\n\n## 2. 24/7 availability\n\nUnlike a physical store, your website works for you 24/7. Customers can learn about your products, make purchases, or contact you at any time.\n\n## 3. Cost-effective marketing\n\nA website is one of the most cost-effective marketing tools available. It allows you to reach a global audience without the high costs of traditional advertising.',
      status: 'published',
      author: 'SetMee Team',
      tags: ['business', 'website', 'digital marketing'],
      featuredImage: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkJ1c2luZXNzIFdlYnNpdGU8L3RleHQ+Cjwvc3ZnPg==',
      contentImages: [],
      createdAt: '2024-01-15T10:00:00.000Z',
      updatedAt: '2024-01-15T10:00:00.000Z',
      publishedAt: '2024-01-15T10:00:00.000Z',
      seo: {
        metaTitle: 'Why Launch a Business Website in 2025 | SetMee',
        metaDescription: 'Discover why 2025 is the perfect time to launch your business website. Learn about online presence, 24/7 availability, and cost-effective marketing.',
        keywords: ['business website', 'digital marketing', '2025', 'online presence'],
        ogTitle: 'Why Launch a Business Website in 2025',
        ogDescription: 'Discover why 2025 is the perfect time to launch your business website.',
        ogImage: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkJ1c2luZXNzIFdlYnNpdGU8L3RleHQ+Cjwvc3ZnPg==',
        ogType: 'article',
        twitterTitle: 'Why Launch a Business Website in 2025',
        twitterDescription: 'Discover why 2025 is the perfect time to launch your business website.',
        twitterImage: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkJ1c2luZXNzIFdlYnNpdGU8L3RleHQ+Cjwvc3ZnPg==',
        twitterCard: 'summary_large_image',
        canonicalUrl: '',
        noIndex: false,
        noFollow: false,
        noArchive: false,
        noSnippet: false,
        schemaType: 'BlogPosting',
        focusKeyword: 'business website',
        breadcrumbs: true
      }
    } as BlogPost
  ];
};

// Get all content from storage (localStorage for now)
export const getAllContent = (): (PageContent | BlogPost)[] => {
  if (typeof window === 'undefined') {
    // На сервере всегда возвращаем пустой массив для избежания гидратации
    return [];
  }

  try {
    const stored = localStorage.getItem('cms_content');
    const data = stored ? JSON.parse(stored) : [];

    // Если данных нет, инициализируем тестовыми данными
    if (data.length === 0) {
      const testData = getServerSideTestData();
      localStorage.setItem('cms_content', JSON.stringify(testData));
      return testData;
    }

    return data;
  } catch {
    return [];
  }
};

// Save content to storage
export const saveContent = (content: PageContent | BlogPost): void => {
  if (typeof window === 'undefined') return;

  // Получаем только клиентские данные для сохранения
  const stored = localStorage.getItem('cms_content');
  const allContent = stored ? JSON.parse(stored) : [];

  const existingIndex = allContent.findIndex((item: any) => item.id === content.id);
  const isUpdate = existingIndex >= 0;

  if (isUpdate) {
    allContent[existingIndex] = content;
  } else {
    allContent.push(content);
  }

  localStorage.setItem('cms_content', JSON.stringify(allContent));

  // Уведомляем другие компоненты об изменении данных
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new CustomEvent('cms-content-updated', {
      detail: {
        action: isUpdate ? 'update' : 'create',
        id: content.id,
        type: content.type
      }
    }));
  }
};

// Delete content from storage
export const deleteContent = (id: string): void => {
  if (typeof window === 'undefined') return;

  try {
    // Получаем только клиентские данные из localStorage
    const stored = localStorage.getItem('cms_content');
    const allContent = stored ? JSON.parse(stored) : [];

    // Фильтруем контент, исключая удаляемый элемент
    const filtered = allContent.filter((item: any) => item.id !== id);

    // Сохраняем обновленный список
    localStorage.setItem('cms_content', JSON.stringify(filtered));

    console.log(`Контент с ID ${id} удален. Осталось элементов: ${filtered.length}`);
  } catch (error) {
    console.error('Ошибка при удалении контента:', error);
  }
};

// Get content by ID
export const getContentById = (id: string): PageContent | BlogPost | null => {
  const allContent = getAllContent();
  return allContent.find(item => item.id === id) || null;
};

// Get content by slug
export const getContentBySlug = (slug: string): PageContent | BlogPost | null => {
  const allContent = getAllContent();
  return allContent.find(item => item.slug === slug) || null;
};

// Get published content only
export const getPublishedContent = (): (PageContent | BlogPost)[] => {
  return getAllContent().filter(item => item.status === 'published');
};

// Get blog posts only
export const getBlogPosts = (status?: ContentStatus): BlogPost[] => {
  const allContent = getAllContent();
  let posts = allContent.filter(item => item.type === 'blog') as BlogPost[];

  if (status) {
    posts = posts.filter(post => post.status === status);
  }

  return posts.sort((a, b) =>
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );
};

// Get pages only
export const getPages = (status?: ContentStatus): PageContent[] => {
  const allContent = getAllContent();
  let pages = allContent.filter(item => item.type === 'page') as PageContent[];
  
  if (status) {
    pages = pages.filter(page => page.status === status);
  }
  
  return pages.sort((a, b) => 
    new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
  );
};

// Create default SEO data
export const createDefaultSEO = (title: string, description?: string): SEOData => {
  return {
    metaTitle: title,
    metaDescription: description || `${title} - Setmee`,
    keywords: ['Kommo', 'CRM', 'автоматизация', 'бизнес'],
    ogTitle: title,
    ogDescription: description || `${title} - Setmee`,
    noIndex: false,
    noFollow: false
  };
};

// Validate content data
export const validateContent = (content: Partial<PageContent | BlogPost>): string[] => {
  const errors: string[] = [];
  
  if (!content.title?.trim()) {
    errors.push('Заголовок обязателен');
  }
  
  if (!content.slug?.trim()) {
    errors.push('URL slug обязателен');
  } else if (!/^[a-z0-9-]+$/.test(content.slug)) {
    errors.push('URL slug может содержать только строчные буквы, цифры и дефисы');
  }
  
  if (content.type === 'blog') {
    const blogPost = content as Partial<BlogPost>;
    if (!blogPost.content?.trim()) {
      errors.push('Содержание статьи обязательно');
    }
    if (!blogPost.excerpt?.trim()) {
      errors.push('Краткое описание обязательно');
    }
    if (!blogPost.category?.trim()) {
      errors.push('Категория обязательна');
    }
  }
  
  if (content.seo) {
    if (!content.seo.metaTitle?.trim()) {
      errors.push('Meta title обязателен');
    }
    if (!content.seo.metaDescription?.trim()) {
      errors.push('Meta description обязательно');
    }
    if (content.seo.metaDescription && content.seo.metaDescription.length > 160) {
      errors.push('Meta description не должно превышать 160 символов');
    }
  }
  
  return errors;
};

// Export content as JSON
export const exportContent = (): string => {
  const allContent = getAllContent();
  return JSON.stringify(allContent, null, 2);
};

// Import content from JSON
export const importContent = (jsonData: string): boolean => {
  try {
    const content = JSON.parse(jsonData);
    if (Array.isArray(content)) {
      localStorage.setItem('cms_content', JSON.stringify(content));
      return true;
    }
    return false;
  } catch {
    return false;
  }
};

// Search content
export const searchContent = (query: string): (PageContent | BlogPost)[] => {
  if (!query.trim()) return [];
  
  const allContent = getAllContent();
  const searchTerm = query.toLowerCase();
  
  return allContent.filter(item =>
    item.title.toLowerCase().includes(searchTerm) ||
    item.seo.metaDescription.toLowerCase().includes(searchTerm) ||
    (item.type === 'blog' && (item as BlogPost).content.toLowerCase().includes(searchTerm))
  );
};

// Get single blog post
export const getBlogPost = (id: string): BlogPost | null => {
  const posts = getBlogPosts();
  return posts.find(post => post.id === id) || null;
};

// Create new blog post
export const createBlogPost = (postData: Omit<BlogPost, 'id' | 'createdAt' | 'updatedAt'>): BlogPost => {
  const newPost: BlogPost = {
    ...postData,
    id: generateId(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  saveContent(newPost);
  return newPost;
};

// Update blog post
export const updateBlogPost = (id: string, updates: Partial<BlogPost>): BlogPost | null => {
  const post = getBlogPost(id);
  if (!post) return null;

  const updatedPost: BlogPost = {
    ...post,
    ...updates,
    updatedAt: new Date().toISOString(),
  };

  saveContent(updatedPost);
  return updatedPost;
};

// Delete blog post
export const deleteBlogPost = (id: string): boolean => {
  if (typeof window === 'undefined') return false;

  try {
    console.log(`deleteBlogPost: Начинаем удаление поста с ID ${id}`);

    // Получаем текущие данные
    const stored = localStorage.getItem('cms_content');
    const allContent = stored ? JSON.parse(stored) : [];

    console.log(`deleteBlogPost: Всего контента до удаления: ${allContent.length}`);
    console.log(`deleteBlogPost: Контент:`, allContent.map((item: any) => ({ id: item.id, type: item.type, title: item.title })));

    const postExists = allContent.some((item: any) => item.id === id && item.type === 'blog');
    if (!postExists) {
      console.log(`deleteBlogPost: Пост с ID ${id} не найден`);
      return false;
    }

    // Фильтруем контент, исключая удаляемый пост
    const filteredContent = allContent.filter((item: any) => item.id !== id);

    console.log(`deleteBlogPost: Контента после фильтрации: ${filteredContent.length}`);
    console.log(`deleteBlogPost: Отфильтрованный контент:`, filteredContent.map((item: any) => ({ id: item.id, type: item.type, title: item.title })));

    // Сохраняем обновленные данные
    localStorage.setItem('cms_content', JSON.stringify(filteredContent));

    // Проверяем, что данные действительно сохранились
    const verification = localStorage.getItem('cms_content');
    const verificationData = verification ? JSON.parse(verification) : [];
    console.log(`deleteBlogPost: Проверка сохранения - контента в localStorage: ${verificationData.length}`);

    // Уведомляем другие компоненты об изменении данных
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('cms-content-updated', {
        detail: { action: 'delete', id, type: 'blog' }
      }));
    }

    console.log(`deleteBlogPost: Пост с ID ${id} успешно удален`);
    return true;
  } catch (error) {
    console.error('deleteBlogPost: Ошибка при удалении поста:', error);
    return false;
  }
};

// Настройки по умолчанию для сервера и клиента
const getDefaultSettings = () => ({
  siteName: 'Setmee',
  siteDescription: 'Professional Kommo CRM Integration',
  siteUrl: 'https://setmee.ru',
  contactEmail: '<EMAIL>',
  blogEnabled: true,
  commentsEnabled: false,
  postsPerPage: 10,
  googleAnalytics: '',
  yandexMetrica: '',
  blogHeroTitle: 'SetMee Blog',
  blogHeroDescription: 'Expert articles about Kommo CRM, integrations, business automation and best practices',
  blogHeroBackgroundColor: '#1e40af',
  // SEO настройки для блога
  blogMetaTitle: 'SetMee Blog - Kommo CRM & Business Automation Articles',
  blogMetaDescription: 'Expert articles about Kommo CRM, integrations, business automation and best practices. Professional guides and tips.',
  blogKeywords: 'Kommo CRM, business automation, integrations, CRM system, sales, marketing',
  blogOgTitle: 'SetMee Blog - Kommo CRM & Business Automation Articles',
  blogOgDescription: 'Expert articles about Kommo CRM, integrations, business automation and best practices.',
});

// Settings management
export const getSettings = (): Record<string, unknown> => {
  const defaultSettings = getDefaultSettings();

  if (typeof window === 'undefined') {
    // На сервере возвращаем настройки по умолчанию
    return defaultSettings;
  }

  try {
    const settings = localStorage.getItem('site_settings');
    return settings ? JSON.parse(settings) : defaultSettings;
  } catch (error) {
    console.error('Error loading settings:', error);
    return defaultSettings;
  }
};

export const saveSettings = (settings: Record<string, unknown>): boolean => {
  if (typeof window === 'undefined') return false;

  try {
    localStorage.setItem('site_settings', JSON.stringify(settings));
    return true;
  } catch (error) {
    console.error('Error saving settings:', error);
    return false;
  }
};

// Media file interface
export interface MediaFile {
  id: string;
  name: string;
  originalName: string;
  size: number;
  type: string;
  url: string;
  uploadedAt: string;
}

// Media management
export const getMediaFiles = (): MediaFile[] => {
  if (typeof window === 'undefined') return [];

  try {
    const files = localStorage.getItem('media_files');
    return files ? JSON.parse(files) : [];
  } catch (error) {
    console.error('Error loading media files:', error);
    return [];
  }
};

export const uploadMediaFile = (file: File): Promise<MediaFile> => {
  return new Promise((resolve, reject) => {
    if (typeof window === 'undefined') {
      reject(new Error('Window is not available'));
      return;
    }

    const reader = new FileReader();
    reader.onload = () => {
      try {
        const mediaFile: MediaFile = {
          id: generateId(),
          name: file.name.replace(/[^a-zA-Z0-9.-]/g, '_'),
          originalName: file.name,
          size: file.size,
          type: file.type,
          url: reader.result as string,
          uploadedAt: new Date().toISOString(),
        };

        const existingFiles = getMediaFiles();
        existingFiles.push(mediaFile);

        localStorage.setItem('media_files', JSON.stringify(existingFiles));
        resolve(mediaFile);
      } catch (error) {
        reject(error);
      }
    };

    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };

    reader.readAsDataURL(file);
  });
};

export const saveMediaFile = (mediaFile: MediaFile): boolean => {
  if (typeof window === 'undefined') return false;

  try {
    const existingFiles = getMediaFiles();
    existingFiles.push(mediaFile);
    localStorage.setItem('media_files', JSON.stringify(existingFiles));
    return true;
  } catch (error) {
    console.error('Error saving media file:', error);
    return false;
  }
};

export const deleteMediaFile = (id: string): boolean => {
  if (typeof window === 'undefined') return false;

  try {
    const files = getMediaFiles();
    const filteredFiles = files.filter(file => file.id !== id);

    if (filteredFiles.length === files.length) return false;

    localStorage.setItem('media_files', JSON.stringify(filteredFiles));
    return true;
  } catch (error) {
    console.error('Error deleting media file:', error);
    return false;
  }
};
