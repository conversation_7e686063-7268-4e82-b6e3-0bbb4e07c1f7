import React from 'react';
import Link from 'next/link';
import { getBlogPosts, getSettings } from '@/lib/cms-utils';
import { BlogPost } from '@/types/cms';
import { Metadata } from 'next';
import BlogHero from '@/components/blog/BlogHero';

// Генерация мета-тегов для главной страницы блога
export async function generateMetadata(): Promise<Metadata> {
  const siteSettings = getSettings();

  const blogMetaTitle = siteSettings?.blogMetaTitle as string || 'SetMee Blog - Kommo CRM & Business Automation Articles';
  const blogMetaDescription = siteSettings?.blogMetaDescription as string || 'Expert articles about Kommo CRM, integrations, business automation and best practices. Professional guides and tips.';
  const blogKeywords = siteSettings?.blogKeywords as string || 'Kommo CRM, business automation, integrations, CRM system, sales, marketing';
  const blogOgTitle = siteSettings?.blogOgTitle as string || blogMetaTitle;
  const blogOgDescription = siteSettings?.blogOgDescription as string || blogMetaDescription;

  return {
    title: blogMetaTitle,
    description: blogMetaDescription,
    keywords: blogKeywords,

    openGraph: {
      title: blogOgTitle,
      description: blogOgDescription,
      type: 'website',
      url: '/blog',
      siteName: 'SetMee',
    },

    twitter: {
      card: 'summary_large_image',
      title: blogOgTitle,
      description: blogOgDescription,
    },

    robots: {
      index: true,
      follow: true,
    },
  };
}

const BlogPage: React.FC = () => {
  const posts = getBlogPosts().filter(post => post.status === 'published');
  const siteSettings = getSettings();

  const settings = {
    blogHeroTitle: siteSettings?.blogHeroTitle || 'Блог Setmee',
    blogHeroDescription: siteSettings?.blogHeroDescription || 'Полезные статьи о Kommo CRM, интеграциях, автоматизации бизнеса и лучших практиках',
    blogHeroBackgroundColor: siteSettings?.blogHeroBackgroundColor || '#1e40af'
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <BlogHero
        defaultTitle={settings.blogHeroTitle}
        defaultDescription={settings.blogHeroDescription}
        defaultBackgroundColor={settings.blogHeroBackgroundColor}
      />

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {posts.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {posts.map((post) => (
              <article key={post.id} className="group cursor-pointer">
                <Link href={`/blog/${post.slug}`}>
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-200 group-hover:border-primary-300">
                    {/* Featured Image */}
                    <div className="aspect-video relative overflow-hidden">
                      {post.featuredImage ? (
                        <img
                          src={post.featuredImage}
                          alt={post.title}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center">
                          <svg className="w-12 h-12 text-primary-600 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                          </svg>
                        </div>
                      )}

                      {/* Category badge */}
                      {post.category && (
                        <div className="absolute top-3 left-3">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white text-primary-700 shadow-sm">
                            {post.category}
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="p-4">
                      {/* Meta info */}
                      <div className="flex items-center text-xs text-gray-500 mb-2">
                        <time dateTime={post.createdAt}>
                          {formatDate(post.createdAt)}
                        </time>
                        <span className="mx-2">•</span>
                        <span>{post.author}</span>
                      </div>

                      {/* Title */}
                      <h2 className="text-sm font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-primary-600 transition-colors">
                        {post.title}
                      </h2>

                      {/* Excerpt */}
                      <p className="text-xs text-gray-600 mb-3 line-clamp-3">
                        {post.excerpt}
                      </p>

                      {/* Tags */}
                      {post.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {post.tags.slice(0, 2).map((tag) => (
                            <span
                              key={tag}
                              className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-700"
                            >
                              {tag}
                            </span>
                          ))}
                          {post.tags.length > 2 && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-700">
                              +{post.tags.length - 2}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </Link>
              </article>
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="max-w-md mx-auto">
              <svg className="mx-auto h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
              </svg>
              <h3 className="mt-4 text-lg font-medium text-gray-900">Пока нет статей</h3>
              <p className="mt-2 text-gray-500">
                Скоро здесь появятся интересные статьи о Kommo CRM и автоматизации бизнеса.
              </p>
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default BlogPage;
