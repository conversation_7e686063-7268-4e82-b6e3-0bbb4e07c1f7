import React from 'react';
import { getSettings } from '@/lib/cms-utils';
import { Metadata } from 'next';
import BlogHeroUpdater from '@/components/blog/BlogHeroUpdater';
import BlogPostsList from '@/components/blog/BlogPostsList';

// Генерация мета-тегов для главной страницы блога
export async function generateMetadata(): Promise<Metadata> {
  const siteSettings = getSettings();

  const blogMetaTitle = siteSettings?.blogMetaTitle as string || 'SetMee Blog - Kommo CRM & Business Automation Articles';
  const blogMetaDescription = siteSettings?.blogMetaDescription as string || 'Expert articles about Kommo CRM, integrations, business automation and best practices. Professional guides and tips.';
  const blogKeywords = siteSettings?.blogKeywords as string || 'Kommo CRM, business automation, integrations, CRM system, sales, marketing';
  const blogOgTitle = siteSettings?.blogOgTitle as string || blogMetaTitle;
  const blogOgDescription = siteSettings?.blogOgDescription as string || blogMetaDescription;

  return {
    title: blogMetaTitle,
    description: blogMetaDescription,
    keywords: blogKeywords,

    openGraph: {
      title: blogOgTitle,
      description: blogOgDescription,
      type: 'website',
      url: '/blog',
      siteName: 'SetMee',
    },

    twitter: {
      card: 'summary_large_image',
      title: blogOgTitle,
      description: blogOgDescription,
    },

    robots: {
      index: true,
      follow: true,
    },
  };
}

const BlogPage: React.FC = () => {
  const siteSettings = getSettings();

  const settings = {
    blogHeroTitle: siteSettings?.blogHeroTitle || 'SetMee Blog',
    blogHeroDescription: siteSettings?.blogHeroDescription || 'Expert articles about Kommo CRM, integrations, business automation and best practices',
    blogHeroBackgroundColor: siteSettings?.blogHeroBackgroundColor || '#1e40af'
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section - Server Rendered */}
      <div
        id="blog-hero"
        className="py-16 px-4 sm:px-6 lg:px-8 transition-all duration-300"
        style={{ backgroundColor: settings.blogHeroBackgroundColor }}
      >
        <div className="max-w-4xl mx-auto text-center">
          <h1
            id="blog-hero-title"
            className="text-4xl md:text-5xl font-bold text-white mb-6 transition-all duration-300"
          >
            {settings.blogHeroTitle}
          </h1>
          <p
            id="blog-hero-description"
            className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed transition-all duration-300"
          >
            {settings.blogHeroDescription}
          </p>
        </div>
      </div>



      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <BlogPostsList />
      </main>

      {/* Клиентский компонент для обновления Hero блока */}
      <BlogHeroUpdater />
    </div>
  );
};

export default BlogPage;
