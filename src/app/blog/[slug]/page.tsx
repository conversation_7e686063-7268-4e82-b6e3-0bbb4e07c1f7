'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { getBlogPosts } from '@/lib/cms-utils';
import { BlogPost } from '@/types/cms';

const BlogPostPage: React.FC = () => {
  const params = useParams();
  const [post, setPost] = useState<BlogPost | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const slug = params.slug as string;
    const posts = getBlogPosts();
    const foundPost = posts.find(p => p.slug === slug && p.status === 'published');
    setPost(foundPost || null);
    setLoading(false);
  }, [params.slug]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Загрузка статьи...</p>
        </div>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">404</h1>
          <p className="text-xl text-gray-600 mb-8">Статья не найдена</p>
          <Link
            href="/blog"
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
          >
            Вернуться к блогу
          </Link>
        </div>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  // Функция для извлечения соответствий шорткодов и URL из комментариев
  const extractImageMappings = (content: string): Record<string, string> => {
    const mappings: Record<string, string> = {};
    const commentRegex = /<!-- \[img:([^\]]+)\] = ([^>]+) -->/g;
    let match;

    while ((match = commentRegex.exec(content)) !== null) {
      mappings[`[img:${match[1]}]`] = match[2];
    }

    return mappings;
  };

  // Convert markdown-like content to HTML (basic implementation)
  const formatContent = (content: string) => {
    const imageMappings = extractImageMappings(content);

    return content
      // Убираем комментарии из отображения
      .replace(/<!-- \[img:[^\]]+\] = [^>]+ -->/g, '')
      .split('\n\n')
      .map((paragraph, index) => {
        // Handle shortcode images [img:id]
        if (paragraph.includes('[img:')) {
          const shortcodeRegex = /\[img:([^\]]+)\]/g;
          const processedParagraph = paragraph.replace(shortcodeRegex, (match) => {
            const imageUrl = imageMappings[match];
            if (imageUrl) {
              // Исключаем featuredImage из контента
              if (post?.featuredImage && imageUrl === post.featuredImage) {
                return ''; // Не показываем featuredImage в контенте
              }
              return `<div class="my-6"><img src="${imageUrl}" alt="Изображение" class="w-full max-w-2xl mx-auto rounded-lg shadow-md" /></div>`;
            }
            return match;
          });
          return processedParagraph ? `<div key="${index}" class="text-gray-700 mb-4 leading-relaxed">${processedParagraph}</div>` : '';
        }

        // Handle regular markdown images ![alt](url)
        if (paragraph.includes('![') && paragraph.includes('](')) {
          const imageRegex = /!\[([^\]]*)\]\(([^)]+)\)/g;
          const processedParagraph = paragraph.replace(imageRegex, (match, alt, url) => {
            // Исключаем featuredImage из контента
            if (post?.featuredImage && url === post.featuredImage) {
              return ''; // Не показываем featuredImage в контенте
            }
            return `<div class="my-6"><img src="${url}" alt="${alt}" class="w-full max-w-2xl mx-auto rounded-lg shadow-md" /></div>`;
          });
          return processedParagraph ? `<div key="${index}" class="text-gray-700 mb-4 leading-relaxed">${processedParagraph}</div>` : '';
        }

        if (paragraph.startsWith('# ')) {
          return `<h1 key="${index}" class="text-3xl font-bold text-gray-900 mb-6">${paragraph.slice(2)}</h1>`;
        }
        if (paragraph.startsWith('## ')) {
          return `<h2 key="${index}" class="text-2xl font-semibold text-gray-900 mb-4 mt-8">${paragraph.slice(3)}</h2>`;
        }
        if (paragraph.startsWith('### ')) {
          return `<h3 key="${index}" class="text-xl font-semibold text-gray-900 mb-3 mt-6">${paragraph.slice(4)}</h3>`;
        }
        return `<p key="${index}" class="text-gray-700 mb-4 leading-relaxed">${paragraph}</p>`;
      })
      .join('');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Breadcrumbs */}
        <nav className="mb-8">
          <ol className="flex items-center space-x-2 text-sm text-gray-500">
            <li>
              <Link href="/" className="hover:text-primary-600">
                Home
              </Link>
            </li>
            <li>
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            </li>
            <li>
              <Link href="/blog" className="hover:text-primary-600">
                Blog
              </Link>
            </li>
            <li>
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            </li>
            <li className="text-gray-900 font-medium truncate">
              {post.title}
            </li>
          </ol>
        </nav>

        {/* Article */}
        <article className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="p-8">
            {/* Article Header */}
            <header className="mb-8">
              <div className="flex items-center text-sm text-gray-500 mb-4">
                <time dateTime={post.createdAt}>
                  {formatDate(post.createdAt)}
                </time>
                <span className="mx-2">•</span>
                <span>{post.author}</span>
                {post.category && (
                  <>
                    <span className="mx-2">•</span>
                    <span className="capitalize">{post.category}</span>
                  </>
                )}
              </div>
              
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                {post.title}
              </h1>

              {post.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-6">
                  {post.tags.map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )}
            </header>

            {/* Article Content */}
            <div 
              className="prose prose-lg max-w-none"
              dangerouslySetInnerHTML={{ __html: formatContent(post.content) }}
            />
          </div>
        </article>

        {/* Back to Blog */}
        <div className="mt-12 text-center">
          <Link
            href="/blog"
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <svg className="mr-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Вернуться к блогу
          </Link>
        </div>
      </main>
    </div>
  );
};

export default BlogPostPage;
