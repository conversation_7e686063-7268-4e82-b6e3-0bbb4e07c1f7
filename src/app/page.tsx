import HeroSection from '@/components/sections/HeroSection';
import OurServicesSection from '@/components/sections/OurServicesSection';
import ImplementationProcessSection from '@/components/sections/ImplementationProcessSection';
import AuditSection from '@/components/sections/AuditSection';
import CustomIntegrationsSection from '@/components/sections/CustomIntegrationsSection';
import AboutSection from '@/components/sections/AboutSection';
import WhyKommoSection from '@/components/sections/WhyKommoSection';
import FeaturesSection from '@/components/sections/FeaturesSection';
import BusinessVerticalsSection from '@/components/sections/BusinessVerticalsSection';
import CTAVideoSection from '@/components/sections/CTAVideoSection';
import IntegrationsSection from '@/components/sections/IntegrationsSection';
import ContactSection from '@/components/sections/ContactSection';

export default function Home() {
  return (
    <div>
      {/* Диагностические маркеры */}
      <div className="bg-red-100 p-2 text-center text-sm">🔍 START: HeroSection</div>
      <HeroSection />

      <div className="bg-blue-100 p-2 text-center text-sm">🔍 START: OurServicesSection</div>
      <OurServicesSection />

      <div className="bg-green-100 p-2 text-center text-sm">🔍 START: ImplementationProcessSection</div>
      <ImplementationProcessSection />

      <div className="bg-yellow-100 p-2 text-center text-sm">🔍 START: AuditSection</div>
      <AuditSection />

      <div className="bg-purple-100 p-2 text-center text-sm">🔍 START: CustomIntegrationsSection</div>
      <CustomIntegrationsSection />

      <div className="bg-pink-100 p-2 text-center text-sm">🔍 START: AboutSection</div>
      <AboutSection />

      <div className="bg-indigo-100 p-2 text-center text-sm">🔍 START: WhyKommoSection</div>
      <WhyKommoSection />

      <div className="bg-teal-100 p-2 text-center text-sm">🔍 START: FeaturesSection</div>
      <FeaturesSection />

      <div className="bg-orange-100 p-2 text-center text-sm">🔍 START: BusinessVerticalsSection</div>
      <BusinessVerticalsSection />

      <div className="bg-cyan-100 p-2 text-center text-sm">🔍 START: CTAVideoSection</div>
      <CTAVideoSection />

      <div className="bg-lime-100 p-2 text-center text-sm">🔍 START: IntegrationsSection</div>
      <IntegrationsSection />

      <div className="bg-emerald-100 p-2 text-center text-sm">🔍 START: ContactSection</div>
      <ContactSection />

      <div className="bg-gray-100 p-2 text-center text-sm">🔍 END: All sections loaded</div>
    </div>
  );
}
