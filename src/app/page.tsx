import WhyKommoSection from '@/components/sections/WhyKommoSection';
import IntegrationsSection from '@/components/sections/IntegrationsSection';

export default function Home() {
  return (
    <div className="min-h-screen bg-white">
      {/* Простая hero секция */}
      <div className="bg-gradient-to-r from-blue-50 to-orange-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Kommo Partner: Certified Professional in Kommo CRM Integration
          </h1>
          <p className="text-lg text-gray-600 mb-8 max-w-3xl mx-auto">
            Smart solutions for your business based on Kommo CRM. We help with Kommo implementation, optimization, training and custom integrations.
          </p>

          <a
            href="/contact"
            className="inline-flex items-center justify-center bg-orange-500 text-white px-8 py-4 text-lg rounded-xl hover:bg-orange-600 transition-colors font-semibold"
            style={{
              pointerEvents: 'auto !important',
              position: 'relative',
              zIndex: 20,
              cursor: 'pointer'
            }}
          >
            Talk to an expert
          </a>
        </div>
      </div>

      {/* Услуги */}
      <div className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">Our Services</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-gray-50 p-8 rounded-lg text-center">
              <div className="w-16 h-16 bg-blue-500 rounded-lg mx-auto mb-4 flex items-center justify-center">
                <span className="text-white font-bold text-xl">K</span>
              </div>
              <h3 className="text-xl font-semibold mb-4">Kommo implementation</h3>
              <p className="text-gray-600">Complete CRM implementation tailored to your business needs</p>
            </div>

            <div className="bg-gray-50 p-8 rounded-lg text-center">
              <div className="w-16 h-16 bg-green-500 rounded-lg mx-auto mb-4 flex items-center justify-center">
                <span className="text-white font-bold text-xl">O</span>
              </div>
              <h3 className="text-xl font-semibold mb-4">Optimisation of use</h3>
              <p className="text-gray-600">Maximize efficiency and get the most out of your Kommo system</p>
            </div>

            <div className="bg-gray-50 p-8 rounded-lg text-center">
              <div className="w-16 h-16 bg-purple-500 rounded-lg mx-auto mb-4 flex items-center justify-center">
                <span className="text-white font-bold text-xl">C</span>
              </div>
              <h3 className="text-xl font-semibold mb-4">Custom integrations</h3>
              <p className="text-gray-600">We create custom integrations tailored to your specific business needs</p>
            </div>
          </div>
        </div>
      </div>

      {/* Компоненты, которые мы исправили */}
      <WhyKommoSection />
      <IntegrationsSection />
    </div>
  );
}
