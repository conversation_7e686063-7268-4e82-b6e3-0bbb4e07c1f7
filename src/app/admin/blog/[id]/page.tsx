'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import AdminLayout from '@/components/admin/AdminLayout';
import MediaGallery from '@/components/admin/MediaGallery';
import RichTextEditor from '@/components/admin/RichTextEditor';
import { getBlogPost, updateBlogPost } from '@/lib/cms-utils';
import { BlogPost } from '@/types/cms';

const EditBlogPostPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const [isLoading, setIsLoading] = useState(false);
  const [post, setPost] = useState<BlogPost | null>(null);
  const [showMediaGallery, setShowMediaGallery] = useState(false);
  const [mediaGalleryMode, setMediaGalleryMode] = useState<'featured' | 'content'>('featured');
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    excerpt: '',
    content: '',
    status: 'draft' as 'draft' | 'published',
    tags: '',
    featuredImage: '',
    contentImages: [] as string[],
    metaTitle: '',
    metaDescription: '',
  });

  useEffect(() => {
    if (params.id) {
      const postData = getBlogPost(params.id as string);
      if (postData) {
        setPost(postData);
        setFormData({
          title: postData.title,
          slug: postData.slug,
          excerpt: postData.excerpt,
          content: postData.content,
          status: postData.status === 'archived' ? 'draft' : postData.status as 'draft' | 'published',
          tags: postData.tags.join(', '),
          featuredImage: postData.featuredImage || '',
          contentImages: postData.contentImages || [],
          metaTitle: postData.seo.metaTitle || '',
          metaDescription: postData.seo.metaDescription || '',
        });
      } else {
        router.push('/admin/blog');
      }
    }
  }, [params.id, router]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!post) return;

    setIsLoading(true);

    try {
      const updatedData: Partial<BlogPost> = {
        title: formData.title,
        slug: formData.slug,
        excerpt: formData.excerpt,
        content: formData.content,
        status: formData.status,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean),
        seo: {
          metaTitle: formData.metaTitle || formData.title,
          metaDescription: formData.metaDescription || formData.excerpt,
          keywords: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean),
        },
      };

      updateBlogPost(post.id, updatedData);
      router.push('/admin/blog');
    } catch (error) {
      console.error('Ошибка при обновлении статьи:', error);
      alert('Ошибка при обновлении статьи');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveDraft = () => {
    setFormData(prev => ({ ...prev, status: 'draft' }));
    setTimeout(() => {
      document.getElementById('submit-form')?.click();
    }, 0);
  };

  const handlePublish = () => {
    setFormData(prev => ({ ...prev, status: 'published' }));
    setTimeout(() => {
      document.getElementById('submit-form')?.click();
    }, 0);
  };

  if (!post) {
    return (
      <AdminLayout>
        <div className="p-6 min-h-screen bg-gray-50">
          <div className="text-center">
            <p>Загрузка...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="p-6 min-h-screen bg-gray-50">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Редактирование статьи</h1>
              <p className="mt-2 text-gray-600">
                Редактирование: {post.title}
              </p>
            </div>
            <div className="flex space-x-3">
              <button
                type="button"
                onClick={() => router.push('/admin/blog')}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Отмена
              </button>
              <button
                type="button"
                onClick={handleSaveDraft}
                disabled={isLoading}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
              >
                Сохранить черновик
              </button>
              <button
                type="button"
                onClick={handlePublish}
                disabled={isLoading}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
              >
                Опубликовать
              </button>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Title */}
              <div className="bg-white rounded-lg shadow p-6">
                <div>
                  <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                    Заголовок статьи *
                  </label>
                  <input
                    type="text"
                    id="title"
                    name="title"
                    required
                    value={formData.title}
                    onChange={handleInputChange}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Введите заголовок статьи"
                  />
                </div>
              </div>

              {/* Slug */}
              <div className="bg-white rounded-lg shadow p-6">
                <div>
                  <label htmlFor="slug" className="block text-sm font-medium text-gray-700 mb-2">
                    URL (slug) *
                  </label>
                  <input
                    type="text"
                    id="slug"
                    name="slug"
                    required
                    value={formData.slug}
                    onChange={handleInputChange}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                    placeholder="url-статьи"
                  />
                  <div className="mt-2 flex items-center justify-between">
                    <p className="text-sm text-gray-500">
                      URL статьи: /blog/{formData.slug || 'url-статьи'}
                    </p>
                    {formData.status === 'published' && formData.slug && (
                      <a
                        href={`/blog/${formData.slug}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                        Посмотреть статью
                      </a>
                    )}
                  </div>
                </div>
              </div>

              {/* Excerpt */}
              <div className="bg-white rounded-lg shadow p-6">
                <div>
                  <label htmlFor="excerpt" className="block text-sm font-medium text-gray-700 mb-2">
                    Краткое описание *
                  </label>
                  <textarea
                    id="excerpt"
                    name="excerpt"
                    required
                    rows={3}
                    value={formData.excerpt}
                    onChange={handleInputChange}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Краткое описание статьи для превью"
                  />
                </div>
              </div>

              {/* Content */}
              <div className="bg-white rounded-lg shadow p-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-4">
                    Содержание статьи *
                  </label>
                  <RichTextEditor
                    value={formData.content}
                    onChange={(content) => setFormData(prev => ({ ...prev, content }))}
                    onImageInsert={() => {/* TODO: Add media gallery */}}
                    placeholder="Редактируйте содержание статьи..."
                    className="min-h-[500px]"
                  />
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Status */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Публикация</h3>
                <div>
                  <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
                    Статус
                  </label>
                  <select
                    id="status"
                    name="status"
                    value={formData.status}
                    onChange={handleInputChange}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="draft">Черновик</option>
                    <option value="published">Опубликовано</option>
                  </select>
                </div>
                <div className="mt-4 text-sm text-gray-500">
                  <p>Создано: {new Date(post.createdAt).toLocaleDateString('ru-RU')}</p>
                  <p>Обновлено: {new Date(post.updatedAt).toLocaleDateString('ru-RU')}</p>
                </div>
              </div>

              {/* Tags */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Теги</h3>
                <div>
                  <label htmlFor="tags" className="block text-sm font-medium text-gray-700 mb-2">
                    Теги (через запятую)
                  </label>
                  <input
                    type="text"
                    id="tags"
                    name="tags"
                    value={formData.tags}
                    onChange={handleInputChange}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                    placeholder="тег1, тег2, тег3"
                  />
                </div>
              </div>

              {/* SEO */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">SEO</h3>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="metaTitle" className="block text-sm font-medium text-gray-700 mb-2">
                      Meta Title
                    </label>
                    <input
                      type="text"
                      id="metaTitle"
                      name="metaTitle"
                      value={formData.metaTitle}
                      onChange={handleInputChange}
                      className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                      placeholder="Заголовок для поисковых систем"
                    />
                  </div>
                  <div>
                    <label htmlFor="metaDescription" className="block text-sm font-medium text-gray-700 mb-2">
                      Meta Description
                    </label>
                    <textarea
                      id="metaDescription"
                      name="metaDescription"
                      rows={3}
                      value={formData.metaDescription}
                      onChange={handleInputChange}
                      className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                      placeholder="Описание для поисковых систем"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Hidden submit button */}
          <button
            type="submit"
            id="submit-form"
            className="hidden"
            disabled={isLoading}
          >
            Submit
          </button>
        </form>
      </div>
    </AdminLayout>
  );
};

export default EditBlogPostPage;
