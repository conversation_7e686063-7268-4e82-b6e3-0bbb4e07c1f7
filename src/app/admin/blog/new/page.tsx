'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import AdminLayout from '@/components/admin/AdminLayout';
import MediaGallery from '@/components/admin/MediaGallery';
import RichTextEditor from '@/components/admin/RichTextEditor';
import { createBlogPost } from '@/lib/cms-utils';
import { BlogPost } from '@/types/cms';

const NewBlogPostPage: React.FC = () => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [showMediaGallery, setShowMediaGallery] = useState(false);
  const [mediaGalleryMode, setMediaGalleryMode] = useState<'featured' | 'content'>('featured');
  const [pendingImages, setPendingImages] = useState<string[]>([]);
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    excerpt: '',
    content: '',
    status: 'draft' as 'draft' | 'published',
    tags: '',
    featuredImage: '',
    contentImages: [] as string[],
    metaTitle: '',
    metaDescription: '',
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Auto-generate slug from title
    if (name === 'title' && !formData.slug) {
      const slug = value
        .toLowerCase()
        .replace(/[^a-z0-9а-я]/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '');
      setFormData(prev => ({
        ...prev,
        slug
      }));
    }
  };

  const handleFeaturedImageSelect = (imageUrl: string) => {
    setFormData(prev => ({
      ...prev,
      featuredImage: imageUrl
    }));
    setShowMediaGallery(false);
  };

  const handleContentImagesSelect = (imageUrls: string[]) => {
    if (mediaGalleryMode === 'content') {
      // Сохраняем изображения для вставки
      setPendingImages(imageUrls);

      // Пытаемся вставить через глобальную функцию
      imageUrls.forEach(imageUrl => {
        if (window.insertImageIntoEditor) {
          window.insertImageIntoEditor(imageUrl);
        } else {
          // Fallback: добавляем в конец контента
          const imageMarkdown = `![Изображение](${imageUrl})`;
          setFormData(prev => ({
            ...prev,
            content: prev.content + (prev.content ? '\n\n' : '') + imageMarkdown
          }));
        }
      });

      // Очищаем pending изображения
      setTimeout(() => setPendingImages([]), 100);
    } else {
      // Legacy behavior for contentImages array
      setFormData(prev => ({
        ...prev,
        contentImages: imageUrls
      }));
    }
    setShowMediaGallery(false);
  };

  const openMediaGallery = (mode: 'featured' | 'content') => {
    setMediaGalleryMode(mode);
    setShowMediaGallery(true);
  };

  const removeContentImage = (imageUrl: string) => {
    setFormData(prev => ({
      ...prev,
      contentImages: prev.contentImages.filter(url => url !== imageUrl)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const postData: Omit<BlogPost, 'id' | 'createdAt' | 'updatedAt'> = {
        title: formData.title,
        slug: formData.slug,
        excerpt: formData.excerpt,
        content: formData.content,
        status: formData.status,
        featuredImage: formData.featuredImage || undefined,
        contentImages: formData.contentImages.length > 0 ? formData.contentImages : undefined,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean),
        type: 'blog',
        category: 'general',
        author: 'Admin',
        seo: {
          metaTitle: formData.metaTitle || formData.title,
          metaDescription: formData.metaDescription || formData.excerpt,
          keywords: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean),
        },
      };

      const newPost = createBlogPost(postData);
      router.push(`/admin/blog/${newPost.id}`);
    } catch (error) {
      console.error('Ошибка при создании статьи:', error);
      alert('Ошибка при создании статьи');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveDraft = () => {
    setFormData(prev => ({ ...prev, status: 'draft' }));
    setTimeout(() => {
      document.getElementById('submit-form')?.click();
    }, 0);
  };

  const handlePublish = () => {
    setFormData(prev => ({ ...prev, status: 'published' }));
    setTimeout(() => {
      document.getElementById('submit-form')?.click();
    }, 0);
  };

  return (
    <AdminLayout>
      <div className="p-6 min-h-screen bg-gray-50">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Новая статья</h1>
              <p className="mt-2 text-gray-600">
                Создание новой статьи для блога
              </p>
            </div>
            <div className="flex space-x-3">
              <button
                type="button"
                onClick={handleSaveDraft}
                disabled={isLoading}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
              >
                Сохранить черновик
              </button>
              <button
                type="button"
                onClick={handlePublish}
                disabled={isLoading}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
              >
                Опубликовать
              </button>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Title */}
              <div className="bg-white rounded-lg shadow p-6">
                <div>
                  <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                    Заголовок статьи *
                  </label>
                  <input
                    type="text"
                    id="title"
                    name="title"
                    required
                    value={formData.title}
                    onChange={handleInputChange}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Введите заголовок статьи"
                  />
                </div>
              </div>

              {/* Slug */}
              <div className="bg-white rounded-lg shadow p-6">
                <div>
                  <label htmlFor="slug" className="block text-sm font-medium text-gray-700 mb-2">
                    URL (slug) *
                  </label>
                  <input
                    type="text"
                    id="slug"
                    name="slug"
                    required
                    value={formData.slug}
                    onChange={handleInputChange}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                    placeholder="url-статьи"
                  />
                  <div className="mt-2 flex items-center justify-between">
                    <p className="text-sm text-gray-500">
                      URL статьи: /blog/{formData.slug || 'url-статьи'}
                    </p>
                    {formData.status === 'published' && formData.slug && (
                      <a
                        href={`/blog/${formData.slug}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                        Посмотреть статью
                      </a>
                    )}
                  </div>
                </div>
              </div>

              {/* Excerpt */}
              <div className="bg-white rounded-lg shadow p-6">
                <div>
                  <label htmlFor="excerpt" className="block text-sm font-medium text-gray-700 mb-2">
                    Краткое описание *
                  </label>
                  <textarea
                    id="excerpt"
                    name="excerpt"
                    required
                    rows={3}
                    value={formData.excerpt}
                    onChange={handleInputChange}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Краткое описание статьи для превью"
                  />
                </div>
              </div>

              {/* Featured Image */}
              <div className="bg-white rounded-lg shadow p-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Изображение превью
                  </label>
                  <div className="space-y-4">
                    {formData.featuredImage ? (
                      <div className="relative inline-block">
                        <img
                          src={formData.featuredImage}
                          alt="Featured"
                          className="w-32 h-32 object-cover rounded-lg border border-gray-300"
                        />
                        <button
                          type="button"
                          onClick={() => setFormData(prev => ({ ...prev, featuredImage: '' }))}
                          className="absolute -top-2 -right-2 bg-red-600 text-white rounded-full p-1 hover:bg-red-700"
                        >
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                        </button>
                      </div>
                    ) : (
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                        <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                          <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                        </svg>
                        <p className="mt-2 text-sm text-gray-600">Изображение не выбрано</p>
                      </div>
                    )}
                    <button
                      type="button"
                      onClick={() => openMediaGallery('featured')}
                      className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      Выбрать изображение
                    </button>
                  </div>
                </div>
              </div>


              {/* Content */}
              <div className="bg-white rounded-lg shadow p-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-4">
                    Содержание статьи *
                  </label>
                  <RichTextEditor
                    value={formData.content}
                    onChange={(content) => setFormData(prev => ({ ...prev, content }))}
                    onImageInsert={() => openMediaGallery('content')}
                    placeholder="Начните писать содержание статьи..."
                    className="min-h-[500px]"
                  />
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Status */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Публикация</h3>
                <div>
                  <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
                    Статус
                  </label>
                  <select
                    id="status"
                    name="status"
                    value={formData.status}
                    onChange={handleInputChange}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="draft">Черновик</option>
                    <option value="published">Опубликовано</option>
                  </select>
                </div>
              </div>

              {/* Tags */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Теги</h3>
                <div>
                  <label htmlFor="tags" className="block text-sm font-medium text-gray-700 mb-2">
                    Теги (через запятую)
                  </label>
                  <input
                    type="text"
                    id="tags"
                    name="tags"
                    value={formData.tags}
                    onChange={handleInputChange}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                    placeholder="тег1, тег2, тег3"
                  />
                </div>
              </div>

              {/* SEO */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">SEO</h3>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="metaTitle" className="block text-sm font-medium text-gray-700 mb-2">
                      Meta Title
                    </label>
                    <input
                      type="text"
                      id="metaTitle"
                      name="metaTitle"
                      value={formData.metaTitle}
                      onChange={handleInputChange}
                      className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                      placeholder="Заголовок для поисковых систем"
                    />
                  </div>
                  <div>
                    <label htmlFor="metaDescription" className="block text-sm font-medium text-gray-700 mb-2">
                      Meta Description
                    </label>
                    <textarea
                      id="metaDescription"
                      name="metaDescription"
                      rows={3}
                      value={formData.metaDescription}
                      onChange={handleInputChange}
                      className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                      placeholder="Описание для поисковых систем"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Hidden submit button */}
          <button
            type="submit"
            id="submit-form"
            className="hidden"
            disabled={isLoading}
          >
            Submit
          </button>
        </form>
      </div>

      {/* Media Gallery Modal */}
      <MediaGallery
        isOpen={showMediaGallery}
        onClose={() => setShowMediaGallery(false)}
        onSelect={mediaGalleryMode === 'featured' ? handleFeaturedImageSelect : (imageUrl: string) => handleContentImagesSelect([imageUrl])}
        onSelectMultiple={mediaGalleryMode === 'content' ? handleContentImagesSelect : undefined}
        multiple={mediaGalleryMode === 'content'}
        selectedImage={mediaGalleryMode === 'featured' ? formData.featuredImage : undefined}
        mode={mediaGalleryMode}
      />
    </AdminLayout>
  );
};

export default NewBlogPostPage;
