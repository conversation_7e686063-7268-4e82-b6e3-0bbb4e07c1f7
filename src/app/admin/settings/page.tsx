'use client';

import React, { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import Toast from '@/components/ui/Toast';
import { getSettings, saveSettings } from '@/lib/cms-utils';

const SettingsPage: React.FC = () => {
  const [settings, setSettings] = useState({
    siteName: '',
    siteDescription: '',
    siteUrl: '',
    contactEmail: '',
    blogEnabled: true,
    commentsEnabled: false,
    postsPerPage: 10,
    metaTitle: '',
    metaDescription: '',
    googleAnalytics: '',
    yandexMetrica: '',
    blogHeroTitle: '',
    blogHeroDescription: '',
    blogHeroBackgroundColor: '',
    // SEO настройки для блога
    blogMetaTitle: '',
    blogMetaDescription: '',
    blogKeywords: '',
    blogOgTitle: '',
    blogOgDescription: '',
  });

  const [isLoading, setIsLoading] = useState(false);
  const [toast, setToast] = useState<{
    message: string;
    type: 'success' | 'error' | 'info';
  } | null>(null);

  useEffect(() => {
    // Load settings on component mount
    const savedSettings = getSettings();
    setSettings(prev => ({
      ...prev,
      ...savedSettings
    }));
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const success = saveSettings(settings);
      if (success) {
        setToast({
          message: 'Настройки успешно сохранены!',
          type: 'success'
        });
      } else {
        throw new Error('Не удалось сохранить настройки');
      }
    } catch (error) {
      console.error('Ошибка при сохранении настроек:', error);
      setToast({
        message: 'Ошибка при сохранении настроек',
        type: 'error'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AdminLayout>
      <div className="p-6 min-h-screen bg-gray-50">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Настройки сайта</h1>
          <p className="mt-2 text-gray-600">
            Общие настройки и конфигурация сайта
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* General Settings */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Основные настройки</h3>
            </div>
            <div className="p-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="siteName" className="block text-sm font-medium text-gray-700 mb-2">
                    Название сайта
                  </label>
                  <input
                    type="text"
                    id="siteName"
                    name="siteName"
                    value={settings.siteName}
                    onChange={handleInputChange}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
                <div>
                  <label htmlFor="siteUrl" className="block text-sm font-medium text-gray-700 mb-2">
                    URL сайта
                  </label>
                  <input
                    type="url"
                    id="siteUrl"
                    name="siteUrl"
                    value={settings.siteUrl}
                    onChange={handleInputChange}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
              </div>
              
              <div>
                <label htmlFor="siteDescription" className="block text-sm font-medium text-gray-700 mb-2">
                  Описание сайта
                </label>
                <textarea
                  id="siteDescription"
                  name="siteDescription"
                  rows={3}
                  value={settings.siteDescription}
                  onChange={handleInputChange}
                  className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                />
              </div>

              <div>
                <label htmlFor="contactEmail" className="block text-sm font-medium text-gray-700 mb-2">
                  Контактный email
                </label>
                <input
                  type="email"
                  id="contactEmail"
                  name="contactEmail"
                  value={settings.contactEmail}
                  onChange={handleInputChange}
                  className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
            </div>
          </div>

          {/* Blog Settings */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Настройки блога</h3>
            </div>
            <div className="p-6 space-y-6">
              <div className="flex items-center">
                <input
                  id="blogEnabled"
                  name="blogEnabled"
                  type="checkbox"
                  checked={settings.blogEnabled}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label htmlFor="blogEnabled" className="ml-2 block text-sm text-gray-900">
                  Включить блог
                </label>
              </div>

              <div className="flex items-center">
                <input
                  id="commentsEnabled"
                  name="commentsEnabled"
                  type="checkbox"
                  checked={settings.commentsEnabled}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label htmlFor="commentsEnabled" className="ml-2 block text-sm text-gray-900">
                  Включить комментарии
                </label>
              </div>

              <div>
                <label htmlFor="postsPerPage" className="block text-sm font-medium text-gray-700 mb-2">
                  Количество статей на странице
                </label>
                <input
                  type="number"
                  id="postsPerPage"
                  name="postsPerPage"
                  min="1"
                  max="50"
                  value={settings.postsPerPage}
                  onChange={handleInputChange}
                  className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                />
              </div>

              {/* Hero Section Settings */}
              <div className="border-t border-gray-200 pt-6">
                <h4 className="text-md font-medium text-gray-900 mb-4">Настройки Hero блока</h4>

                <div className="space-y-4">
                  <div>
                    <label htmlFor="blogHeroTitle" className="block text-sm font-medium text-gray-700 mb-2">
                      Заголовок Hero блока
                    </label>
                    <input
                      type="text"
                      id="blogHeroTitle"
                      name="blogHeroTitle"
                      value={settings.blogHeroTitle}
                      onChange={handleInputChange}
                      placeholder="Блог Setmee"
                      className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>

                  <div>
                    <label htmlFor="blogHeroDescription" className="block text-sm font-medium text-gray-700 mb-2">
                      Описание Hero блока
                    </label>
                    <textarea
                      id="blogHeroDescription"
                      name="blogHeroDescription"
                      rows={3}
                      value={settings.blogHeroDescription}
                      onChange={handleInputChange}
                      placeholder="Полезные статьи о Kommo CRM, интеграциях, автоматизации бизнеса и лучших практиках"
                      className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>

                  <div>
                    <label htmlFor="blogHeroBackgroundColor" className="block text-sm font-medium text-gray-700 mb-2">
                      Цвет фона Hero блока
                    </label>
                    <div className="flex items-center space-x-3">
                      <input
                        type="color"
                        id="blogHeroBackgroundColor"
                        name="blogHeroBackgroundColor"
                        value={settings.blogHeroBackgroundColor}
                        onChange={handleInputChange}
                        className="h-10 w-20 border border-gray-300 rounded-md cursor-pointer"
                      />
                      <input
                        type="text"
                        value={settings.blogHeroBackgroundColor}
                        onChange={handleInputChange}
                        name="blogHeroBackgroundColor"
                        placeholder="#1e40af"
                        className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                      />
                    </div>
                    <p className="mt-1 text-sm text-gray-500">
                      Выберите цвет или введите HEX код (например: #1e40af)
                    </p>
                  </div>
                </div>
              </div>

              {/* SEO Settings for Blog */}
              <div className="border-t border-gray-200 pt-6">
                <h4 className="text-md font-medium text-gray-900 mb-4">SEO настройки блога</h4>

                <div className="space-y-4">
                  <div>
                    <label htmlFor="blogMetaTitle" className="block text-sm font-medium text-gray-700 mb-2">
                      Meta Title блога
                    </label>
                    <input
                      type="text"
                      id="blogMetaTitle"
                      name="blogMetaTitle"
                      value={settings.blogMetaTitle}
                      onChange={handleInputChange}
                      placeholder="SetMee Blog - Kommo CRM & Business Automation Articles"
                      className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                    />
                    <p className="mt-1 text-sm text-gray-500">
                      Заголовок страницы блога для поисковых систем (рекомендуется до 60 символов)
                    </p>
                  </div>

                  <div>
                    <label htmlFor="blogMetaDescription" className="block text-sm font-medium text-gray-700 mb-2">
                      Meta Description блога
                    </label>
                    <textarea
                      id="blogMetaDescription"
                      name="blogMetaDescription"
                      rows={3}
                      value={settings.blogMetaDescription}
                      onChange={handleInputChange}
                      placeholder="Expert articles about Kommo CRM, integrations, business automation and best practices. Professional guides and tips."
                      className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                    />
                    <p className="mt-1 text-sm text-gray-500">
                      Описание страницы блога для поисковых систем (рекомендуется до 160 символов)
                    </p>
                  </div>

                  <div>
                    <label htmlFor="blogKeywords" className="block text-sm font-medium text-gray-700 mb-2">
                      Ключевые слова блога
                    </label>
                    <input
                      type="text"
                      id="blogKeywords"
                      name="blogKeywords"
                      value={settings.blogKeywords}
                      onChange={handleInputChange}
                      placeholder="Kommo CRM, business automation, integrations, CRM system, sales, marketing"
                      className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                    />
                    <p className="mt-1 text-sm text-gray-500">
                      Ключевые слова через запятую для SEO оптимизации блога
                    </p>
                  </div>

                  <div>
                    <label htmlFor="blogOgTitle" className="block text-sm font-medium text-gray-700 mb-2">
                      Open Graph заголовок
                    </label>
                    <input
                      type="text"
                      id="blogOgTitle"
                      name="blogOgTitle"
                      value={settings.blogOgTitle}
                      onChange={handleInputChange}
                      placeholder="SetMee Blog - Kommo CRM & Business Automation Articles"
                      className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                    />
                    <p className="mt-1 text-sm text-gray-500">
                      Заголовок для социальных сетей (Facebook, LinkedIn)
                    </p>
                  </div>

                  <div>
                    <label htmlFor="blogOgDescription" className="block text-sm font-medium text-gray-700 mb-2">
                      Open Graph описание
                    </label>
                    <textarea
                      id="blogOgDescription"
                      name="blogOgDescription"
                      rows={2}
                      value={settings.blogOgDescription}
                      onChange={handleInputChange}
                      placeholder="Expert articles about Kommo CRM, integrations, business automation and best practices."
                      className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                    />
                    <p className="mt-1 text-sm text-gray-500">
                      Описание для социальных сетей (Facebook, LinkedIn)
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* SEO Settings */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">SEO настройки</h3>
            </div>
            <div className="p-6 space-y-6">
              <div>
                <label htmlFor="metaTitle" className="block text-sm font-medium text-gray-700 mb-2">
                  Meta Title (по умолчанию)
                </label>
                <input
                  type="text"
                  id="metaTitle"
                  name="metaTitle"
                  value={settings.metaTitle}
                  onChange={handleInputChange}
                  className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                />
              </div>

              <div>
                <label htmlFor="metaDescription" className="block text-sm font-medium text-gray-700 mb-2">
                  Meta Description (по умолчанию)
                </label>
                <textarea
                  id="metaDescription"
                  name="metaDescription"
                  rows={3}
                  value={settings.metaDescription}
                  onChange={handleInputChange}
                  className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
            </div>
          </div>

          {/* Analytics Settings */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Аналитика</h3>
            </div>
            <div className="p-6 space-y-6">
              <div>
                <label htmlFor="googleAnalytics" className="block text-sm font-medium text-gray-700 mb-2">
                  Google Analytics ID
                </label>
                <input
                  type="text"
                  id="googleAnalytics"
                  name="googleAnalytics"
                  placeholder="G-XXXXXXXXXX"
                  value={settings.googleAnalytics}
                  onChange={handleInputChange}
                  className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                />
              </div>

              <div>
                <label htmlFor="yandexMetrica" className="block text-sm font-medium text-gray-700 mb-2">
                  Яндекс.Метрика ID
                </label>
                <input
                  type="text"
                  id="yandexMetrica"
                  name="yandexMetrica"
                  placeholder="12345678"
                  value={settings.yandexMetrica}
                  onChange={handleInputChange}
                  className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isLoading}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Сохранение...
                </>
              ) : (
                'Сохранить настройки'
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Toast Notification */}
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
    </AdminLayout>
  );
};

export default SettingsPage;
