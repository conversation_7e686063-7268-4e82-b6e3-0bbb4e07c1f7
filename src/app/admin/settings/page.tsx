'use client';

import React, { useState } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';

const SettingsPage: React.FC = () => {
  const [settings, setSettings] = useState({
    siteName: 'Setmee',
    siteDescription: 'Профессиональная интеграция Kommo CRM',
    siteUrl: 'https://setmee.ru',
    contactEmail: '<EMAIL>',
    blogEnabled: true,
    commentsEnabled: false,
    postsPerPage: 10,
    metaTitle: 'Setmee - Kommo Partner',
    metaDescription: 'Профессиональная интеграция и настройка Kommo CRM для вашего бизнеса',
    googleAnalytics: '',
    yandexMetrica: '',
  });

  const [isLoading, setIsLoading] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Здесь будет логика сохранения настроек
      await new Promise(resolve => setTimeout(resolve, 1000));
      alert('Настройки успешно сохранены!');
    } catch (error) {
      console.error('Ошибка при сохранении настроек:', error);
      alert('Ошибка при сохранении настроек');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AdminLayout>
      <div className="p-6 min-h-screen bg-gray-50">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Настройки сайта</h1>
          <p className="mt-2 text-gray-600">
            Общие настройки и конфигурация сайта
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* General Settings */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Основные настройки</h3>
            </div>
            <div className="p-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="siteName" className="block text-sm font-medium text-gray-700 mb-2">
                    Название сайта
                  </label>
                  <input
                    type="text"
                    id="siteName"
                    name="siteName"
                    value={settings.siteName}
                    onChange={handleInputChange}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
                <div>
                  <label htmlFor="siteUrl" className="block text-sm font-medium text-gray-700 mb-2">
                    URL сайта
                  </label>
                  <input
                    type="url"
                    id="siteUrl"
                    name="siteUrl"
                    value={settings.siteUrl}
                    onChange={handleInputChange}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
              </div>
              
              <div>
                <label htmlFor="siteDescription" className="block text-sm font-medium text-gray-700 mb-2">
                  Описание сайта
                </label>
                <textarea
                  id="siteDescription"
                  name="siteDescription"
                  rows={3}
                  value={settings.siteDescription}
                  onChange={handleInputChange}
                  className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                />
              </div>

              <div>
                <label htmlFor="contactEmail" className="block text-sm font-medium text-gray-700 mb-2">
                  Контактный email
                </label>
                <input
                  type="email"
                  id="contactEmail"
                  name="contactEmail"
                  value={settings.contactEmail}
                  onChange={handleInputChange}
                  className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
            </div>
          </div>

          {/* Blog Settings */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Настройки блога</h3>
            </div>
            <div className="p-6 space-y-6">
              <div className="flex items-center">
                <input
                  id="blogEnabled"
                  name="blogEnabled"
                  type="checkbox"
                  checked={settings.blogEnabled}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label htmlFor="blogEnabled" className="ml-2 block text-sm text-gray-900">
                  Включить блог
                </label>
              </div>

              <div className="flex items-center">
                <input
                  id="commentsEnabled"
                  name="commentsEnabled"
                  type="checkbox"
                  checked={settings.commentsEnabled}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label htmlFor="commentsEnabled" className="ml-2 block text-sm text-gray-900">
                  Включить комментарии
                </label>
              </div>

              <div>
                <label htmlFor="postsPerPage" className="block text-sm font-medium text-gray-700 mb-2">
                  Количество статей на странице
                </label>
                <input
                  type="number"
                  id="postsPerPage"
                  name="postsPerPage"
                  min="1"
                  max="50"
                  value={settings.postsPerPage}
                  onChange={handleInputChange}
                  className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
            </div>
          </div>

          {/* SEO Settings */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">SEO настройки</h3>
            </div>
            <div className="p-6 space-y-6">
              <div>
                <label htmlFor="metaTitle" className="block text-sm font-medium text-gray-700 mb-2">
                  Meta Title (по умолчанию)
                </label>
                <input
                  type="text"
                  id="metaTitle"
                  name="metaTitle"
                  value={settings.metaTitle}
                  onChange={handleInputChange}
                  className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                />
              </div>

              <div>
                <label htmlFor="metaDescription" className="block text-sm font-medium text-gray-700 mb-2">
                  Meta Description (по умолчанию)
                </label>
                <textarea
                  id="metaDescription"
                  name="metaDescription"
                  rows={3}
                  value={settings.metaDescription}
                  onChange={handleInputChange}
                  className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
            </div>
          </div>

          {/* Analytics Settings */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Аналитика</h3>
            </div>
            <div className="p-6 space-y-6">
              <div>
                <label htmlFor="googleAnalytics" className="block text-sm font-medium text-gray-700 mb-2">
                  Google Analytics ID
                </label>
                <input
                  type="text"
                  id="googleAnalytics"
                  name="googleAnalytics"
                  placeholder="G-XXXXXXXXXX"
                  value={settings.googleAnalytics}
                  onChange={handleInputChange}
                  className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                />
              </div>

              <div>
                <label htmlFor="yandexMetrica" className="block text-sm font-medium text-gray-700 mb-2">
                  Яндекс.Метрика ID
                </label>
                <input
                  type="text"
                  id="yandexMetrica"
                  name="yandexMetrica"
                  placeholder="12345678"
                  value={settings.yandexMetrica}
                  onChange={handleInputChange}
                  className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isLoading}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Сохранение...
                </>
              ) : (
                'Сохранить настройки'
              )}
            </button>
          </div>
        </form>
      </div>
    </AdminLayout>
  );
};

export default SettingsPage;
