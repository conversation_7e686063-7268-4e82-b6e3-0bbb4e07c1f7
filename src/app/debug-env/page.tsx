'use client';

import React from 'react';

const DebugEnvPage: React.FC = () => {
  const envVars = {
    NEXT_PUBLIC_MAKE_CONTACT_WEBHOOK: process.env.NEXT_PUBLIC_MAKE_CONTACT_WEBHOOK,
    NEXT_PUBLIC_MAKE_NEWSLETTER_WEBHOOK: process.env.NEXT_PUBLIC_MAKE_NEWSLETTER_WEBHOOK,
    NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL,
    NODE_ENV: process.env.NODE_ENV,
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold mb-8">Environment Variables Debug</h1>
        
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Current Environment Variables:</h2>
          
          <div className="space-y-3">
            {Object.entries(envVars).map(([key, value]) => (
              <div key={key} className="border-b pb-2">
                <div className="font-mono text-sm text-gray-600">{key}:</div>
                <div className="font-mono text-sm bg-gray-100 p-2 rounded">
                  {value || '❌ NOT SET'}
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded">
            <h3 className="font-semibold text-yellow-800">Troubleshooting:</h3>
            <ul className="mt-2 text-sm text-yellow-700 space-y-1">
              <li>• Убедитесь, что файл .env.local существует в корне проекта</li>
              <li>• Перезапустите сервер после изменения .env.local</li>
              <li>• Переменные должны начинаться с NEXT_PUBLIC_ для клиентского кода</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DebugEnvPage;
