'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { 
  submitContactForm, 
  submitEmailSubscription, 
  submitDemoRequest, 
  submitAuditRequest, 
  submitConsultationRequest 
} from '@/lib/form-service';
import { 
  contactFormSchema, 
  emailSubscriptionSchema, 
  demoRequestSchema, 
  auditRequestSchema, 
  consultationRequestSchema,
  ContactFormData,
  EmailSubscriptionData,
  DemoRequestData,
  AuditRequestData,
  ConsultationRequestData
} from '@/lib/validations';
import { Container, But<PERSON>, Card } from '@/components/ui';

const TestFormsPage: React.FC = () => {
  const [results, setResults] = useState<Record<string, { success: boolean; message: string }>>({});

  // Contact Form
  const contactForm = useForm<ContactFormData>({
    resolver: zodResolver(contactFormSchema),
  });

  const onContactSubmit = async (data: ContactFormData) => {
    const result = await submitContactForm(data);
    setResults(prev => ({ ...prev, contact: result }));
    if (result.success) contactForm.reset();
  };

  // Newsletter Form
  const newsletterForm = useForm<EmailSubscriptionData>({
    resolver: zodResolver(emailSubscriptionSchema),
  });

  const onNewsletterSubmit = async (data: EmailSubscriptionData) => {
    const result = await submitEmailSubscription(data);
    setResults(prev => ({ ...prev, newsletter: result }));
    if (result.success) newsletterForm.reset();
  };

  // Demo Form
  const demoForm = useForm<DemoRequestData>({
    resolver: zodResolver(demoRequestSchema),
  });

  const onDemoSubmit = async (data: DemoRequestData) => {
    const result = await submitDemoRequest(data);
    setResults(prev => ({ ...prev, demo: result }));
    if (result.success) demoForm.reset();
  };

  // Audit Form
  const auditForm = useForm<AuditRequestData>({
    resolver: zodResolver(auditRequestSchema),
  });

  const onAuditSubmit = async (data: AuditRequestData) => {
    const result = await submitAuditRequest(data);
    setResults(prev => ({ ...prev, audit: result }));
    if (result.success) auditForm.reset();
  };

  // Consultation Form
  const consultationForm = useForm<ConsultationRequestData>({
    resolver: zodResolver(consultationRequestSchema),
  });

  const onConsultationSubmit = async (data: ConsultationRequestData) => {
    const result = await submitConsultationRequest(data);
    setResults(prev => ({ ...prev, consultation: result }));
    if (result.success) consultationForm.reset();
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <Container>
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-center mb-8">Тестирование форм</h1>
          
          <div className="grid md:grid-cols-2 gap-8">
            {/* Contact Form */}
            <Card padding="lg">
              <h2 className="text-xl font-semibold mb-4">Contact Form</h2>
              <form onSubmit={contactForm.handleSubmit(onContactSubmit)} className="space-y-4">
                <input
                  {...contactForm.register('name')}
                  placeholder="Name"
                  className="w-full p-3 border rounded-lg"
                />
                <input
                  {...contactForm.register('email')}
                  placeholder="Email"
                  type="email"
                  className="w-full p-3 border rounded-lg"
                />
                <input
                  {...contactForm.register('phone')}
                  placeholder="Phone (optional)"
                  className="w-full p-3 border rounded-lg"
                />
                <input
                  {...contactForm.register('company')}
                  placeholder="Company (optional)"
                  className="w-full p-3 border rounded-lg"
                />
                <textarea
                  {...contactForm.register('message')}
                  placeholder="Message"
                  rows={3}
                  className="w-full p-3 border rounded-lg"
                />
                <Button type="submit" variant="primary" className="w-full">
                  Submit Contact
                </Button>
              </form>
              {results.contact && (
                <div className={`mt-4 p-3 rounded ${results.contact.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                  {results.contact.message}
                </div>
              )}
            </Card>

            {/* Newsletter Form */}
            <Card padding="lg">
              <h2 className="text-xl font-semibold mb-4">Newsletter Form</h2>
              <form onSubmit={newsletterForm.handleSubmit(onNewsletterSubmit)} className="space-y-4">
                <input
                  {...newsletterForm.register('email')}
                  placeholder="Email"
                  type="email"
                  className="w-full p-3 border rounded-lg"
                />
                <Button type="submit" variant="primary" className="w-full">
                  Subscribe
                </Button>
              </form>
              {results.newsletter && (
                <div className={`mt-4 p-3 rounded ${results.newsletter.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                  {results.newsletter.message}
                </div>
              )}
            </Card>

            {/* Demo Form */}
            <Card padding="lg">
              <h2 className="text-xl font-semibold mb-4">Demo Request</h2>
              <form onSubmit={demoForm.handleSubmit(onDemoSubmit)} className="space-y-4">
                <input
                  {...demoForm.register('name')}
                  placeholder="Name"
                  className="w-full p-3 border rounded-lg"
                />
                <input
                  {...demoForm.register('email')}
                  placeholder="Email"
                  type="email"
                  className="w-full p-3 border rounded-lg"
                />
                <input
                  {...demoForm.register('company')}
                  placeholder="Company (optional)"
                  className="w-full p-3 border rounded-lg"
                />
                <input
                  {...demoForm.register('phone')}
                  placeholder="Phone (optional)"
                  className="w-full p-3 border rounded-lg"
                />
                <Button type="submit" variant="primary" className="w-full">
                  Request Demo
                </Button>
              </form>
              {results.demo && (
                <div className={`mt-4 p-3 rounded ${results.demo.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                  {results.demo.message}
                </div>
              )}
            </Card>

            {/* Audit Form */}
            <Card padding="lg">
              <h2 className="text-xl font-semibold mb-4">Audit Request</h2>
              <form onSubmit={auditForm.handleSubmit(onAuditSubmit)} className="space-y-4">
                <input
                  {...auditForm.register('name')}
                  placeholder="Name"
                  className="w-full p-3 border rounded-lg"
                />
                <input
                  {...auditForm.register('email')}
                  placeholder="Email"
                  type="email"
                  className="w-full p-3 border rounded-lg"
                />
                <input
                  {...auditForm.register('website')}
                  placeholder="Website URL (optional)"
                  className="w-full p-3 border rounded-lg"
                />
                <input
                  {...auditForm.register('company')}
                  placeholder="Company (optional)"
                  className="w-full p-3 border rounded-lg"
                />
                <input
                  {...auditForm.register('currentCrm')}
                  placeholder="Current CRM (optional)"
                  className="w-full p-3 border rounded-lg"
                />
                <Button type="submit" variant="primary" className="w-full">
                  Request Audit
                </Button>
              </form>
              {results.audit && (
                <div className={`mt-4 p-3 rounded ${results.audit.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                  {results.audit.message}
                </div>
              )}
            </Card>

            {/* Consultation Form */}
            <Card padding="lg">
              <h2 className="text-xl font-semibold mb-4">Consultation Request</h2>
              <form onSubmit={consultationForm.handleSubmit(onConsultationSubmit)} className="space-y-4">
                <input
                  {...consultationForm.register('name')}
                  placeholder="Name"
                  className="w-full p-3 border rounded-lg"
                />
                <input
                  {...consultationForm.register('email')}
                  placeholder="Email"
                  type="email"
                  className="w-full p-3 border rounded-lg"
                />
                <input
                  {...consultationForm.register('phone')}
                  placeholder="Phone (optional)"
                  className="w-full p-3 border rounded-lg"
                />
                <input
                  {...consultationForm.register('company')}
                  placeholder="Company (optional)"
                  className="w-full p-3 border rounded-lg"
                />
                <textarea
                  {...consultationForm.register('challenge')}
                  placeholder="Describe your challenge (optional)"
                  rows={3}
                  className="w-full p-3 border rounded-lg"
                />
                <Button type="submit" variant="primary" className="w-full">
                  Request Consultation
                </Button>
              </form>
              {results.consultation && (
                <div className={`mt-4 p-3 rounded ${results.consultation.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                  {results.consultation.message}
                </div>
              )}
            </Card>
          </div>

          {/* Instructions */}
          <Card padding="lg" className="mt-8">
            <h2 className="text-xl font-semibold mb-4">Инструкции по тестированию</h2>
            <div className="space-y-2 text-sm text-gray-600">
              <p>1. Откройте DevTools → Console для просмотра логов</p>
              <p>2. Если webhook'и не настроены, данные будут логироваться в консоль</p>
              <p>3. Для настройки webhook'ов добавьте URL'ы в .env.local</p>
              <p>4. Проверьте Network tab для отслеживания HTTP запросов</p>
            </div>
          </Card>
        </div>
      </Container>
    </div>
  );
};

export default TestFormsPage;
