import type { <PERSON>ada<PERSON>, Viewport } from "next";
import "./globals.css";
import { <PERSON><PERSON>, Footer } from "@/components/layout";

export const metadata: Metadata = {
  title: "Kommo Partner: Certified Professional in Kommo CRM Integration | Setmee",
  description: "Smart solutions for your business based on Kommo CRM. We help with Kommo implementation, optimization, training and custom integrations. 8+ years experience, 320+ projects completed.",
  keywords: [
    "Kommo CRM",
    "CRM implementation",
    "Kommo partner",
    "CRM integration",
    "sales automation",
    "business process automation",
    "Kommo training",
    "CRM optimization",
    "custom integrations",
    "sales funnel setup"
  ],
  authors: [{ name: "Setmee Team" }],
  creator: "<PERSON><PERSON>",
  publisher: "<PERSON><PERSON>",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://setmee.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: "Kommo Partner: Certified Professional in Kommo CRM Integration | Setmee",
    description: "Smart solutions for your business based on Kommo CRM. We help with Kommo implementation, optimization, training and custom integrations.",
    url: 'https://setmee.com',
    siteName: 'Setmee',
    images: [
      {
        url: '/images/setmee-og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Setmee - Kommo CRM Partner',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "Kommo Partner: Certified Professional in Kommo CRM Integration | Setmee",
    description: "Smart solutions for your business based on Kommo CRM. We help with Kommo implementation, optimization, training and custom integrations.",
    images: ['/images/setmee-og-image.jpg'],
    creator: '@setmee',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="antialiased">
        <Header />
        <main className="min-h-screen">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  );
}
